# Ubuzima Application Environment Configuration
# Copy this file to .env and update with your actual values

# Database Configuration
DATABASE_URL=*******************************************
DATABASE_USERNAME=ubuzima_user
DATABASE_PASSWORD=ubuzima_password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-minimum-256-bits
JWT_EXPIRATION=********
JWT_REFRESH_EXPIRATION=*********

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,https://yourdomain.com

# File Upload Configuration
FILE_UPLOAD_DIR=./uploads
FILE_MAX_SIZE=********

# Email Configuration (for notifications)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# SMS Configuration (Twilio)
SMS_PROVIDER=twilio
SMS_API_KEY=your-twilio-account-sid
SMS_API_SECRET=your-twilio-auth-token
SMS_FROM_NUMBER=+**********

# Firebase Configuration (for push notifications)
FIREBASE_SERVICE_ACCOUNT_KEY=path/to/firebase-service-account.json

# AI Configuration
GEMINI_API_KEY=your-gemini-api-key-here
OPENAI_API_KEY=your-openai-api-key-here

# Encryption Configuration
ENCRYPTION_KEY=your-encryption-key-32-characters

# Redis Configuration (for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Monitoring Configuration
SENTRY_DSN=your-sentry-dsn-for-error-tracking

# Google Maps Configuration
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Social Media Integration
FACEBOOK_APP_ID=your-facebook-app-id
GOOGLE_CLIENT_ID=your-google-oauth-client-id

# Payment Integration (if needed)
STRIPE_PUBLIC_KEY=your-stripe-public-key
STRIPE_SECRET_KEY=your-stripe-secret-key

# Analytics Configuration
GOOGLE_ANALYTICS_ID=your-google-analytics-id

# CDN Configuration
CDN_URL=https://your-cdn-domain.com

# Backup Configuration
BACKUP_S3_BUCKET=your-backup-bucket
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1

# Environment
ENVIRONMENT=development
DEBUG=true

# Logging
LOG_LEVEL=INFO
LOG_FILE_PATH=./logs/ubuzima.log

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100

# Session Configuration
SESSION_TIMEOUT=3600

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30

# Notification Settings
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=true
ENABLE_PUSH_NOTIFICATIONS=true

# Feature Flags
ENABLE_AI_FEATURES=true
ENABLE_VOICE_FEATURES=true
ENABLE_VIDEO_CALLS=true
ENABLE_OFFLINE_MODE=true

# Development Settings
ENABLE_SWAGGER=true
ENABLE_ACTUATOR=true
ENABLE_DEBUG_LOGGING=false
