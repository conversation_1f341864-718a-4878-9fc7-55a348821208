name: Ubuzima CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  FLUTTER_VERSION: '3.16.0'
  JAVA_VERSION: '17'
  NODE_VERSION: '18'

jobs:
  # Backend Tests
  backend-test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: ubuzima_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v4

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Maven packages
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Run backend tests
      working-directory: ./backend
      run: |
        mvn clean test
        mvn jacoco:report

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/target/site/jacoco/jacoco.xml
        flags: backend

  # Frontend Tests
  frontend-test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ env.FLUTTER_VERSION }}

    - name: Cache Flutter packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.pub-cache
          frontend/ubuzima_app/.dart_tool
        key: ${{ runner.os }}-flutter-${{ hashFiles('**/pubspec.lock') }}
        restore-keys: ${{ runner.os }}-flutter-

    - name: Get Flutter dependencies
      working-directory: ./frontend/ubuzima_app
      run: flutter pub get

    - name: Analyze Flutter code
      working-directory: ./frontend/ubuzima_app
      run: flutter analyze

    - name: Run Flutter tests
      working-directory: ./frontend/ubuzima_app
      run: flutter test --coverage

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/ubuzima_app/coverage/lcov.info
        flags: frontend

  # Security Scan
  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # Build and Deploy
  build-and-deploy:
    needs: [backend-test, frontend-test, security-scan]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Build and push backend image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: true
        tags: |
          ${{ secrets.DOCKER_USERNAME }}/ubuzima-backend:latest
          ${{ secrets.DOCKER_USERNAME }}/ubuzima-backend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Build and push frontend image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend/ubuzima_app
        push: true
        tags: |
          ${{ secrets.DOCKER_USERNAME }}/ubuzima-frontend:latest
          ${{ secrets.DOCKER_USERNAME }}/ubuzima-frontend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Deploy to staging
      if: github.ref == 'refs/heads/develop'
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here

    - name: Deploy to production
      if: github.ref == 'refs/heads/main'
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here

  # Performance Tests
  performance-test:
    needs: [build-and-deploy]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Run performance tests
      run: |
        echo "Running performance tests..."
        # Add performance testing commands here

  # Notification
  notify:
    needs: [build-and-deploy]
    runs-on: ubuntu-latest
    if: always()

    steps:
    - name: Notify deployment status
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      if: always()
