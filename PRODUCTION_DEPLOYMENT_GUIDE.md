# 🚀 Ubuzima Production Deployment Guide

## 📋 **Overview**

This guide provides step-by-step instructions for deploying the Ubuzima application to production environments using modern cloud platforms and best practices.

---

## 🏗️ **Architecture Overview**

```
Internet → Load Balancer → Frontend (Flutter Web) → Backend (Spring Boot) → Database (PostgreSQL)
                      ↓
                   CDN (Static Assets)
                      ↓
                   Monitoring & Logging
```

---

## 🔧 **Prerequisites**

### **Required Tools:**
- Docker & Docker Compose
- Git
- Domain name (for production)
- SSL certificate (Let's Encrypt recommended)

### **Cloud Platform Options:**
1. **AWS** (Recommended for enterprise)
2. **Google Cloud Platform** (Good for startups)
3. **DigitalOcean** (Cost-effective)
4. **Heroku** (Easiest for beginners)

---

## 🚀 **Deployment Options**

### **Option 1: Docker Compose (Recommended)**

#### **Step 1: Prepare Environment**
```bash
# Clone repository
git clone https://github.com/your-username/ubuzima-app.git
cd ubuzima-app

# Copy environment file
cp .env.example .env

# Edit environment variables
nano .env
```

#### **Step 2: Configure Environment Variables**
```bash
# Database
DATABASE_URL=******************************************
DATABASE_USERNAME=ubuzima_user
DATABASE_PASSWORD=your_secure_password

# JWT
JWT_SECRET=your-super-secret-jwt-key-256-bits-minimum

# CORS
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Email
MAIL_HOST=smtp.gmail.com
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# AI
GEMINI_API_KEY=your-gemini-api-key

# Maps
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
```

#### **Step 3: Deploy with Docker Compose**
```bash
# Build and start services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

#### **Step 4: Setup SSL (Production)**
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### **Option 2: Cloud Platform Deployment**

#### **AWS Deployment**

**Step 1: Setup AWS Resources**
```bash
# Create RDS PostgreSQL instance
aws rds create-db-instance \
  --db-instance-identifier ubuzima-db \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --master-username ubuzima_user \
  --master-user-password your_password \
  --allocated-storage 20

# Create ECS cluster
aws ecs create-cluster --cluster-name ubuzima-cluster

# Create Application Load Balancer
aws elbv2 create-load-balancer \
  --name ubuzima-alb \
  --subnets subnet-12345 subnet-67890 \
  --security-groups sg-12345
```

**Step 2: Deploy Backend to ECS**
```bash
# Build and push Docker image
docker build -t ubuzima-backend ./backend
docker tag ubuzima-backend:latest your-account.dkr.ecr.region.amazonaws.com/ubuzima-backend:latest
docker push your-account.dkr.ecr.region.amazonaws.com/ubuzima-backend:latest

# Create ECS task definition
aws ecs register-task-definition --cli-input-json file://backend-task-definition.json

# Create ECS service
aws ecs create-service \
  --cluster ubuzima-cluster \
  --service-name ubuzima-backend \
  --task-definition ubuzima-backend:1 \
  --desired-count 2
```

**Step 3: Deploy Frontend to S3 + CloudFront**
```bash
# Build Flutter web app
cd frontend/ubuzima_app
flutter build web --release

# Upload to S3
aws s3 sync build/web/ s3://your-bucket-name/ --delete

# Create CloudFront distribution
aws cloudfront create-distribution --distribution-config file://cloudfront-config.json
```

#### **Google Cloud Platform Deployment**

**Step 1: Setup GCP Resources**
```bash
# Create Cloud SQL instance
gcloud sql instances create ubuzima-db \
  --database-version=POSTGRES_13 \
  --tier=db-f1-micro \
  --region=us-central1

# Create GKE cluster
gcloud container clusters create ubuzima-cluster \
  --zone=us-central1-a \
  --num-nodes=2
```

**Step 2: Deploy to GKE**
```bash
# Build and push to Container Registry
docker build -t gcr.io/your-project/ubuzima-backend ./backend
docker push gcr.io/your-project/ubuzima-backend

# Deploy to Kubernetes
kubectl apply -f k8s/
```

#### **DigitalOcean Deployment**

**Step 1: Create Droplet**
```bash
# Create Ubuntu droplet
doctl compute droplet create ubuzima-server \
  --size s-2vcpu-2gb \
  --image ubuntu-20-04-x64 \
  --region nyc1 \
  --ssh-keys your-ssh-key-id
```

**Step 2: Setup Server**
```bash
# SSH to server
ssh root@your-server-ip

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Clone and deploy
git clone https://github.com/your-username/ubuzima-app.git
cd ubuzima-app
cp .env.example .env
# Edit .env with your values
docker-compose up -d
```

---

## 🔒 **Security Configuration**

### **SSL/TLS Setup**
```nginx
# nginx.conf
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
}
```

### **Firewall Configuration**
```bash
# UFW (Ubuntu)
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable

# Fail2ban
sudo apt install fail2ban
sudo systemctl enable fail2ban
```

### **Database Security**
```sql
-- Create restricted user
CREATE USER ubuzima_app WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE ubuzima_db TO ubuzima_app;
GRANT USAGE ON SCHEMA public TO ubuzima_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO ubuzima_app;

-- Enable SSL
ALTER SYSTEM SET ssl = on;
SELECT pg_reload_conf();
```

---

## 📊 **Monitoring & Logging**

### **Application Monitoring**
```yaml
# docker-compose.monitoring.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

### **Log Management**
```yaml
# ELK Stack
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
    environment:
      - discovery.type=single-node
    ports:
      - "9200:9200"

  logstash:
    image: docker.elastic.co/logstash/logstash:7.15.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf

  kibana:
    image: docker.elastic.co/kibana/kibana:7.15.0
    ports:
      - "5601:5601"
```

---

## 🔄 **CI/CD Pipeline**

### **GitHub Actions Workflow**
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Deploy to server
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /opt/ubuzima-app
            git pull origin main
            docker-compose down
            docker-compose up -d --build
```

---

## 🧪 **Health Checks & Testing**

### **Application Health Checks**
```bash
# Backend health check
curl -f http://localhost:8080/api/v1/actuator/health

# Frontend health check
curl -f http://localhost/health

# Database health check
pg_isready -h localhost -p 5432 -U ubuzima_user
```

### **Load Testing**
```bash
# Install Apache Bench
sudo apt install apache2-utils

# Test backend API
ab -n 1000 -c 10 http://localhost:8080/api/v1/health

# Test frontend
ab -n 1000 -c 10 http://localhost/
```

---

## 📈 **Scaling Considerations**

### **Horizontal Scaling**
```yaml
# docker-compose.scale.yml
services:
  backend:
    deploy:
      replicas: 3
    
  frontend:
    deploy:
      replicas: 2
```

### **Database Scaling**
```sql
-- Read replicas
CREATE PUBLICATION ubuzima_pub FOR ALL TABLES;

-- Connection pooling
# pgbouncer configuration
[databases]
ubuzima_db = host=localhost port=5432 dbname=ubuzima_db

[pgbouncer]
pool_mode = transaction
max_client_conn = 100
default_pool_size = 20
```

---

## 🔧 **Maintenance**

### **Backup Strategy**
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U ubuzima_user ubuzima_db > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://your-backup-bucket/
```

### **Update Process**
```bash
#!/bin/bash
# update.sh
git pull origin main
docker-compose down
docker-compose pull
docker-compose up -d
docker system prune -f
```

---

## 🎯 **Performance Optimization**

### **Frontend Optimization**
```bash
# Build optimized Flutter web
flutter build web --release --web-renderer html --dart-define=FLUTTER_WEB_USE_SKIA=false

# Enable gzip compression
gzip_on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
```

### **Backend Optimization**
```yaml
# application-prod.yml
spring:
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
```

---

## ✅ **Production Checklist**

- [ ] Environment variables configured
- [ ] SSL certificate installed
- [ ] Database secured and backed up
- [ ] Monitoring and logging setup
- [ ] Health checks implemented
- [ ] CI/CD pipeline configured
- [ ] Load testing completed
- [ ] Security scan passed
- [ ] Documentation updated
- [ ] Team trained on deployment process

---

## 🆘 **Troubleshooting**

### **Common Issues**
1. **Database connection failed**: Check network connectivity and credentials
2. **SSL certificate expired**: Renew with `certbot renew`
3. **High memory usage**: Scale horizontally or increase instance size
4. **Slow response times**: Check database queries and add indexes

### **Emergency Procedures**
```bash
# Rollback deployment
docker-compose down
git checkout previous-stable-tag
docker-compose up -d

# Scale down during high load
docker-compose scale backend=1 frontend=1

# Emergency database backup
pg_dump -h localhost -U ubuzima_user ubuzima_db > emergency_backup.sql
```

---

**Your Ubuzima application is now ready for production deployment! 🚀**

For support, contact the development team or refer to the troubleshooting section.
