<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="ubuzima_db@localhost" uuid="768506ae-a036-49d0-863c-ca947b12d20d">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>*******************************************</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.host.port" />
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
        <property name="com.intellij.clouds.kubernetes.db.container.port" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>