# 🗄️ Ubuzima Database Setup Guide

## 📋 **Quick Setup Summary**

**Database Details:**
- **Database Name**: `ubuzima_db`
- **Username**: `ubuzima_user`
- **Password**: `ubuzima_password`
- **Host**: `localhost`
- **Port**: `5432`

---

## 🚀 **Step-by-Step Setup**

### **Step 1: Create Database and User**

#### **Option A: Using psql Command Line**

1. **Open Command Prompt/PowerShell as Administrator**
2. **Connect to PostgreSQL**:
   ```bash
   psql -U postgres
   ```
   (Enter your postgres password when prompted)

3. **Run the setup script**:
   ```bash
   \i C:/WEB/develop/backend/setup-database.sql
   ```
   OR copy and paste the commands from `setup-database.sql`

#### **Option B: Using pgAdmin**

1. **Open pgAdmin**
2. **Connect to your PostgreSQL server**
3. **Right-click on "Databases" → Create → Database**
   - Name: `ubuzima_db`
   - Owner: `postgres`
4. **Right-click on "Login/Group Roles" → Create → Login/Group Role**
   - General tab: Name = `ubuzima_user`
   - Definition tab: Password = `ubuzima_password`
   - Privileges tab: Check "Can login?"
5. **Right-click on `ubuzima_db` → Properties → Security**
   - Add `ubuzima_user` with ALL privileges

#### **Option C: Manual SQL Commands**

Copy and paste these commands in your PostgreSQL client:

```sql
-- Create database
CREATE DATABASE ubuzima_db;

-- Create user
CREATE USER ubuzima_user WITH PASSWORD 'ubuzima_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE ubuzima_db TO ubuzima_user;

-- Connect to database
\c ubuzima_db;

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO ubuzima_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO ubuzima_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO ubuzima_user;
```

### **Step 2: Verify Database Connection**

Test the connection using psql:
```bash
psql -h localhost -p 5432 -U ubuzima_user -d ubuzima_db
```

You should be able to connect without errors.

---

## 🏃‍♂️ **Running the Backend**

### **Step 1: Navigate to Backend Directory**
```bash
cd C:/WEB/develop/backend
```

### **Step 2: Build the Project**
```bash
./mvnw clean compile
```

### **Step 3: Run the Application**
```bash
./mvnw spring-boot:run
```

**OR** if you prefer to run with specific profile:
```bash
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
```

### **Step 4: Verify Backend is Running**

1. **Check console output** - Look for:
   ```
   Started UbuzimaApplication in X.XXX seconds
   ```

2. **Test health endpoint**:
   - Open browser: http://localhost:8080/api/v1/actuator/health
   - Should return: `{"status":"UP"}`

3. **Check API documentation**:
   - Open browser: http://localhost:8080/api/v1/swagger-ui.html

---

## 📊 **What Happens When You Run the Backend**

### **Automatic Table Creation**

When you run the backend with `ddl-auto: update`, Hibernate will:

1. **Connect to the database** using the credentials in `application.yml`
2. **Scan your entity classes** (User, HealthRecord, Appointment, etc.)
3. **Create tables automatically** based on your JPA annotations
4. **Create relationships** (foreign keys, indexes)
5. **Insert initial data** if you have data.sql files

### **Expected Tables Created**

Based on your entities, these tables will be created:

- `users` - User accounts and profiles
- `health_records` - Health tracking data
- `appointments` - Appointment scheduling
- `health_facilities` - Clinic information
- `audio_content` - Educational content
- `content_play_history` - Learning progress
- `content_ratings` - User feedback
- `facility_reviews` - Clinic reviews
- `user_sessions` - Authentication sessions

### **Console Output to Look For**

```
Hibernate: create table users (...)
Hibernate: create table health_records (...)
Hibernate: create table appointments (...)
...
HikariPool-1 - Start completed.
Started UbuzimaApplication in 15.234 seconds
```

---

## 🔧 **Troubleshooting**

### **Common Issues and Solutions**

#### **Issue 1: "Connection refused"**
```
org.postgresql.util.PSQLException: Connection to localhost:5432 refused
```
**Solution**: 
- Make sure PostgreSQL service is running
- Check if port 5432 is correct
- Verify PostgreSQL is listening on localhost

#### **Issue 2: "Authentication failed"**
```
org.postgresql.util.PSQLException: FATAL: password authentication failed
```
**Solution**:
- Verify username/password in application.yml
- Make sure user `ubuzima_user` exists
- Check user has correct privileges

#### **Issue 3: "Database does not exist"**
```
org.postgresql.util.PSQLException: FATAL: database "ubuzima_db" does not exist
```
**Solution**:
- Create the database using the setup script
- Verify database name spelling

#### **Issue 4: "Permission denied"**
```
org.postgresql.util.PSQLException: ERROR: permission denied for schema public
```
**Solution**:
- Grant proper privileges to ubuzima_user
- Run the GRANT commands from setup script

### **Verification Commands**

Check if PostgreSQL is running:
```bash
# Windows
sc query postgresql-x64-15

# Or check processes
tasklist | findstr postgres
```

Check if database exists:
```bash
psql -U postgres -l
```

Test connection:
```bash
psql -h localhost -p 5432 -U ubuzima_user -d ubuzima_db -c "SELECT version();"
```

---

## 🎯 **Next Steps After Setup**

1. **✅ Database Created** - ubuzima_db with ubuzima_user
2. **✅ Backend Running** - Spring Boot on port 8080
3. **✅ Tables Created** - Hibernate auto-generated schema
4. **✅ API Available** - REST endpoints accessible

### **Test the Integration**

1. **Register a new user**:
   ```bash
   curl -X POST http://localhost:8080/api/v1/auth/register \
     -H "Content-Type: application/json" \
     -d '{"name":"Test User","email":"<EMAIL>","password":"password123","phone":"+250788123456"}'
   ```

2. **Check database**:
   ```sql
   SELECT * FROM users;
   ```

3. **Login**:
   ```bash
   curl -X POST http://localhost:8080/api/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password123"}'
   ```

---

## 🎉 **Success Indicators**

You'll know everything is working when:

- ✅ Backend starts without errors
- ✅ Database tables are created automatically
- ✅ Health endpoint returns `{"status":"UP"}`
- ✅ Swagger UI is accessible
- ✅ You can register/login users
- ✅ Frontend can connect to backend APIs

**You're now ready to connect your Flutter frontend to the backend!** 🚀
