# Ubuzima Backend API

A comprehensive Spring Boot REST API for the Ubuzima Family Planning Mobile Application, providing healthcare services for rural Rwanda.

## 🏥 Overview

The Ubuzima Backend API is designed to support a culturally-sensitive family planning mobile application that serves rural communities in Rwanda. It provides robust backend services for user management, health records, appointments, facility management, and family planning education.

## 🚀 Features

### Core Functionality
- **User Management**: Multi-role user system (Clients, Health Workers, Admins)
- **Authentication & Authorization**: JWT-based security with role-based access control
- **Health Records**: Comprehensive medical record management
- **Appointment System**: Scheduling and management of healthcare appointments
- **Facility Management**: Health facility directory and management
- **Family Planning Services**: Specialized endpoints for contraception and reproductive health

### Technical Features
- **RESTful API**: Clean, well-documented REST endpoints
- **Database**: PostgreSQL with Flyway migrations
- **Security**: Spring Security with JWT authentication
- **Documentation**: OpenAPI/Swagger integration
- **Validation**: Comprehensive input validation
- **Error Handling**: Global exception handling
- **Caching**: Redis-ready caching support
- **Monitoring**: Spring Boot Actuator integration

## 🛠️ Technology Stack

- **Framework**: Spring Boot 3.2.1
- **Language**: Java 17
- **Database**: PostgreSQL
- **Security**: Spring Security + JWT
- **Documentation**: OpenAPI 3 (Swagger)
- **Build Tool**: Maven
- **Migration**: Flyway
- **Testing**: JUnit 5, TestContainers

## 📋 Prerequisites

- Java 17 or higher
- Maven 3.6+
- PostgreSQL 12+
- Git

## 🔧 Installation & Setup

### 1. Clone the Repository
```bash
git clone https://github.com/ubuzima/ubuzima-backend.git
cd ubuzima-backend
```

### 2. Database Setup
```bash
# Create PostgreSQL database
createdb ubuzima_db

# Create user (optional)
psql -c "CREATE USER ubuzima_user WITH PASSWORD 'ubuzima_password';"
psql -c "GRANT ALL PRIVILEGES ON DATABASE ubuzima_db TO ubuzima_user;"
```

### 3. Configuration
Update `src/main/resources/application.yml` with your database credentials:

```yaml
spring:
  datasource:
    url: *******************************************
    username: ubuzima_user
    password: ubuzima_password
```

### 4. Build & Run
```bash
# Build the application
mvn clean install

# Run the application
mvn spring-boot:run
```

The API will be available at `http://localhost:8080/api/v1`

## 📚 API Documentation

Once the application is running, access the interactive API documentation:

- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **OpenAPI JSON**: http://localhost:8080/v3/api-docs

## 🔐 Authentication

The API uses JWT (JSON Web Tokens) for authentication. To access protected endpoints:

1. **Register/Login** via `/api/v1/auth/login`
2. **Include JWT token** in the Authorization header: `Bearer <your-jwt-token>`

### Example Login Request
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "password123"
  }'
```

## 🏗️ Project Structure

```
src/main/java/rw/health/ubuzima/
├── config/          # Configuration classes
├── controller/      # REST controllers
├── dto/            # Data Transfer Objects
├── entity/         # JPA entities
├── enums/          # Enumeration classes
├── exception/      # Custom exceptions
├── repository/     # Data repositories
├── security/       # Security configuration
└── service/        # Business logic services

src/main/resources/
├── db/migration/   # Flyway database migrations
└── application.yml # Application configuration
```

## 🗄️ Database Schema

The application uses PostgreSQL with the following main entities:

- **Users**: Multi-role user management
- **Health Facilities**: Healthcare facility directory
- **Appointments**: Appointment scheduling system
- **Health Records**: Medical records and health data
- **User Sessions**: Session management
- **Facility Reviews**: User reviews and ratings

## 🔒 Security Features

- **JWT Authentication**: Stateless authentication
- **Role-Based Access Control**: CLIENT, HEALTH_WORKER, ADMIN, SUPER_ADMIN
- **Password Encryption**: BCrypt hashing
- **Account Security**: Login attempt tracking and account locking
- **CORS Configuration**: Cross-origin resource sharing
- **Input Validation**: Comprehensive request validation

## 🧪 Testing

```bash
# Run all tests
mvn test

# Run with coverage
mvn test jacoco:report
```

## 🚀 Deployment

### Environment Variables
Set the following environment variables for production:

```bash
DATABASE_URL=**********************************************
DATABASE_USERNAME=your-username
DATABASE_PASSWORD=your-password
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRATION=********
CORS_ALLOWED_ORIGINS=https://your-frontend-domain.com
```

### Docker Deployment
```bash
# Build Docker image
docker build -t ubuzima-backend .

# Run with Docker Compose
docker-compose up -d
```

## 📊 Monitoring & Health Checks

The application includes Spring Boot Actuator endpoints:

- **Health Check**: `/actuator/health`
- **Application Info**: `/actuator/info`
- **Metrics**: `/actuator/metrics`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Team

- **Ubuzima Development Team**
- **Email**: <EMAIL>
- **Website**: https://ubuzima.rw

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>
- Documentation: https://docs.ubuzima.rw

---

**Ubuzima** - Empowering rural Rwanda with accessible family planning healthcare technology.
