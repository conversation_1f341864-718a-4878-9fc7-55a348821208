version: '3.8'

services:
  # PostgreSQL Database for Development
  postgres:
    image: postgres:15-alpine
    container_name: ubuzima-postgres-dev
    environment:
      POSTGRES_DB: ubuzima_db
      POSTGRES_USER: ubuzima_user
      POSTGRES_PASSWORD: ubuzima_password
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./src/main/resources/db/init:/docker-entrypoint-initdb.d
    networks:
      - ubuzima-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ubuzima_user -d ubuzima_db"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # pgAdmin for Database Management (Optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ubuzima-pgadmin-dev
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8081:80"
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
    networks:
      - ubuzima-dev-network
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres_dev_data:
    driver: local
  pgadmin_dev_data:
    driver: local

networks:
  ubuzima-dev-network:
    driver: bridge
