2025-07-08 20:55:07 [main] INFO  r.health.ubuzima.UbuzimaApplication - Starting UbuzimaApplication using Java 17.0.15 with PID 41392 (C:\WEB\develop\backend\target\classes started by tganz in C:\WEB\develop\backend)
2025-07-08 20:55:07 [main] DEBUG r.health.ubuzima.UbuzimaApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-08 20:55:07 [main] INFO  r.health.ubuzima.UbuzimaApplication - The following 1 profile is active: "dev"
2025-07-08 20:55:08 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-08 20:55:08 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 85 ms. Found 12 JPA repository interfaces.
2025-07-08 20:55:08 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port 8080 (http)
2025-07-08 20:55:08 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-08 20:55:08 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-08 20:55:09 [main] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring embedded WebApplicationContext
2025-07-08 20:55:09 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2020 ms
2025-07-08 20:55:09 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-08 20:55:09 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-08 20:55:09 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-08 20:55:09 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-08 20:55:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-08 20:55:09 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3c62be3c
2025-07-08 20:55:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-08 20:55:10 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-08 20:55:11 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table appointments (
        id bigserial not null,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        version bigint,
        appointment_type varchar(255) not null check (appointment_type in ('CONSULTATION','FAMILY_PLANNING','PRENATAL_CARE','POSTNATAL_CARE','VACCINATION','HEALTH_SCREENING','FOLLOW_UP','EMERGENCY','COUNSELING','OTHER')),
        cancellation_reason varchar(255),
        cancelled_at timestamp(6),
        completed_at timestamp(6),
        duration_minutes integer,
        notes TEXT,
        reason TEXT,
        reminder_sent boolean,
        scheduled_date timestamp(6) not null,
        status varchar(255) not null check (status in ('SCHEDULED','CONFIRMED','IN_PROGRESS','COMPLETED','CANCELLED','NO_SHOW','RESCHEDULED')),
        health_facility_id bigint not null,
        health_worker_id bigint,
        user_id bigint not null,
        primary key (id)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table contraception_methods (
        id bigserial not null,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        version bigint,
        additional_data TEXT,
        description TEXT,
        effectiveness float(53),
        end_date date,
        instructions TEXT,
        is_active boolean,
        name varchar(255) not null,
        next_appointment date,
        prescribed_by varchar(255),
        start_date date not null,
        contraception_type varchar(255) not null check (contraception_type in ('PILL','INJECTION','IMPLANT','IUD','CONDOM','DIAPHRAGM','PATCH','RING','NATURAL_FAMILY_PLANNING','STERILIZATION','EMERGENCY_CONTRACEPTION','OTHER')),
        user_id bigint not null,
        primary key (id)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table contraception_side_effects (
        contraception_id bigint not null,
        side_effect varchar(255)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table education_lessons (
        id bigserial not null,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        version bigint,
        audio_url varchar(255),
        author varchar(255),
        category varchar(255) not null check (category in ('FAMILY_PLANNING','CONTRACEPTION','MENSTRUAL_HEALTH','PREGNANCY','STI_PREVENTION','REPRODUCTIVE_HEALTH','MATERNAL_HEALTH','NUTRITION','GENERAL_HEALTH','MENTAL_HEALTH')),
        content TEXT,
        description TEXT,
        duration_minutes integer,
        is_published boolean,
        language varchar(255),
        level varchar(255) not null check (level in ('BEGINNER','INTERMEDIATE','ADVANCED','EXPERT')),
        order_index integer,
        title varchar(255) not null,
        video_url varchar(255),
        view_count bigint,
        primary key (id)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table education_progress (
        id bigserial not null,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        version bigint,
        completed_at timestamp(6),
        is_completed boolean,
        last_accessed_at timestamp(6),
        notes TEXT,
        progress_percentage float(53),
        quiz_attempts integer,
        quiz_score float(53),
        time_spent_minutes integer,
        lesson_id bigint not null,
        user_id bigint not null,
        primary key (id)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table health_facilities (
        id bigserial not null,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        version bigint,
        address varchar(255) not null,
        email varchar(255),
        emergency_contact varchar(255),
        facility_type varchar(255) not null check (facility_type in ('HOSPITAL','HEALTH_CENTER','CLINIC','DISPENSARY','PHARMACY','LABORATORY','MATERNITY_CENTER','COMMUNITY_HEALTH_POST','PRIVATE_PRACTICE','OTHER')),
        is_active boolean,
        latitude float(53),
        longitude float(53),
        name varchar(255) not null,
        operating_hours varchar(255),
        phone_number varchar(255),
        services_offered TEXT,
        website_url varchar(255),
        primary key (id)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table health_records (
        id bigserial not null,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        version bigint,
        is_verified boolean,
        notes TEXT,
        record_type varchar(255) not null check (record_type in ('WEIGHT','HEIGHT','BLOOD_PRESSURE','TEMPERATURE','HEART_RATE','MENSTRUAL_CYCLE','PREGNANCY_TEST','CONTRACEPTIVE_USE','SYMPTOMS','MEDICATION','VACCINATION','CONSULTATION_NOTES','OTHER')),
        recorded_at timestamp(6) not null,
        recorded_by varchar(255),
        unit varchar(255),
        value varchar(255) not null,
        verification_notes varchar(255),
        user_id bigint not null,
        primary key (id)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table lesson_images (
        lesson_id bigint not null,
        image_url varchar(255)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table lesson_tags (
        lesson_id bigint not null,
        tag varchar(255)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table medication_side_effects (
        medication_id bigint not null,
        side_effect varchar(255)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table medications (
        id bigserial not null,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        version bigint,
        dosage varchar(255) not null,
        end_date date,
        frequency varchar(255) not null,
        instructions TEXT,
        is_active boolean,
        name varchar(255) not null,
        notes TEXT,
        prescribed_by varchar(255),
        purpose varchar(255) not null,
        start_date date not null,
        user_id bigint not null,
        primary key (id)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table menstrual_cycles (
        id bigserial not null,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        version bigint,
        cycle_length integer,
        end_date date,
        fertile_window_end date,
        fertile_window_start date,
        flow_duration integer,
        flow_intensity varchar(255) check (flow_intensity in ('LIGHT','NORMAL','HEAVY')),
        is_predicted boolean,
        notes TEXT,
        ovulation_date date,
        start_date date not null,
        user_id bigint not null,
        primary key (id)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table menstrual_symptoms (
        cycle_id bigint not null,
        symptom varchar(255)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table message_attachments (
        message_id bigint not null,
        attachment_url varchar(255)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table messages (
        id bigserial not null,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        version bigint,
        content TEXT,
        conversation_id varchar(255),
        is_emergency boolean,
        is_read boolean,
        message_type varchar(255) check (message_type in ('TEXT','VOICE','IMAGE','AUDIO','VIDEO','DOCUMENT','LOCATION')),
        metadata TEXT,
        priority varchar(255) check (priority in ('LOW','NORMAL','HIGH','URGENT')),
        read_at timestamp(6),
        reply_to_id bigint,
        receiver_id bigint not null,
        sender_id bigint not null,
        primary key (id)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table notifications (
        id bigserial not null,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        version bigint,
        action_url varchar(255),
        icon varchar(255),
        is_read boolean,
        message TEXT,
        metadata TEXT,
        priority integer,
        read_at timestamp(6),
        scheduled_for timestamp(6),
        sent_at timestamp(6),
        title varchar(255) not null,
        notification_type varchar(255) check (notification_type in ('APPOINTMENT_REMINDER','MEDICATION_REMINDER','HEALTH_TIP','EMERGENCY_ALERT','SYSTEM_NOTIFICATION','MESSAGE_RECEIVED','EDUCATION_REMINDER','CONTRACEPTION_REMINDER','MENSTRUAL_REMINDER','GENERAL')),
        user_id bigint not null,
        primary key (id)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table time_slots (
        id bigserial not null,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        version bigint,
        current_appointments integer,
        end_time timestamp(6) not null,
        is_available boolean,
        max_appointments integer,
        reason varchar(255),
        start_time timestamp(6) not null,
        health_facility_id bigint,
        health_worker_id bigint,
        primary key (id)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    create table users (
        id bigserial not null,
        created_at timestamp(6) not null,
        updated_at timestamp(6),
        version bigint,
        cell varchar(255),
        date_of_birth date,
        district varchar(255),
        email varchar(255) not null,
        email_verified boolean,
        emergency_contact varchar(255),
        facility_id varchar(255),
        gender varchar(255) check (gender in ('MALE','FEMALE','OTHER','PREFER_NOT_TO_SAY')),
        last_login_at date,
        name varchar(255) not null,
        password_hash varchar(255) not null,
        phone varchar(255) not null,
        phone_verified boolean,
        preferred_language varchar(255),
        profile_picture_url varchar(255),
        role varchar(255) not null check (role in ('CLIENT','HEALTH_WORKER','ADMIN')),
        sector varchar(255),
        status varchar(255) not null check (status in ('ACTIVE','INACTIVE','SUSPENDED','PENDING_VERIFICATION')),
        village varchar(255),
        primary key (id)
    )
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists users 
       drop constraint if exists UK_6dotkott2kjsp8vw4d0m25fb7
2025-07-08 20:55:11 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - SQL Warning Code: 0, SQLState: 00000
2025-07-08 20:55:11 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - constraint "uk_6dotkott2kjsp8vw4d0m25fb7" of relation "users" does not exist, skipping
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists users 
       add constraint UK_6dotkott2kjsp8vw4d0m25fb7 unique (email)
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists users 
       drop constraint if exists UK_du5v5sr43g5bfnji4vb8hg5s3
2025-07-08 20:55:11 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - SQL Warning Code: 0, SQLState: 00000
2025-07-08 20:55:11 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - constraint "uk_du5v5sr43g5bfnji4vb8hg5s3" of relation "users" does not exist, skipping
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists users 
       add constraint UK_du5v5sr43g5bfnji4vb8hg5s3 unique (phone)
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists appointments 
       add constraint FK5ap3xihtac67r0iixe46q6015 
       foreign key (health_facility_id) 
       references health_facilities
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists appointments 
       add constraint FKjsb4mgoelr1m6e9m4fu5ygdk1 
       foreign key (health_worker_id) 
       references users
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists appointments 
       add constraint FK886ced1atxgvnf1o3oxtj5m4s 
       foreign key (user_id) 
       references users
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists contraception_methods 
       add constraint FKe2atxucm12m9wfyt49ugd4o0v 
       foreign key (user_id) 
       references users
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists contraception_side_effects 
       add constraint FK6w166uv1nhum5d1l8r1kx94sk 
       foreign key (contraception_id) 
       references contraception_methods
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists education_progress 
       add constraint FKry49ua156sersg1vgecn3wnl4 
       foreign key (lesson_id) 
       references education_lessons
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists education_progress 
       add constraint FK5erlq4pu2j8xxg8mq3a53eqc5 
       foreign key (user_id) 
       references users
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists health_records 
       add constraint FKnm8qm5054prog8qul6v2jce1d 
       foreign key (user_id) 
       references users
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists lesson_images 
       add constraint FK2hbnty9hqb56qb6sl9cgmfifv 
       foreign key (lesson_id) 
       references education_lessons
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists lesson_tags 
       add constraint FKnervbrcjbv424bx5d78nym463 
       foreign key (lesson_id) 
       references education_lessons
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists medication_side_effects 
       add constraint FK6acdcsestnwdrbbstug5sk406 
       foreign key (medication_id) 
       references medications
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists medications 
       add constraint FKsae8ns7nscnqntu61xu8xxwl3 
       foreign key (user_id) 
       references users
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists menstrual_cycles 
       add constraint FK95f2xv8lhdppp7mo076m9mu0q 
       foreign key (user_id) 
       references users
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists menstrual_symptoms 
       add constraint FKe6y040eits7uahnbocri6pfr7 
       foreign key (cycle_id) 
       references menstrual_cycles
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists message_attachments 
       add constraint FKj7twd218e2gqw9cmlhwvo1rth 
       foreign key (message_id) 
       references messages
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists messages 
       add constraint FKt05r0b6n0iis8u7dfna4xdh73 
       foreign key (receiver_id) 
       references users
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists messages 
       add constraint FK4ui4nnwntodh6wjvck53dbk9m 
       foreign key (sender_id) 
       references users
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists notifications 
       add constraint FK9y21adhxn0ayjhfocscqox7bh 
       foreign key (user_id) 
       references users
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists time_slots 
       add constraint FK3f0pc1dhenjt4n02vj8tqhfvr 
       foreign key (health_facility_id) 
       references health_facilities
2025-07-08 20:55:11 [main] DEBUG org.hibernate.SQL - 
    alter table if exists time_slots 
       add constraint FKvvuovr02xfmx0efcrpgrjled 
       foreign key (health_worker_id) 
       references users
2025-07-08 20:55:11 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-08 20:55:11 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-08 20:55:12 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-08 20:55:12 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: a8673dba-8447-4f58-9c00-a7651a8a4075

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-08 20:55:13 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-07-08 20:55:13 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@34bc32be, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5a0be3d0, org.springframework.security.web.context.SecurityContextHolderFilter@81a3c9b, org.springframework.security.web.header.HeaderWriterFilter@26d709d2, org.springframework.web.filter.CorsFilter@257ef7ca, org.springframework.security.web.authentication.logout.LogoutFilter@ebf1fd1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@222c7639, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6e9f87f8, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@15a75f79, org.springframework.security.web.session.SessionManagementFilter@1a5fa7e3, org.springframework.security.web.access.ExceptionTranslationFilter@46941601, org.springframework.security.web.access.intercept.AuthorizationFilter@7249d9ce]
2025-07-08 20:55:14 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api/v1'
2025-07-08 20:55:14 [main] INFO  r.health.ubuzima.UbuzimaApplication - Started UbuzimaApplication in 8.128 seconds (process running for 10.184)
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.User */insert 
    into
        users (cell, created_at, date_of_birth, district, email, email_verified, emergency_contact, facility_id, gender, last_login_at, name, password_hash, phone, phone_verified, preferred_language, profile_picture_url, role, sector, status, updated_at, version, village) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.User */insert 
    into
        users (cell, created_at, date_of_birth, district, email, email_verified, emergency_contact, facility_id, gender, last_login_at, name, password_hash, phone, phone_verified, preferred_language, profile_picture_url, role, sector, status, updated_at, version, village) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* <criteria> */ select
        u1_0.id 
    from
        users u1_0 
    where
        u1_0.email=? 
    fetch
        first ? rows only
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.User */insert 
    into
        users (cell, created_at, date_of_birth, district, email, email_verified, emergency_contact, facility_id, gender, last_login_at, name, password_hash, phone, phone_verified, preferred_language, profile_picture_url, role, sector, status, updated_at, version, village) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        HealthFacility x */ select
            count(*) 
        from
            health_facilities hf1_0
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.HealthFacility */insert 
    into
        health_facilities (address, created_at, email, emergency_contact, facility_type, is_active, latitude, longitude, name, operating_hours, phone_number, services_offered, updated_at, version, website_url) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.HealthFacility */insert 
    into
        health_facilities (address, created_at, email, emergency_contact, facility_type, is_active, latitude, longitude, name, operating_hours, phone_number, services_offered, updated_at, version, website_url) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.HealthFacility */insert 
    into
        health_facilities (address, created_at, email, emergency_contact, facility_type, is_active, latitude, longitude, name, operating_hours, phone_number, services_offered, updated_at, version, website_url) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.HealthFacility */insert 
    into
        health_facilities (address, created_at, email, emergency_contact, facility_type, is_active, latitude, longitude, name, operating_hours, phone_number, services_offered, updated_at, version, website_url) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.HealthFacility */insert 
    into
        health_facilities (address, created_at, email, emergency_contact, facility_type, is_active, latitude, longitude, name, operating_hours, phone_number, services_offered, updated_at, version, website_url) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* select
        count(*) 
    from
        EducationLesson x */ select
            count(*) 
        from
            education_lessons el1_0
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.EducationLesson */insert 
    into
        education_lessons (audio_url, author, category, content, created_at, description, duration_minutes, is_published, language, level, order_index, title, updated_at, version, video_url, view_count) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.EducationLesson */insert 
    into
        education_lessons (audio_url, author, category, content, created_at, description, duration_minutes, is_published, language, level, order_index, title, updated_at, version, video_url, view_count) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-08 20:55:14 [main] DEBUG org.hibernate.SQL - 
    /* insert for
        rw.health.ubuzima.entity.EducationLesson */insert 
    into
        education_lessons (audio_url, author, category, content, created_at, description, duration_minutes, is_published, language, level, order_index, title, updated_at, version, video_url, view_count) 
    values
        (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-08 20:55:15 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[localhost].[/api/v1] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-08 20:55:15 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-08 20:55:15 [RMI TCP Connection(3)-************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
