@REM Maven Wrapper for Windows
@echo off
setlocal

set MAVEN_PROJECTBASEDIR=%~dp0
if "%MAVEN_PROJECTBASEDIR%"=="" set MAVEN_PROJECTBASEDIR=.

set MAVEN_OPTS=%MAVEN_OPTS% -Xmx1024m

if not "%JAVA_HOME%"=="" goto OkJHome
echo Error: JAVA_HOME not found in your environment.
echo Please set the JAVA_HOME variable to match the location of your Java installation.
goto error

:OkJHome
if exist "%JAVA_HOME%\bin\java.exe" goto init
echo Error: JAVA_HOME is set to an invalid directory.
echo Please set the JAVA_HOME variable to match the location of your Java installation.
goto error

:init
set MAVEN_CMD_LINE_ARGS=%*
set MAVEN_JAVA_EXE="%JAVA_HOME%\bin\java.exe"

%MAVEN_JAVA_EXE% -cp "%MAVEN_PROJECTBASEDIR%\.mvn\wrapper\maven-wrapper.jar" "-Dmaven.multiModuleProjectDirectory=%MAVEN_PROJECTBASEDIR%" org.apache.maven.wrapper.MavenWrapperMain %MAVEN_CMD_LINE_ARGS%
if ERRORLEVEL 1 goto error
goto end

:error
set ERROR_CODE=1

:end
exit /B %ERROR_CODE%
