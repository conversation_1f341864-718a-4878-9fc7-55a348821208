package rw.health.ubuzima;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Main application class for Ubuzima Family Planning Backend API
 * 
 * This application provides comprehensive backend services for:
 * - Family planning education and resources
 * - Health tracking and monitoring
 * - Appointment management
 * - Communication between clients and health workers
 * - Administrative functions for health facilities
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableJpaAuditing
@EnableCaching
@EnableAsync
@EnableScheduling
public class UbuzimaApplication {

    public static void main(String[] args) {
        SpringApplication.run(UbuzimaApplication.class, args);
    }
}
