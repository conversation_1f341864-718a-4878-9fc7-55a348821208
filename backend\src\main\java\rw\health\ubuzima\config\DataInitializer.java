package rw.health.ubuzima.config;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.entity.HealthFacility;
import rw.health.ubuzima.enums.UserRole;
import rw.health.ubuzima.enums.UserStatus;
import rw.health.ubuzima.enums.FacilityType;
import rw.health.ubuzima.repository.UserRepository;
import rw.health.ubuzima.repository.HealthFacilityRepository;

@Component
@RequiredArgsConstructor
public class DataInitializer implements CommandLineRunner {

    private final UserRepository userRepository;
    private final HealthFacilityRepository healthFacilityRepository;

    @Override
    public void run(String... args) throws Exception {
        initializeUsers();
        initializeHealthFacilities();
    }

    private void initializeUsers() {
        // Create admin user if not exists
        if (!userRepository.existsByEmail("<EMAIL>")) {
            User admin = new User();
            admin.setName("System Administrator");
            admin.setEmail("<EMAIL>");
            admin.setPhone("+250788000001");
            admin.setPasswordHash("admin123"); // In real app, hash this
            admin.setRole(UserRole.ADMIN);
            admin.setStatus(UserStatus.ACTIVE);
            admin.setDistrict("Kigali");
            admin.setSector("Nyarugenge");
            admin.setCell("Nyarugenge");
            admin.setVillage("Kigali");
            admin.setEmailVerified(true);
            admin.setPhoneVerified(true);
            userRepository.save(admin);
        }

        // Create health worker if not exists
        if (!userRepository.existsByEmail("<EMAIL>")) {
            User healthWorker = new User();
            healthWorker.setName("Dr. Marie Uwimana");
            healthWorker.setEmail("<EMAIL>");
            healthWorker.setPhone("+250788000002");
            healthWorker.setPasswordHash("healthworker123"); // In real app, hash this
            healthWorker.setRole(UserRole.HEALTH_WORKER);
            healthWorker.setStatus(UserStatus.ACTIVE);
            healthWorker.setFacilityId("1");
            healthWorker.setDistrict("Kigali");
            healthWorker.setSector("Gasabo");
            healthWorker.setCell("Kimisagara");
            healthWorker.setVillage("Kimisagara");
            healthWorker.setEmailVerified(true);
            healthWorker.setPhoneVerified(true);
            userRepository.save(healthWorker);
        }

        // Create client if not exists
        if (!userRepository.existsByEmail("<EMAIL>")) {
            User client = new User();
            client.setName("Grace Mukamana");
            client.setEmail("<EMAIL>");
            client.setPhone("+250788000003");
            client.setPasswordHash("client123"); // In real app, hash this
            client.setRole(UserRole.CLIENT);
            client.setStatus(UserStatus.ACTIVE);
            client.setDistrict("Kigali");
            client.setSector("Kicukiro");
            client.setCell("Gahanga");
            client.setVillage("Gahanga");
            client.setEmailVerified(true);
            client.setPhoneVerified(true);
            userRepository.save(client);
        }
    }

    private void initializeHealthFacilities() {
        // Create sample health facilities if not exist
        if (healthFacilityRepository.count() == 0) {
            // Kigali University Teaching Hospital
            HealthFacility chuk = new HealthFacility();
            chuk.setName("Kigali University Teaching Hospital (CHUK)");
            chuk.setFacilityType(FacilityType.HOSPITAL);
            chuk.setAddress("KN 4 Ave, Kigali, Rwanda");
            chuk.setPhoneNumber("+250788300000");
            chuk.setEmail("<EMAIL>");
            chuk.setLatitude(-1.9441);
            chuk.setLongitude(30.0619);
            chuk.setOperatingHours("24/7");
            chuk.setServicesOffered("Emergency Care, Surgery, Maternity, Family Planning, General Medicine");
            chuk.setIsActive(true);
            healthFacilityRepository.save(chuk);

            // King Faisal Hospital
            HealthFacility kfh = new HealthFacility();
            kfh.setName("King Faisal Hospital");
            kfh.setFacilityType(FacilityType.HOSPITAL);
            kfh.setAddress("KG 544 St, Kigali, Rwanda");
            kfh.setPhoneNumber("+250788500000");
            kfh.setEmail("<EMAIL>");
            kfh.setLatitude(-1.9536);
            kfh.setLongitude(30.0606);
            kfh.setOperatingHours("24/7");
            kfh.setServicesOffered("Specialized Care, Surgery, Maternity, Family Planning, Cardiology");
            kfh.setIsActive(true);
            healthFacilityRepository.save(kfh);

            // Kimisagara Health Center
            HealthFacility kimisagara = new HealthFacility();
            kimisagara.setName("Kimisagara Health Center");
            kimisagara.setFacilityType(FacilityType.HEALTH_CENTER);
            kimisagara.setAddress("Kimisagara, Nyarugenge, Kigali");
            kimisagara.setPhoneNumber("+250788100000");
            kimisagara.setEmail("<EMAIL>");
            kimisagara.setLatitude(-1.9706);
            kimisagara.setLongitude(30.0588);
            kimisagara.setOperatingHours("Mon-Fri: 7:00-17:00, Sat: 8:00-12:00");
            kimisagara.setServicesOffered("Primary Care, Family Planning, Maternal Health, Vaccination");
            kimisagara.setIsActive(true);
            healthFacilityRepository.save(kimisagara);

            // Gahanga Health Center
            HealthFacility gahanga = new HealthFacility();
            gahanga.setName("Gahanga Health Center");
            gahanga.setFacilityType(FacilityType.HEALTH_CENTER);
            gahanga.setAddress("Gahanga, Kicukiro, Kigali");
            gahanga.setPhoneNumber("+250788200000");
            gahanga.setEmail("<EMAIL>");
            gahanga.setLatitude(-1.9897);
            gahanga.setLongitude(30.1026);
            gahanga.setOperatingHours("Mon-Fri: 7:00-17:00, Sat: 8:00-12:00");
            gahanga.setServicesOffered("Primary Care, Family Planning, Maternal Health, Child Health");
            gahanga.setIsActive(true);
            healthFacilityRepository.save(gahanga);

            // Remera Health Center
            HealthFacility remera = new HealthFacility();
            remera.setName("Remera Health Center");
            remera.setFacilityType(FacilityType.HEALTH_CENTER);
            remera.setAddress("Remera, Gasabo, Kigali");
            remera.setPhoneNumber("+250788300000");
            remera.setEmail("<EMAIL>");
            remera.setLatitude(-1.9353);
            remera.setLongitude(30.1059);
            remera.setOperatingHours("Mon-Fri: 7:00-17:00, Sat: 8:00-12:00");
            remera.setServicesOffered("Primary Care, Family Planning, HIV/AIDS Care, Mental Health");
            remera.setIsActive(true);
            healthFacilityRepository.save(remera);
        }
    }
}
