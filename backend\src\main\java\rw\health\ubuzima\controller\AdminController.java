package rw.health.ubuzima.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.dto.response.UserResponse;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.UserRole;
import rw.health.ubuzima.enums.UserStatus;
import rw.health.ubuzima.repository.UserRepository;
import rw.health.ubuzima.repository.HealthRecordRepository;
import rw.health.ubuzima.repository.AppointmentRepository;
import rw.health.ubuzima.repository.HealthFacilityRepository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class AdminController {

    private final UserRepository userRepository;
    private final HealthRecordRepository healthRecordRepository;
    private final AppointmentRepository appointmentRepository;
    private final HealthFacilityRepository healthFacilityRepository;

    // User Management
    @GetMapping("/users")
    public ResponseEntity<Map<String, Object>> getAllUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) UserRole role,
            @RequestParam(required = false) String search) {
        
        try {
            Pageable pageable = PageRequest.of(page, size);
            List<User> users;

            if (search != null && !search.isEmpty()) {
                users = userRepository.searchUsers(search);
            } else if (role != null) {
                users = userRepository.findByRole(role);
            } else {
                users = userRepository.findAll();
            }

            List<UserResponse> userResponses = users.stream()
                .map(this::convertToUserResponse)
                .collect(Collectors.toList());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("users", userResponses);
            response.put("total", users.size());
            response.put("page", page);
            response.put("size", size);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch users: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/users/{userId}")
    public ResponseEntity<Map<String, Object>> getUserById(@PathVariable Long userId) {
        try {
            User user = userRepository.findById(userId).orElse(null);
            
            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            UserResponse userResponse = convertToUserResponse(user);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "user", userResponse
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch user: " + e.getMessage()
            ));
        }
    }

    @PutMapping("/users/{userId}/status")
    public ResponseEntity<Map<String, Object>> updateUserStatus(
            @PathVariable Long userId,
            @RequestBody Map<String, String> request) {
        
        try {
            User user = userRepository.findById(userId).orElse(null);
            
            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            String statusStr = request.get("status");
            UserStatus status = UserStatus.valueOf(statusStr.toUpperCase());
            
            user.setStatus(status);
            userRepository.save(user);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "User status updated successfully"
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to update user status: " + e.getMessage()
            ));
        }
    }

    // Dashboard Statistics
    @GetMapping("/dashboard/stats")
    public ResponseEntity<Map<String, Object>> getDashboardStats() {
        try {
            long totalUsers = userRepository.count();
            long totalClients = userRepository.findByRole(UserRole.CLIENT).size();
            long totalHealthWorkers = userRepository.findByRole(UserRole.HEALTH_WORKER).size();
            long totalAdmins = userRepository.findByRole(UserRole.ADMIN).size();
            long totalHealthRecords = healthRecordRepository.count();
            long totalAppointments = appointmentRepository.count();
            long totalFacilities = healthFacilityRepository.count();

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalUsers", totalUsers);
            stats.put("totalClients", totalClients);
            stats.put("totalHealthWorkers", totalHealthWorkers);
            stats.put("totalAdmins", totalAdmins);
            stats.put("totalHealthRecords", totalHealthRecords);
            stats.put("totalAppointments", totalAppointments);
            stats.put("totalFacilities", totalFacilities);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "stats", stats
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch dashboard stats: " + e.getMessage()
            ));
        }
    }

    // Health Workers Management
    @GetMapping("/health-workers")
    public ResponseEntity<Map<String, Object>> getHealthWorkers() {
        try {
            List<User> healthWorkers = userRepository.findByRole(UserRole.HEALTH_WORKER);
            
            List<UserResponse> healthWorkerResponses = healthWorkers.stream()
                .map(this::convertToUserResponse)
                .collect(Collectors.toList());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "healthWorkers", healthWorkerResponses
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch health workers: " + e.getMessage()
            ));
        }
    }

    // System Health
    @GetMapping("/system/health")
    public ResponseEntity<Map<String, Object>> getSystemHealth() {
        try {
            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("timestamp", LocalDateTime.now());
            health.put("database", "Connected");
            health.put("activeUsers", userRepository.findByStatus(UserStatus.ACTIVE).size());

            return ResponseEntity.ok(Map.of(
                "success", true,
                "health", health
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "System health check failed: " + e.getMessage()
            ));
        }
    }

    private UserResponse convertToUserResponse(User user) {
        UserResponse response = new UserResponse();
        response.setId(Long.valueOf(user.getId().toString()));
        response.setName(user.getName());
        response.setEmail(user.getEmail());
        response.setPhone(user.getPhone());
        response.setRole(user.getRole());
        response.setFacilityId(user.getFacilityId());
        response.setDistrict(user.getDistrict());
        response.setSector(user.getSector());
        response.setCell(user.getCell());
        response.setVillage(user.getVillage());
        response.setCreatedAt(user.getCreatedAt());
        response.setLastLoginAt(user.getLastLoginAt());
        response.setActive(user.isActive());
        response.setProfileImageUrl(user.getProfilePictureUrl());
        return response;
    }
}
