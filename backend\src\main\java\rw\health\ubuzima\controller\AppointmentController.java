package rw.health.ubuzima.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.dto.request.AppointmentCreateRequest;
import rw.health.ubuzima.dto.request.AppointmentUpdateRequest;
import rw.health.ubuzima.dto.response.ApiResponseDto;
import rw.health.ubuzima.dto.response.AppointmentResponse;
import rw.health.ubuzima.entity.Appointment;
import rw.health.ubuzima.enums.AppointmentStatus;
import rw.health.ubuzima.enums.AppointmentType;
import rw.health.ubuzima.service.AppointmentService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * REST Controller for Appointment management operations
 * 
 * <AUTHOR> Development Team
 */
@Slf4j
@RestController
@RequestMapping("/appointments")
@RequiredArgsConstructor
@Tag(name = "Appointment Management", description = "APIs for managing appointments in the Ubuzima system")
public class AppointmentController {

    private final AppointmentService appointmentService;

    @Operation(summary = "Create a new appointment", description = "Creates a new appointment in the system")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Appointment created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "409", description = "Time slot conflict")
    })
    @PostMapping
    @PreAuthorize("hasRole('CLIENT') or hasRole('HEALTH_WORKER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<AppointmentResponse>> createAppointment(
            @Valid @RequestBody AppointmentCreateRequest request) {
        
        log.info("Creating new appointment for client: {}", request.getClientId());
        
        Appointment appointment = appointmentService.createAppointment(
            request.getClientId(),
            request.getFacilityId(),
            request.getHealthWorkerId(),
            request.getAppointmentDate(),
            request.getType(),
            request.getReason()
        );
        
        AppointmentResponse response = AppointmentResponse.fromEntity(appointment);
        
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponseDto.success("Appointment created successfully", response));
    }

    @Operation(summary = "Get appointment by ID", description = "Retrieves an appointment by its unique identifier")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Appointment found"),
        @ApiResponse(responseCode = "404", description = "Appointment not found")
    })
    @GetMapping("/{appointmentId}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('HEALTH_WORKER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<AppointmentResponse>> getAppointmentById(
            @Parameter(description = "Appointment ID") @PathVariable UUID appointmentId) {
        
        log.info("Retrieving appointment with ID: {}", appointmentId);
        
        Appointment appointment = appointmentService.getAppointmentById(appointmentId);
        AppointmentResponse response = AppointmentResponse.fromEntity(appointment);
        
        return ResponseEntity.ok(ApiResponseDto.success("Appointment retrieved successfully", response));
    }

    @Operation(summary = "Get all appointments", description = "Retrieves a paginated list of all appointments")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Appointments retrieved successfully")
    })
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('HEALTH_WORKER')")
    public ResponseEntity<ApiResponseDto<Page<AppointmentResponse>>> getAllAppointments(
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.info("Retrieving all appointments with pagination: {}", pageable);
        
        Page<Appointment> appointments = appointmentService.getAllAppointments(pageable);
        Page<AppointmentResponse> response = appointments.map(AppointmentResponse::fromEntity);
        
        return ResponseEntity.ok(ApiResponseDto.success("Appointments retrieved successfully", response));
    }

    @Operation(summary = "Update appointment", description = "Updates an existing appointment")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Appointment updated successfully"),
        @ApiResponse(responseCode = "404", description = "Appointment not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input data")
    })
    @PutMapping("/{appointmentId}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('HEALTH_WORKER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<AppointmentResponse>> updateAppointment(
            @Parameter(description = "Appointment ID") @PathVariable UUID appointmentId,
            @Valid @RequestBody AppointmentUpdateRequest request) {
        
        log.info("Updating appointment with ID: {}", appointmentId);
        
        Appointment appointmentUpdates = request.toEntity();
        Appointment updatedAppointment = appointmentService.updateAppointment(appointmentId, appointmentUpdates);
        AppointmentResponse response = AppointmentResponse.fromEntity(updatedAppointment);
        
        return ResponseEntity.ok(ApiResponseDto.success("Appointment updated successfully", response));
    }

    @Operation(summary = "Cancel appointment", description = "Cancels an existing appointment")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Appointment cancelled successfully"),
        @ApiResponse(responseCode = "404", description = "Appointment not found")
    })
    @PatchMapping("/{appointmentId}/cancel")
    @PreAuthorize("hasRole('CLIENT') or hasRole('HEALTH_WORKER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<Void>> cancelAppointment(
            @Parameter(description = "Appointment ID") @PathVariable UUID appointmentId,
            @RequestParam String reason,
            @RequestParam String cancelledBy) {
        
        log.info("Cancelling appointment with ID: {}", appointmentId);
        
        appointmentService.cancelAppointment(appointmentId, reason, cancelledBy);
        
        return ResponseEntity.ok(ApiResponseDto.success("Appointment cancelled successfully", null));
    }

    @Operation(summary = "Get appointments by client", description = "Retrieves appointments for a specific client")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Appointments retrieved successfully")
    })
    @GetMapping("/client/{clientId}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('HEALTH_WORKER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<Page<AppointmentResponse>>> getAppointmentsByClient(
            @Parameter(description = "Client ID") @PathVariable UUID clientId,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.info("Retrieving appointments for client: {}", clientId);
        
        Page<Appointment> appointments = appointmentService.getAppointmentsByClient(clientId, pageable);
        Page<AppointmentResponse> response = appointments.map(AppointmentResponse::fromEntity);
        
        return ResponseEntity.ok(ApiResponseDto.success("Appointments retrieved successfully", response));
    }

    @Operation(summary = "Get appointments by health worker", description = "Retrieves appointments for a specific health worker")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Appointments retrieved successfully")
    })
    @GetMapping("/health-worker/{healthWorkerId}")
    @PreAuthorize("hasRole('HEALTH_WORKER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<Page<AppointmentResponse>>> getAppointmentsByHealthWorker(
            @Parameter(description = "Health Worker ID") @PathVariable UUID healthWorkerId,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.info("Retrieving appointments for health worker: {}", healthWorkerId);
        
        Page<Appointment> appointments = appointmentService.getAppointmentsByHealthWorker(healthWorkerId, pageable);
        Page<AppointmentResponse> response = appointments.map(AppointmentResponse::fromEntity);
        
        return ResponseEntity.ok(ApiResponseDto.success("Appointments retrieved successfully", response));
    }

    @Operation(summary = "Get appointments by facility", description = "Retrieves appointments for a specific facility")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Appointments retrieved successfully")
    })
    @GetMapping("/facility/{facilityId}")
    @PreAuthorize("hasRole('HEALTH_WORKER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<Page<AppointmentResponse>>> getAppointmentsByFacility(
            @Parameter(description = "Facility ID") @PathVariable UUID facilityId,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.info("Retrieving appointments for facility: {}", facilityId);
        
        Page<Appointment> appointments = appointmentService.getAppointmentsByFacility(facilityId, pageable);
        Page<AppointmentResponse> response = appointments.map(AppointmentResponse::fromEntity);
        
        return ResponseEntity.ok(ApiResponseDto.success("Appointments retrieved successfully", response));
    }

    @Operation(summary = "Update appointment status", description = "Updates an appointment's status")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Appointment status updated successfully"),
        @ApiResponse(responseCode = "404", description = "Appointment not found")
    })
    @PatchMapping("/{appointmentId}/status")
    @PreAuthorize("hasRole('HEALTH_WORKER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<AppointmentResponse>> updateAppointmentStatus(
            @Parameter(description = "Appointment ID") @PathVariable UUID appointmentId,
            @Parameter(description = "New status") @RequestParam AppointmentStatus status) {
        
        log.info("Updating status for appointment ID: {} to {}", appointmentId, status);
        
        Appointment updatedAppointment = appointmentService.updateAppointmentStatus(appointmentId, status);
        AppointmentResponse response = AppointmentResponse.fromEntity(updatedAppointment);
        
        return ResponseEntity.ok(ApiResponseDto.success("Appointment status updated successfully", response));
    }

    @Operation(summary = "Reschedule appointment", description = "Reschedules an appointment to a new date and time")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Appointment rescheduled successfully"),
        @ApiResponse(responseCode = "404", description = "Appointment not found"),
        @ApiResponse(responseCode = "409", description = "Time slot conflict")
    })
    @PatchMapping("/{appointmentId}/reschedule")
    @PreAuthorize("hasRole('CLIENT') or hasRole('HEALTH_WORKER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<AppointmentResponse>> rescheduleAppointment(
            @Parameter(description = "Appointment ID") @PathVariable UUID appointmentId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime newDate,
            @RequestParam String reason) {
        
        log.info("Rescheduling appointment ID: {} to new date: {}", appointmentId, newDate);
        
        Appointment rescheduledAppointment = appointmentService.rescheduleAppointment(appointmentId, newDate, reason);
        AppointmentResponse response = AppointmentResponse.fromEntity(rescheduledAppointment);
        
        return ResponseEntity.ok(ApiResponseDto.success("Appointment rescheduled successfully", response));
    }

    @Operation(summary = "Get available time slots", description = "Retrieves available time slots for a health worker on a specific date")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Available time slots retrieved successfully")
    })
    @GetMapping("/available-slots")
    @PreAuthorize("hasRole('CLIENT') or hasRole('HEALTH_WORKER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<List<String>>> getAvailableTimeSlots(
            @RequestParam UUID healthWorkerId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        
        log.info("Retrieving available time slots for health worker: {} on date: {}", healthWorkerId, date);
        
        List<String> availableSlots = appointmentService.getAvailableTimeSlots(healthWorkerId, date);
        
        return ResponseEntity.ok(ApiResponseDto.success("Available time slots retrieved successfully", availableSlots));
    }

    @Operation(summary = "Get upcoming appointments", description = "Retrieves upcoming appointments for a client")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Upcoming appointments retrieved successfully")
    })
    @GetMapping("/client/{clientId}/upcoming")
    @PreAuthorize("hasRole('CLIENT') or hasRole('HEALTH_WORKER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<List<AppointmentResponse>>> getUpcomingAppointmentsByClient(
            @Parameter(description = "Client ID") @PathVariable UUID clientId) {
        
        log.info("Retrieving upcoming appointments for client: {}", clientId);
        
        List<Appointment> appointments = appointmentService.getUpcomingAppointmentsByClient(clientId);
        List<AppointmentResponse> response = appointments.stream()
                .map(AppointmentResponse::fromEntity)
                .toList();
        
        return ResponseEntity.ok(ApiResponseDto.success("Upcoming appointments retrieved successfully", response));
    }

    @Operation(summary = "Get today's appointments", description = "Retrieves today's appointments for a health worker")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Today's appointments retrieved successfully")
    })
    @GetMapping("/health-worker/{healthWorkerId}/today")
    @PreAuthorize("hasRole('HEALTH_WORKER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponseDto<List<AppointmentResponse>>> getTodaysAppointmentsByHealthWorker(
            @Parameter(description = "Health Worker ID") @PathVariable UUID healthWorkerId) {
        
        log.info("Retrieving today's appointments for health worker: {}", healthWorkerId);
        
        List<Appointment> appointments = appointmentService.getTodaysAppointmentsByHealthWorker(healthWorkerId);
        List<AppointmentResponse> response = appointments.stream()
                .map(AppointmentResponse::fromEntity)
                .toList();
        
        return ResponseEntity.ok(ApiResponseDto.success("Today's appointments retrieved successfully", response));
    }

    @Operation(summary = "Get appointment statistics", description = "Retrieves appointment statistics")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully")
    })
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN') or hasRole('HEALTH_WORKER')")
    public ResponseEntity<ApiResponseDto<AppointmentStatistics>> getAppointmentStatistics() {
        
        log.info("Retrieving appointment statistics");
        
        AppointmentStatistics stats = AppointmentStatistics.builder()
                .totalAppointments(appointmentService.getTotalAppointmentCount())
                .scheduledAppointments(appointmentService.getAppointmentCountByStatus(AppointmentStatus.SCHEDULED))
                .completedAppointments(appointmentService.getAppointmentCountByStatus(AppointmentStatus.COMPLETED))
                .cancelledAppointments(appointmentService.getAppointmentCountByStatus(AppointmentStatus.CANCELLED))
                .build();
        
        return ResponseEntity.ok(ApiResponseDto.success("Statistics retrieved successfully", stats));
    }

    // Inner class for statistics response
    public static class AppointmentStatistics {
        private long totalAppointments;
        private long scheduledAppointments;
        private long completedAppointments;
        private long cancelledAppointments;

        public static AppointmentStatisticsBuilder builder() {
            return new AppointmentStatisticsBuilder();
        }

        public static class AppointmentStatisticsBuilder {
            private long totalAppointments;
            private long scheduledAppointments;
            private long completedAppointments;
            private long cancelledAppointments;

            public AppointmentStatisticsBuilder totalAppointments(long totalAppointments) {
                this.totalAppointments = totalAppointments;
                return this;
            }

            public AppointmentStatisticsBuilder scheduledAppointments(long scheduledAppointments) {
                this.scheduledAppointments = scheduledAppointments;
                return this;
            }

            public AppointmentStatisticsBuilder completedAppointments(long completedAppointments) {
                this.completedAppointments = completedAppointments;
                return this;
            }

            public AppointmentStatisticsBuilder cancelledAppointments(long cancelledAppointments) {
                this.cancelledAppointments = cancelledAppointments;
                return this;
            }

            public AppointmentStatistics build() {
                AppointmentStatistics stats = new AppointmentStatistics();
                stats.totalAppointments = this.totalAppointments;
                stats.scheduledAppointments = this.scheduledAppointments;
                stats.completedAppointments = this.completedAppointments;
                stats.cancelledAppointments = this.cancelledAppointments;
                return stats;
            }
        }

        // Getters
        public long getTotalAppointments() { return totalAppointments; }
        public long getScheduledAppointments() { return scheduledAppointments; }
        public long getCompletedAppointments() { return completedAppointments; }
        public long getCancelledAppointments() { return cancelledAppointments; }
    }
}
