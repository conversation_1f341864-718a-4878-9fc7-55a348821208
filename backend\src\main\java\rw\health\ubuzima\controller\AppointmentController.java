package rw.health.ubuzima.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.entity.Appointment;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.entity.HealthFacility;
import rw.health.ubuzima.entity.TimeSlot;
import rw.health.ubuzima.enums.AppointmentStatus;
import rw.health.ubuzima.enums.AppointmentType;
import rw.health.ubuzima.repository.AppointmentRepository;
import rw.health.ubuzima.repository.UserRepository;
import rw.health.ubuzima.repository.HealthFacilityRepository;
import rw.health.ubuzima.repository.TimeSlotRepository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/appointments")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class AppointmentController {

    private final AppointmentRepository appointmentRepository;
    private final UserRepository userRepository;
    private final HealthFacilityRepository healthFacilityRepository;
    private final TimeSlotRepository timeSlotRepository;

    @GetMapping
    public ResponseEntity<Map<String, Object>> getAppointments(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long userId) {
        
        try {
            List<Appointment> appointments;
            
            if (userId != null) {
                User user = userRepository.findById(userId).orElse(null);
                if (user == null) {
                    return ResponseEntity.badRequest().body(Map.of(
                        "success", false,
                        "message", "User not found"
                    ));
                }
                appointments = appointmentRepository.findByUserOrderByScheduledDateDesc(user);
            } else {
                Pageable pageable = PageRequest.of(page, limit);
                appointments = appointmentRepository.findAll(pageable).getContent();
            }

            if (status != null) {
                AppointmentStatus appointmentStatus = AppointmentStatus.valueOf(status.toUpperCase());
                appointments = appointments.stream()
                    .filter(apt -> apt.getStatus() == appointmentStatus)
                    .toList();
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "appointments", appointments,
                "total", appointments.size()
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch appointments: " + e.getMessage()
            ));
        }
    }

    @PostMapping
    public ResponseEntity<Map<String, Object>> createAppointment(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            Long facilityId = Long.valueOf(request.get("facilityId").toString());
            
            User user = userRepository.findById(userId).orElse(null);
            HealthFacility facility = healthFacilityRepository.findById(facilityId).orElse(null);
            
            if (user == null || facility == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "User or facility not found"
                ));
            }

            Appointment appointment = new Appointment();
            appointment.setUser(user);
            appointment.setHealthFacility(facility);
            appointment.setScheduledDate(LocalDateTime.parse(request.get("scheduledDate").toString()));
            appointment.setAppointmentType(AppointmentType.valueOf(request.get("appointmentType").toString().toUpperCase()));
            appointment.setStatus(AppointmentStatus.SCHEDULED);
            
            if (request.get("reason") != null) {
                appointment.setReason(request.get("reason").toString());
            }
            
            if (request.get("notes") != null) {
                appointment.setNotes(request.get("notes").toString());
            }
            
            if (request.get("healthWorkerId") != null) {
                Long healthWorkerId = Long.valueOf(request.get("healthWorkerId").toString());
                User healthWorker = userRepository.findById(healthWorkerId).orElse(null);
                appointment.setHealthWorker(healthWorker);
            }

            Appointment savedAppointment = appointmentRepository.save(appointment);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Appointment created successfully",
                "appointment", savedAppointment
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to create appointment: " + e.getMessage()
            ));
        }
    }

    @GET("/{id}")
    public ResponseEntity<Map<String, Object>> getAppointment(@PathVariable Long id) {
        try {
            Appointment appointment = appointmentRepository.findById(id).orElse(null);
            
            if (appointment == null) {
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "appointment", appointment
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch appointment: " + e.getMessage()
            ));
        }
    }

    @PUT("/{id}")
    public ResponseEntity<Map<String, Object>> updateAppointment(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request) {
        
        try {
            Appointment appointment = appointmentRepository.findById(id).orElse(null);
            
            if (appointment == null) {
                return ResponseEntity.notFound().build();
            }

            if (request.get("scheduledDate") != null) {
                appointment.setScheduledDate(LocalDateTime.parse(request.get("scheduledDate").toString()));
            }
            
            if (request.get("status") != null) {
                appointment.setStatus(AppointmentStatus.valueOf(request.get("status").toString().toUpperCase()));
            }
            
            if (request.get("notes") != null) {
                appointment.setNotes(request.get("notes").toString());
            }

            Appointment updatedAppointment = appointmentRepository.save(appointment);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Appointment updated successfully",
                "appointment", updatedAppointment
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to update appointment: " + e.getMessage()
            ));
        }
    }

    @DELETE("/{id}")
    public ResponseEntity<Map<String, Object>> cancelAppointment(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request) {
        
        try {
            Appointment appointment = appointmentRepository.findById(id).orElse(null);
            
            if (appointment == null) {
                return ResponseEntity.notFound().build();
            }

            appointment.setStatus(AppointmentStatus.CANCELLED);
            appointment.setCancelledAt(LocalDateTime.now());
            
            if (request.get("reason") != null) {
                appointment.setCancellationReason(request.get("reason").toString());
            }

            appointmentRepository.save(appointment);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Appointment cancelled successfully"
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to cancel appointment: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/available-slots")
    public ResponseEntity<Map<String, Object>> getAvailableSlots(
            @RequestParam String facilityId,
            @RequestParam(required = false) String healthWorkerId,
            @RequestParam String date) {
        
        try {
            HealthFacility facility = healthFacilityRepository.findById(Long.valueOf(facilityId)).orElse(null);
            
            if (facility == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Facility not found"
                ));
            }

            LocalDate requestedDate = LocalDate.parse(date);
            LocalDateTime startOfDay = requestedDate.atStartOfDay();
            LocalDateTime endOfDay = requestedDate.atTime(LocalTime.MAX);

            List<TimeSlot> availableSlots;
            
            if (healthWorkerId != null) {
                User healthWorker = userRepository.findById(Long.valueOf(healthWorkerId)).orElse(null);
                if (healthWorker == null) {
                    return ResponseEntity.badRequest().body(Map.of(
                        "success", false,
                        "message", "Health worker not found"
                    ));
                }
                availableSlots = timeSlotRepository.findAvailableSlots(facility, healthWorker, startOfDay, endOfDay);
            } else {
                availableSlots = timeSlotRepository.findAvailableSlotsByFacility(facility, startOfDay, endOfDay);
            }

            // If no time slots exist, generate default ones
            if (availableSlots.isEmpty()) {
                availableSlots = generateDefaultTimeSlots(facility, requestedDate);
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "timeSlots", availableSlots
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch available slots: " + e.getMessage()
            ));
        }
    }

    private List<TimeSlot> generateDefaultTimeSlots(HealthFacility facility, LocalDate date) {
        List<TimeSlot> slots = new ArrayList<>();
        
        // Generate slots from 8 AM to 5 PM, every 30 minutes
        for (int hour = 8; hour < 17; hour++) {
            for (int minute = 0; minute < 60; minute += 30) {
                LocalDateTime startTime = date.atTime(hour, minute);
                LocalDateTime endTime = startTime.plusMinutes(30);
                
                TimeSlot slot = new TimeSlot();
                slot.setHealthFacility(facility);
                slot.setStartTime(startTime);
                slot.setEndTime(endTime);
                slot.setIsAvailable(true);
                
                slots.add(slot);
            }
        }
        
        return slots;
    }
}
