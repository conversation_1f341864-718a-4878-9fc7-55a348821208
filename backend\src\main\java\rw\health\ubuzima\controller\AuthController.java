package rw.health.ubuzima.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.dto.request.LoginRequest;
import rw.health.ubuzima.dto.request.UserCreateRequest;
import rw.health.ubuzima.dto.response.ApiResponseDto;
import rw.health.ubuzima.dto.response.JwtAuthenticationResponse;
import rw.health.ubuzima.dto.response.UserResponse;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.security.JwtTokenProvider;
import rw.health.ubuzima.service.UserService;

/**
 * Authentication Controller for login, registration, and token management
 * 
 * <AUTHOR> Development Team
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Tag(name = "Authentication", description = "APIs for user authentication and registration")
public class AuthController {

    private final AuthenticationManager authenticationManager;
    private final UserService userService;
    private final JwtTokenProvider tokenProvider;

    @Operation(summary = "User login", description = "Authenticates a user and returns JWT tokens")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Login successful"),
        @ApiResponse(responseCode = "401", description = "Invalid credentials"),
        @ApiResponse(responseCode = "423", description = "Account locked")
    })
    @PostMapping("/login")
    public ResponseEntity<ApiResponseDto<JwtAuthenticationResponse>> login(
            @Valid @RequestBody LoginRequest loginRequest) {
        
        log.info("Login attempt for user: {}", loginRequest.getUsername());
        
        try {
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    loginRequest.getUsername(),
                    loginRequest.getPassword()
                )
            );

            SecurityContextHolder.getContext().setAuthentication(authentication);

            String jwt = tokenProvider.generateToken(authentication);
            String refreshToken = tokenProvider.generateRefreshToken(loginRequest.getUsername());

            // Update last login time
            User user = userService.findActiveUserByEmailOrPhoneNumber(loginRequest.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            
            userService.updateLastLogin(user.getId());
            userService.resetLoginAttempts(user.getId());

            JwtAuthenticationResponse response = JwtAuthenticationResponse.builder()
                    .accessToken(jwt)
                    .refreshToken(refreshToken)
                    .tokenType("Bearer")
                    .expiresIn(tokenProvider.getRemainingValidityTime(jwt))
                    .user(UserResponse.fromEntity(user))
                    .build();

            log.info("Successful login for user: {}", loginRequest.getUsername());
            return ResponseEntity.ok(ApiResponseDto.success("Login successful", response));
            
        } catch (Exception e) {
            log.error("Login failed for user: {}", loginRequest.getUsername(), e);
            
            // Increment login attempts for existing users
            userService.findByEmailOrPhoneNumber(loginRequest.getUsername())
                    .ifPresent(user -> userService.incrementLoginAttempts(user.getId()));
            
            return ResponseEntity.status(401)
                    .body(ApiResponseDto.error("Invalid username or password", 401));
        }
    }

    @Operation(summary = "User registration", description = "Registers a new user in the system")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Registration successful"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "409", description = "User already exists")
    })
    @PostMapping("/register")
    public ResponseEntity<ApiResponseDto<UserResponse>> register(
            @Valid @RequestBody UserCreateRequest registerRequest) {
        
        log.info("Registration attempt for email: {}", registerRequest.getEmail());
        
        try {
            User user = userService.createUser(
                registerRequest.getFirstName(),
                registerRequest.getLastName(),
                registerRequest.getEmail(),
                registerRequest.getPhoneNumber(),
                registerRequest.getPassword(),
                registerRequest.getRole()
            );

            UserResponse response = UserResponse.fromEntity(user);
            
            log.info("Successful registration for user: {}", user.getEmail());
            return ResponseEntity.status(201)
                    .body(ApiResponseDto.success("Registration successful", response));
                    
        } catch (Exception e) {
            log.error("Registration failed for email: {}", registerRequest.getEmail(), e);
            return ResponseEntity.status(400)
                    .body(ApiResponseDto.error(e.getMessage(), 400));
        }
    }

    @Operation(summary = "Refresh token", description = "Refreshes an expired access token using refresh token")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Token refreshed successfully"),
        @ApiResponse(responseCode = "401", description = "Invalid refresh token")
    })
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponseDto<JwtAuthenticationResponse>> refreshToken(
            @RequestParam String refreshToken) {
        
        log.info("Token refresh attempt");
        
        try {
            if (!tokenProvider.validateToken(refreshToken) || !tokenProvider.isRefreshToken(refreshToken)) {
                return ResponseEntity.status(401)
                        .body(ApiResponseDto.error("Invalid refresh token", 401));
            }

            String username = tokenProvider.getUsernameFromToken(refreshToken);
            User user = userService.findActiveUserByEmailOrPhoneNumber(username)
                    .orElseThrow(() -> new RuntimeException("User not found"));

            String newAccessToken = tokenProvider.generateTokenFromUsername(username);
            String newRefreshToken = tokenProvider.generateRefreshToken(username);

            JwtAuthenticationResponse response = JwtAuthenticationResponse.builder()
                    .accessToken(newAccessToken)
                    .refreshToken(newRefreshToken)
                    .tokenType("Bearer")
                    .expiresIn(tokenProvider.getRemainingValidityTime(newAccessToken))
                    .user(UserResponse.fromEntity(user))
                    .build();

            log.info("Token refreshed successfully for user: {}", username);
            return ResponseEntity.ok(ApiResponseDto.success("Token refreshed successfully", response));
            
        } catch (Exception e) {
            log.error("Token refresh failed", e);
            return ResponseEntity.status(401)
                    .body(ApiResponseDto.error("Token refresh failed", 401));
        }
    }

    @Operation(summary = "Logout", description = "Logs out the current user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Logout successful")
    })
    @PostMapping("/logout")
    public ResponseEntity<ApiResponseDto<Void>> logout() {
        SecurityContextHolder.clearContext();
        log.info("User logged out successfully");
        return ResponseEntity.ok(ApiResponseDto.success("Logout successful"));
    }

    @Operation(summary = "Get current user", description = "Returns the currently authenticated user's information")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User information retrieved"),
        @ApiResponse(responseCode = "401", description = "Not authenticated")
    })
    @GetMapping("/me")
    public ResponseEntity<ApiResponseDto<UserResponse>> getCurrentUser(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(401)
                    .body(ApiResponseDto.error("Not authenticated", 401));
        }

        String username = authentication.getName();
        User user = userService.findActiveUserByEmailOrPhoneNumber(username)
                .orElseThrow(() -> new RuntimeException("User not found"));

        UserResponse response = UserResponse.fromEntity(user);
        return ResponseEntity.ok(ApiResponseDto.success("User information retrieved", response));
    }
}
