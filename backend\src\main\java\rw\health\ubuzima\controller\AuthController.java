package rw.health.ubuzima.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.dto.request.UserCreateRequest;
import rw.health.ubuzima.dto.response.UserResponse;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.UserStatus;
import rw.health.ubuzima.repository.UserRepository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class AuthController {

    private final UserRepository userRepository;

    @PostMapping("/register")
    public ResponseEntity<Map<String, Object>> register(@Valid @RequestBody UserCreateRequest request) {
        try {
            // Check if user already exists
            if (userRepository.existsByEmail(request.getEmail())) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Email already exists"
                ));
            }

            if (userRepository.existsByPhone(request.getPhone())) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Phone number already exists"
                ));
            }

            // Create new user
            User user = new User();
            user.setName(request.getName());
            user.setEmail(request.getEmail());
            user.setPhone(request.getPhone());
            user.setPasswordHash(request.getPassword()); // In real app, hash this
            user.setRole(request.getRole());
            user.setStatus(UserStatus.ACTIVE);
            user.setFacilityId(request.getFacilityId());
            user.setDistrict(request.getDistrict());
            user.setSector(request.getSector());
            user.setCell(request.getCell());
            user.setVillage(request.getVillage());
            user.setEmailVerified(false);
            user.setPhoneVerified(false);

            User savedUser = userRepository.save(user);

            // Create response
            UserResponse userResponse = new UserResponse();
            userResponse.setId(savedUser.getId().toString());
            userResponse.setName(savedUser.getName());
            userResponse.setEmail(savedUser.getEmail());
            userResponse.setPhone(savedUser.getPhone());
            userResponse.setRole(savedUser.getRole());
            userResponse.setFacilityId(savedUser.getFacilityId());
            userResponse.setDistrict(savedUser.getDistrict());
            userResponse.setSector(savedUser.getSector());
            userResponse.setCell(savedUser.getCell());
            userResponse.setVillage(savedUser.getVillage());
            userResponse.setCreatedAt(savedUser.getCreatedAt());
            userResponse.setLastLoginAt(savedUser.getLastLoginAt());
            userResponse.setActive(savedUser.isActive());
            userResponse.setProfileImageUrl(savedUser.getProfilePictureUrl());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "User registered successfully");
            response.put("user", userResponse);
            response.put("token", "mock-jwt-token"); // In real app, generate JWT

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Registration failed: " + e.getMessage()
            ));
        }
    }

    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(@RequestBody Map<String, String> loginRequest) {
        try {
            String email = loginRequest.get("email");
            String password = loginRequest.get("password");

            if (email == null || password == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Email and password are required"
                ));
            }

            // Find user by email
            User user = userRepository.findByEmail(email).orElse(null);
            
            if (user == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Invalid email or password"
                ));
            }

            // In real app, verify password hash
            if (!user.getPasswordHash().equals(password)) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Invalid email or password"
                ));
            }

            // Update last login
            user.setLastLoginAt(java.time.LocalDate.now());
            userRepository.save(user);

            // Create response
            UserResponse userResponse = new UserResponse();
            userResponse.setId(user.getId().toString());
            userResponse.setName(user.getName());
            userResponse.setEmail(user.getEmail());
            userResponse.setPhone(user.getPhone());
            userResponse.setRole(user.getRole());
            userResponse.setFacilityId(user.getFacilityId());
            userResponse.setDistrict(user.getDistrict());
            userResponse.setSector(user.getSector());
            userResponse.setCell(user.getCell());
            userResponse.setVillage(user.getVillage());
            userResponse.setCreatedAt(user.getCreatedAt());
            userResponse.setLastLoginAt(user.getLastLoginAt());
            userResponse.setActive(user.isActive());
            userResponse.setProfileImageUrl(user.getProfilePictureUrl());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Login successful");
            response.put("user", userResponse);
            response.put("token", "mock-jwt-token"); // In real app, generate JWT

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Login failed: " + e.getMessage()
            ));
        }
    }
}
