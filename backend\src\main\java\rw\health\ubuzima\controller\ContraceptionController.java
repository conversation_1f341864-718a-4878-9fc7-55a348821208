package rw.health.ubuzima.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.entity.ContraceptionMethod;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.ContraceptionType;
import rw.health.ubuzima.repository.ContraceptionMethodRepository;
import rw.health.ubuzima.repository.UserRepository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/contraception")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class ContraceptionController {

    private final ContraceptionMethodRepository contraceptionMethodRepository;
    private final UserRepository userRepository;

    @GetMapping
    public ResponseEntity<Map<String, Object>> getContraceptionMethods(@RequestParam(required = false) Long userId) {
        try {
            List<ContraceptionMethod> methods;
            
            if (userId != null) {
                User user = userRepository.findById(userId).orElse(null);
                if (user == null) {
                    return ResponseEntity.badRequest().body(Map.of(
                        "success", false,
                        "message", "User not found"
                    ));
                }
                methods = contraceptionMethodRepository.findByUserOrderByStartDateDesc(user);
            } else {
                methods = contraceptionMethodRepository.findAll();
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "contraceptionMethods", methods
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch contraception methods: " + e.getMessage()
            ));
        }
    }

    @PostMapping
    public ResponseEntity<Map<String, Object>> createContraceptionMethod(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            User user = userRepository.findById(userId).orElse(null);
            
            if (user == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "User not found"
                ));
            }

            ContraceptionMethod method = new ContraceptionMethod();
            method.setUser(user);
            method.setType(ContraceptionType.valueOf(request.get("type").toString().toUpperCase()));
            method.setName(request.get("name").toString());
            method.setStartDate(LocalDate.parse(request.get("startDate").toString()));
            
            if (request.get("description") != null) {
                method.setDescription(request.get("description").toString());
            }
            
            if (request.get("endDate") != null) {
                method.setEndDate(LocalDate.parse(request.get("endDate").toString()));
            }
            
            if (request.get("effectiveness") != null) {
                method.setEffectiveness(Double.valueOf(request.get("effectiveness").toString()));
            }
            
            if (request.get("instructions") != null) {
                method.setInstructions(request.get("instructions").toString());
            }
            
            if (request.get("prescribedBy") != null) {
                method.setPrescribedBy(request.get("prescribedBy").toString());
            }
            
            if (request.get("nextAppointment") != null) {
                method.setNextAppointment(LocalDate.parse(request.get("nextAppointment").toString()));
            }

            ContraceptionMethod savedMethod = contraceptionMethodRepository.save(method);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Contraception method created successfully",
                "contraceptionMethod", savedMethod
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to create contraception method: " + e.getMessage()
            ));
        }
    }

    @PUT("/{id}")
    public ResponseEntity<Map<String, Object>> updateContraceptionMethod(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request) {
        
        try {
            ContraceptionMethod method = contraceptionMethodRepository.findById(id).orElse(null);
            
            if (method == null) {
                return ResponseEntity.notFound().build();
            }

            if (request.get("endDate") != null) {
                method.setEndDate(LocalDate.parse(request.get("endDate").toString()));
            }
            
            if (request.get("isActive") != null) {
                method.setIsActive(Boolean.valueOf(request.get("isActive").toString()));
            }
            
            if (request.get("nextAppointment") != null) {
                method.setNextAppointment(LocalDate.parse(request.get("nextAppointment").toString()));
            }
            
            if (request.get("instructions") != null) {
                method.setInstructions(request.get("instructions").toString());
            }

            ContraceptionMethod updatedMethod = contraceptionMethodRepository.save(method);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Contraception method updated successfully",
                "contraceptionMethod", updatedMethod
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to update contraception method: " + e.getMessage()
            ));
        }
    }

    @DELETE("/{id}")
    public ResponseEntity<Map<String, Object>> deleteContraceptionMethod(@PathVariable Long id) {
        try {
            ContraceptionMethod method = contraceptionMethodRepository.findById(id).orElse(null);
            
            if (method == null) {
                return ResponseEntity.notFound().build();
            }

            contraceptionMethodRepository.delete(method);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Contraception method deleted successfully"
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to delete contraception method: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/active")
    public ResponseEntity<Map<String, Object>> getActiveContraceptionMethod(@RequestParam Long userId) {
        try {
            User user = userRepository.findById(userId).orElse(null);
            
            if (user == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "User not found"
                ));
            }

            ContraceptionMethod activeMethod = contraceptionMethodRepository
                .findByUserAndIsActiveTrue(user).orElse(null);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "activeMethod", activeMethod
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch active contraception method: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/types")
    public ResponseEntity<Map<String, Object>> getContraceptionTypes() {
        try {
            ContraceptionType[] types = ContraceptionType.values();
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "contraceptionTypes", types
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch contraception types: " + e.getMessage()
            ));
        }
    }
}
