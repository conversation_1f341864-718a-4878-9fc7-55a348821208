package rw.health.ubuzima.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.entity.HealthRecord;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.RecordType;
import rw.health.ubuzima.repository.HealthRecordRepository;
import rw.health.ubuzima.repository.UserRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/health-records")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class HealthRecordController {

    private final HealthRecordRepository healthRecordRepository;
    private final UserRepository userRepository;

    @GetMapping
    public ResponseEntity<Map<String, Object>> getHealthRecords(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String recordType) {
        
        try {
            List<HealthRecord> healthRecords;
            
            if (userId != null) {
                User user = userRepository.findById(userId).orElse(null);
                if (user == null) {
                    return ResponseEntity.badRequest().body(Map.of(
                        "success", false,
                        "message", "User not found"
                    ));
                }
                
                if (recordType != null) {
                    RecordType type = RecordType.valueOf(recordType.toUpperCase());
                    healthRecords = healthRecordRepository.findByUserAndRecordTypeOrderByRecordedAtDesc(user, type);
                } else {
                    healthRecords = healthRecordRepository.findByUserOrderByRecordedAtDesc(user);
                }
            } else {
                Pageable pageable = PageRequest.of(page, limit);
                healthRecords = healthRecordRepository.findAll(pageable).getContent();
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "healthRecords", healthRecords,
                "total", healthRecords.size()
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch health records: " + e.getMessage()
            ));
        }
    }

    @PostMapping
    public ResponseEntity<Map<String, Object>> createHealthRecord(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            User user = userRepository.findById(userId).orElse(null);
            
            if (user == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "User not found"
                ));
            }

            HealthRecord healthRecord = new HealthRecord();
            healthRecord.setUser(user);
            healthRecord.setRecordType(RecordType.valueOf(request.get("recordType").toString().toUpperCase()));
            healthRecord.setValue(request.get("value").toString());
            
            if (request.get("unit") != null) {
                healthRecord.setUnit(request.get("unit").toString());
            }
            
            if (request.get("notes") != null) {
                healthRecord.setNotes(request.get("notes").toString());
            }
            
            if (request.get("recordedBy") != null) {
                healthRecord.setRecordedBy(request.get("recordedBy").toString());
            } else {
                healthRecord.setRecordedBy("Self-reported");
            }
            
            healthRecord.setRecordedAt(LocalDateTime.now());
            healthRecord.setIsVerified(false);

            HealthRecord savedRecord = healthRecordRepository.save(healthRecord);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Health record created successfully",
                "healthRecord", savedRecord
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to create health record: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getHealthRecord(@PathVariable Long id) {
        try {
            HealthRecord healthRecord = healthRecordRepository.findById(id).orElse(null);
            
            if (healthRecord == null) {
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "healthRecord", healthRecord
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch health record: " + e.getMessage()
            ));
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateHealthRecord(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request) {
        
        try {
            HealthRecord healthRecord = healthRecordRepository.findById(id).orElse(null);
            
            if (healthRecord == null) {
                return ResponseEntity.notFound().build();
            }

            if (request.get("value") != null) {
                healthRecord.setValue(request.get("value").toString());
            }
            
            if (request.get("unit") != null) {
                healthRecord.setUnit(request.get("unit").toString());
            }
            
            if (request.get("notes") != null) {
                healthRecord.setNotes(request.get("notes").toString());
            }
            
            if (request.get("isVerified") != null) {
                healthRecord.setIsVerified(Boolean.valueOf(request.get("isVerified").toString()));
            }

            HealthRecord updatedRecord = healthRecordRepository.save(healthRecord);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Health record updated successfully",
                "healthRecord", updatedRecord
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to update health record: " + e.getMessage()
            ));
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteHealthRecord(@PathVariable Long id) {
        try {
            HealthRecord healthRecord = healthRecordRepository.findById(id).orElse(null);
            
            if (healthRecord == null) {
                return ResponseEntity.notFound().build();
            }

            healthRecordRepository.delete(healthRecord);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Health record deleted successfully"
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to delete health record: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getHealthStatistics(@RequestParam Long userId) {
        try {
            User user = userRepository.findById(userId).orElse(null);
            
            if (user == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "User not found"
                ));
            }

            List<HealthRecord> allRecords = healthRecordRepository.findByUser(user);
            
            // Calculate basic statistics
            long totalRecords = allRecords.size();
            long verifiedRecords = allRecords.stream()
                .mapToLong(record -> record.getIsVerified() ? 1 : 0)
                .sum();
            
            // Group by record type
            Map<RecordType, Long> recordsByType = allRecords.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    HealthRecord::getRecordType,
                    java.util.stream.Collectors.counting()
                ));

            return ResponseEntity.ok(Map.of(
                "success", true,
                "statistics", Map.of(
                    "totalRecords", totalRecords,
                    "verifiedRecords", verifiedRecords,
                    "recordsByType", recordsByType
                )
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch health statistics: " + e.getMessage()
            ));
        }
    }
}
