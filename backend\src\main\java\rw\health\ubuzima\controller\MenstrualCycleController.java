package rw.health.ubuzima.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.entity.MenstrualCycle;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.repository.MenstrualCycleRepository;
import rw.health.ubuzima.repository.UserRepository;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/menstrual-cycles")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class MenstrualCycleController {

    private final MenstrualCycleRepository menstrualCycleRepository;
    private final UserRepository userRepository;

    @GetMapping
    public ResponseEntity<Map<String, Object>> getMenstrualCycles(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(required = false) Long userId) {
        
        try {
            User user = null;
            if (userId != null) {
                user = userRepository.findById(userId).orElse(null);
                if (user == null) {
                    return ResponseEntity.badRequest().body(Map.of(
                        "success", false,
                        "message", "User not found"
                    ));
                }
            }

            List<MenstrualCycle> cycles;
            if (user != null) {
                cycles = menstrualCycleRepository.findByUserOrderByStartDateDesc(user);
            } else {
                Pageable pageable = PageRequest.of(page, limit);
                cycles = menstrualCycleRepository.findAll(pageable).getContent();
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "cycles", cycles,
                "total", cycles.size()
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch menstrual cycles: " + e.getMessage()
            ));
        }
    }

    @PostMapping
    public ResponseEntity<Map<String, Object>> createMenstrualCycle(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            User user = userRepository.findById(userId).orElse(null);
            
            if (user == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "User not found"
                ));
            }

            MenstrualCycle cycle = new MenstrualCycle();
            cycle.setUser(user);
            cycle.setStartDate(LocalDate.parse(request.get("startDate").toString()));
            
            if (request.get("endDate") != null) {
                cycle.setEndDate(LocalDate.parse(request.get("endDate").toString()));
            }
            
            if (request.get("cycleLength") != null) {
                cycle.setCycleLength(Integer.valueOf(request.get("cycleLength").toString()));
            }
            
            if (request.get("flowDuration") != null) {
                cycle.setFlowDuration(Integer.valueOf(request.get("flowDuration").toString()));
            }
            
            if (request.get("notes") != null) {
                cycle.setNotes(request.get("notes").toString());
            }

            MenstrualCycle savedCycle = menstrualCycleRepository.save(cycle);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Menstrual cycle created successfully",
                "cycle", savedCycle
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to create menstrual cycle: " + e.getMessage()
            ));
        }
    }

    @PUT("/{id}")
    public ResponseEntity<Map<String, Object>> updateMenstrualCycle(
            @PathVariable Long id,
            @RequestBody Map<String, Object> request) {
        
        try {
            MenstrualCycle cycle = menstrualCycleRepository.findById(id).orElse(null);
            
            if (cycle == null) {
                return ResponseEntity.notFound().build();
            }

            if (request.get("endDate") != null) {
                cycle.setEndDate(LocalDate.parse(request.get("endDate").toString()));
            }
            
            if (request.get("cycleLength") != null) {
                cycle.setCycleLength(Integer.valueOf(request.get("cycleLength").toString()));
            }
            
            if (request.get("flowDuration") != null) {
                cycle.setFlowDuration(Integer.valueOf(request.get("flowDuration").toString()));
            }
            
            if (request.get("notes") != null) {
                cycle.setNotes(request.get("notes").toString());
            }

            MenstrualCycle updatedCycle = menstrualCycleRepository.save(cycle);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Menstrual cycle updated successfully",
                "cycle", updatedCycle
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to update menstrual cycle: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/current")
    public ResponseEntity<Map<String, Object>> getCurrentCycle(@RequestParam Long userId) {
        try {
            User user = userRepository.findById(userId).orElse(null);
            
            if (user == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "User not found"
                ));
            }

            MenstrualCycle currentCycle = menstrualCycleRepository
                .findCurrentCycle(user, LocalDate.now()).orElse(null);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "currentCycle", currentCycle
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch current cycle: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/predictions")
    public ResponseEntity<Map<String, Object>> getPredictions(@RequestParam Long userId) {
        try {
            User user = userRepository.findById(userId).orElse(null);
            
            if (user == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "User not found"
                ));
            }

            // Get historical cycles to calculate predictions
            List<MenstrualCycle> historicalCycles = menstrualCycleRepository
                .findByUserAndIsPredicted(user, false);

            // Simple prediction logic (can be enhanced)
            Map<String, Object> predictions = new HashMap<>();
            if (!historicalCycles.isEmpty()) {
                MenstrualCycle lastCycle = historicalCycles.get(0);
                int avgCycleLength = historicalCycles.stream()
                    .mapToInt(c -> c.getCycleLength() != null ? c.getCycleLength() : 28)
                    .sum() / historicalCycles.size();

                LocalDate nextPeriodDate = lastCycle.getStartDate().plusDays(avgCycleLength);
                LocalDate ovulationDate = nextPeriodDate.minusDays(14);
                
                predictions.put("nextPeriodDate", nextPeriodDate);
                predictions.put("ovulationDate", ovulationDate);
                predictions.put("fertileWindowStart", ovulationDate.minusDays(5));
                predictions.put("fertileWindowEnd", ovulationDate.plusDays(1));
                predictions.put("averageCycleLength", avgCycleLength);
            }

            return ResponseEntity.ok(Map.of(
                "success", true,
                "predictions", predictions
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to generate predictions: " + e.getMessage()
            ));
        }
    }
}
