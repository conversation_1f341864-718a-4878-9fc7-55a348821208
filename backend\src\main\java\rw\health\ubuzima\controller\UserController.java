package rw.health.ubuzima.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.dto.response.UserResponse;
import rw.health.ubuzima.repository.UserRepository;

import java.util.Map;

@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class UserController {

    private final UserRepository userRepository;

    @GetMapping("/profile")
    public ResponseEntity<Map<String, Object>> getProfile(@RequestHeader("Authorization") String authHeader) {
        try {
            // In real app, extract user from JWT token
            // For now, return first user as demo
            User user = userRepository.findAll().stream().findFirst().orElse(null);
            
            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            UserResponse userResponse = convertToUserResponse(user);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "user", userResponse
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to fetch profile: " + e.getMessage()
            ));
        }
    }

    @PUT("/profile")
    public ResponseEntity<Map<String, Object>> updateProfile(
            @RequestHeader("Authorization") String authHeader,
            @RequestBody Map<String, Object> request) {
        
        try {
            // In real app, extract user from JWT token
            User user = userRepository.findAll().stream().findFirst().orElse(null);
            
            if (user == null) {
                return ResponseEntity.notFound().build();
            }

            // Update user fields
            if (request.get("name") != null) {
                user.setName(request.get("name").toString());
            }
            
            if (request.get("phone") != null) {
                user.setPhone(request.get("phone").toString());
            }
            
            if (request.get("district") != null) {
                user.setDistrict(request.get("district").toString());
            }
            
            if (request.get("sector") != null) {
                user.setSector(request.get("sector").toString());
            }
            
            if (request.get("cell") != null) {
                user.setCell(request.get("cell").toString());
            }
            
            if (request.get("village") != null) {
                user.setVillage(request.get("village").toString());
            }

            User updatedUser = userRepository.save(user);
            UserResponse userResponse = convertToUserResponse(updatedUser);

            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Profile updated successfully",
                "user", userResponse
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to update profile: " + e.getMessage()
            ));
        }
    }

    @POST("/change-password")
    public ResponseEntity<Map<String, Object>> changePassword(
            @RequestHeader("Authorization") String authHeader,
            @RequestBody Map<String, String> request) {
        
        try {
            String currentPassword = request.get("currentPassword");
            String newPassword = request.get("newPassword");
            
            if (currentPassword == null || newPassword == null) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "Current password and new password are required"
                ));
            }

            // In real app, validate current password and update
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Password changed successfully"
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to change password: " + e.getMessage()
            ));
        }
    }

    @DELETE("/account")
    public ResponseEntity<Map<String, Object>> deleteAccount(@RequestHeader("Authorization") String authHeader) {
        try {
            // In real app, extract user from JWT token and delete account
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Account deleted successfully"
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(Map.of(
                "success", false,
                "message", "Failed to delete account: " + e.getMessage()
            ));
        }
    }

    private UserResponse convertToUserResponse(User user) {
        UserResponse response = new UserResponse();
        response.setId(user.getId().toString());
        response.setName(user.getName());
        response.setEmail(user.getEmail());
        response.setPhone(user.getPhone());
        response.setRole(user.getRole());
        response.setFacilityId(user.getFacilityId());
        response.setDistrict(user.getDistrict());
        response.setSector(user.getSector());
        response.setCell(user.getCell());
        response.setVillage(user.getVillage());
        response.setCreatedAt(user.getCreatedAt());
        response.setLastLoginAt(user.getLastLoginAt());
        response.setActive(user.isActive());
        response.setProfileImageUrl(user.getProfilePictureUrl());
        return response;
    }
}
