package rw.health.ubuzima.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import rw.health.ubuzima.dto.request.UserCreateRequest;
import rw.health.ubuzima.dto.request.UserUpdateRequest;
import rw.health.ubuzima.dto.response.ApiResponseDto;
import rw.health.ubuzima.dto.response.UserResponse;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.UserRole;
import rw.health.ubuzima.enums.UserStatus;
import rw.health.ubuzima.service.UserService;

import java.util.List;
import java.util.UUID;

/**
 * REST Controller for User management operations
 * 
 * <AUTHOR> Development Team
 */
@Slf4j
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@Tag(name = "User Management", description = "APIs for managing users in the Ubuzima system")
public class UserController {

    private final UserService userService;

    @Operation(summary = "Create a new user", description = "Creates a new user in the system")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "User created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "409", description = "User already exists")
    })
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<UserResponse>> createUser(
            @Valid @RequestBody UserCreateRequest request) {
        
        log.info("Creating new user with email: {}", request.getEmail());
        
        User user = userService.createUser(
            request.getFirstName(),
            request.getLastName(),
            request.getEmail(),
            request.getPhoneNumber(),
            request.getPassword(),
            request.getRole()
        );
        
        UserResponse response = UserResponse.fromEntity(user);
        
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponseDto.success("User created successfully", response));
    }

    @Operation(summary = "Get user by ID", description = "Retrieves a user by their unique identifier")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User found"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @GetMapping("/{userId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or @userService.isCurrentUser(#userId)")
    public ResponseEntity<ApiResponseDto<UserResponse>> getUserById(
            @Parameter(description = "User ID") @PathVariable UUID userId) {
        
        log.info("Retrieving user with ID: {}", userId);
        
        User user = userService.getUserById(userId);
        UserResponse response = UserResponse.fromEntity(user);
        
        return ResponseEntity.ok(ApiResponseDto.success("User retrieved successfully", response));
    }

    @Operation(summary = "Get all users", description = "Retrieves a paginated list of all users")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Users retrieved successfully")
    })
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Page<UserResponse>>> getAllUsers(
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.info("Retrieving all users with pagination: {}", pageable);
        
        Page<User> users = userService.getAllUsers(pageable);
        Page<UserResponse> response = users.map(UserResponse::fromEntity);
        
        return ResponseEntity.ok(ApiResponseDto.success("Users retrieved successfully", response));
    }

    @Operation(summary = "Update user", description = "Updates an existing user's information")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User updated successfully"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input data")
    })
    @PutMapping("/{userId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or @userService.isCurrentUser(#userId)")
    public ResponseEntity<ApiResponseDto<UserResponse>> updateUser(
            @Parameter(description = "User ID") @PathVariable UUID userId,
            @Valid @RequestBody UserUpdateRequest request) {
        
        log.info("Updating user with ID: {}", userId);
        
        User userUpdates = request.toEntity();
        User updatedUser = userService.updateUser(userId, userUpdates);
        UserResponse response = UserResponse.fromEntity(updatedUser);
        
        return ResponseEntity.ok(ApiResponseDto.success("User updated successfully", response));
    }

    @Operation(summary = "Delete user", description = "Permanently deletes a user from the system")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "User deleted successfully"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @DeleteMapping("/{userId}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Void>> deleteUser(
            @Parameter(description = "User ID") @PathVariable UUID userId) {
        
        log.info("Deleting user with ID: {}", userId);
        
        userService.deleteUser(userId);
        
        return ResponseEntity.ok(ApiResponseDto.success("User deleted successfully", null));
    }

    @Operation(summary = "Deactivate user", description = "Soft deletes a user by marking them as inactive")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User deactivated successfully"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @PatchMapping("/{userId}/deactivate")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Void>> deactivateUser(
            @Parameter(description = "User ID") @PathVariable UUID userId) {
        
        log.info("Deactivating user with ID: {}", userId);
        
        userService.softDeleteUser(userId);
        
        return ResponseEntity.ok(ApiResponseDto.success("User deactivated successfully", null));
    }

    @Operation(summary = "Get users by role", description = "Retrieves users filtered by their role")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Users retrieved successfully")
    })
    @GetMapping("/role/{role}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Page<UserResponse>>> getUsersByRole(
            @Parameter(description = "User role") @PathVariable UserRole role,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.info("Retrieving users with role: {}", role);
        
        Page<User> users = userService.getUsersByRole(role, pageable);
        Page<UserResponse> response = users.map(UserResponse::fromEntity);
        
        return ResponseEntity.ok(ApiResponseDto.success("Users retrieved successfully", response));
    }

    @Operation(summary = "Search users", description = "Searches users by name, email, or phone number")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Search completed successfully")
    })
    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('HEALTH_WORKER')")
    public ResponseEntity<ApiResponseDto<Page<UserResponse>>> searchUsers(
            @Parameter(description = "Search term") @RequestParam String q,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.info("Searching users with term: {}", q);
        
        Page<User> users = userService.searchUsers(q, pageable);
        Page<UserResponse> response = users.map(UserResponse::fromEntity);
        
        return ResponseEntity.ok(ApiResponseDto.success("Search completed successfully", response));
    }

    @Operation(summary = "Update user status", description = "Updates a user's status")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User status updated successfully"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @PatchMapping("/{userId}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<UserResponse>> updateUserStatus(
            @Parameter(description = "User ID") @PathVariable UUID userId,
            @Parameter(description = "New status") @RequestParam UserStatus status) {
        
        log.info("Updating status for user ID: {} to {}", userId, status);
        
        User updatedUser = userService.updateUserStatus(userId, status);
        UserResponse response = UserResponse.fromEntity(updatedUser);
        
        return ResponseEntity.ok(ApiResponseDto.success("User status updated successfully", response));
    }

    @Operation(summary = "Get health workers", description = "Retrieves all active health workers")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Health workers retrieved successfully")
    })
    @GetMapping("/health-workers")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<List<UserResponse>>> getHealthWorkers() {
        
        log.info("Retrieving all active health workers");
        
        List<User> healthWorkers = userService.getActiveHealthWorkers();
        List<UserResponse> response = healthWorkers.stream()
                .map(UserResponse::fromEntity)
                .toList();
        
        return ResponseEntity.ok(ApiResponseDto.success("Health workers retrieved successfully", response));
    }

    @Operation(summary = "Get clients by facility", description = "Retrieves clients assigned to a specific facility")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Clients retrieved successfully")
    })
    @GetMapping("/facility/{facilityId}/clients")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('HEALTH_WORKER')")
    public ResponseEntity<ApiResponseDto<Page<UserResponse>>> getClientsByFacility(
            @Parameter(description = "Facility ID") @PathVariable UUID facilityId,
            @PageableDefault(size = 20) Pageable pageable) {
        
        log.info("Retrieving clients for facility ID: {}", facilityId);
        
        Page<User> clients = userService.getClientsByFacility(facilityId, pageable);
        Page<UserResponse> response = clients.map(UserResponse::fromEntity);
        
        return ResponseEntity.ok(ApiResponseDto.success("Clients retrieved successfully", response));
    }

    @Operation(summary = "Get user statistics", description = "Retrieves user statistics")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully")
    })
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<UserStatistics>> getUserStatistics() {
        
        log.info("Retrieving user statistics");
        
        UserStatistics stats = UserStatistics.builder()
                .totalUsers(userService.getTotalUserCount())
                .activeUsers(userService.getActiveUserCount())
                .clients(userService.getUserCountByRole(UserRole.CLIENT))
                .healthWorkers(userService.getUserCountByRole(UserRole.HEALTH_WORKER))
                .admins(userService.getUserCountByRole(UserRole.ADMIN))
                .build();
        
        return ResponseEntity.ok(ApiResponseDto.success("Statistics retrieved successfully", stats));
    }

    // Inner class for statistics response
    public static class UserStatistics {
        private long totalUsers;
        private long activeUsers;
        private long clients;
        private long healthWorkers;
        private long admins;

        public static UserStatisticsBuilder builder() {
            return new UserStatisticsBuilder();
        }

        public static class UserStatisticsBuilder {
            private long totalUsers;
            private long activeUsers;
            private long clients;
            private long healthWorkers;
            private long admins;

            public UserStatisticsBuilder totalUsers(long totalUsers) {
                this.totalUsers = totalUsers;
                return this;
            }

            public UserStatisticsBuilder activeUsers(long activeUsers) {
                this.activeUsers = activeUsers;
                return this;
            }

            public UserStatisticsBuilder clients(long clients) {
                this.clients = clients;
                return this;
            }

            public UserStatisticsBuilder healthWorkers(long healthWorkers) {
                this.healthWorkers = healthWorkers;
                return this;
            }

            public UserStatisticsBuilder admins(long admins) {
                this.admins = admins;
                return this;
            }

            public UserStatistics build() {
                UserStatistics stats = new UserStatistics();
                stats.totalUsers = this.totalUsers;
                stats.activeUsers = this.activeUsers;
                stats.clients = this.clients;
                stats.healthWorkers = this.healthWorkers;
                stats.admins = this.admins;
                return stats;
            }
        }

        // Getters
        public long getTotalUsers() { return totalUsers; }
        public long getActiveUsers() { return activeUsers; }
        public long getClients() { return clients; }
        public long getHealthWorkers() { return healthWorkers; }
        public long getAdmins() { return admins; }
    }
}
