package rw.health.ubuzima.dto.request;

import jakarta.validation.constraints.*;
import lombok.Data;
import rw.health.ubuzima.enums.AppointmentType;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for appointment creation requests
 * 
 * <AUTHOR> Development Team
 */
@Data
public class AppointmentCreateRequest {

    @NotNull(message = "Client ID is required")
    private UUID clientId;

    @NotNull(message = "Facility ID is required")
    private UUID facilityId;

    private UUID healthWorkerId; // Optional - can be assigned later

    @NotNull(message = "Appointment date is required")
    @Future(message = "Appointment date must be in the future")
    private LocalDateTime appointmentDate;

    @NotNull(message = "Appointment type is required")
    private AppointmentType type;

    @Size(max = 500, message = "Reason cannot exceed 500 characters")
    private String reason;

    @Size(max = 1000, message = "Notes cannot exceed 1000 characters")
    private String notes;

    @Min(value = 15, message = "Duration must be at least 15 minutes")
    @Max(value = 180, message = "Duration cannot exceed 180 minutes")
    private Integer durationMinutes;

    private Boolean isEmergency = false;

    private Boolean isFollowUp = false;

    @Size(max = 100, message = "Emergency contact name cannot exceed 100 characters")
    private String emergencyContactName;

    @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Emergency contact phone should be valid")
    private String emergencyContactPhone;

    // Preferred communication method
    @Pattern(regexp = "^(SMS|CALL|EMAIL)$", message = "Preferred contact method must be SMS, CALL, or EMAIL")
    private String preferredContactMethod = "SMS";

    // Special requirements or accommodations
    @Size(max = 500, message = "Special requirements cannot exceed 500 characters")
    private String specialRequirements;

    // Language preference for the appointment
    @Pattern(regexp = "^(en|fr|rw)$", message = "Language preference must be en, fr, or rw")
    private String languagePreference = "rw";
}
