package rw.health.ubuzima.dto.request;

import jakarta.validation.constraints.*;
import lombok.Data;
import rw.health.ubuzima.entity.Appointment;
import rw.health.ubuzima.enums.AppointmentType;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for appointment update requests
 * 
 * <AUTHOR> Development Team
 */
@Data
public class AppointmentUpdateRequest {

    @Future(message = "Appointment date must be in the future")
    private LocalDateTime appointmentDate;

    private AppointmentType type;

    @Size(max = 500, message = "Reason cannot exceed 500 characters")
    private String reason;

    @Size(max = 1000, message = "Notes cannot exceed 1000 characters")
    private String notes;

    @Size(max = 1000, message = "Health worker notes cannot exceed 1000 characters")
    private String healthWorkerNotes;

    @Min(value = 15, message = "Duration must be at least 15 minutes")
    @Max(value = 180, message = "Duration cannot exceed 180 minutes")
    private Integer durationMinutes;

    private UUID healthWorkerId;

    private Boolean isEmergency;

    private Boolean isFollowUp;

    @Size(max = 100, message = "Emergency contact name cannot exceed 100 characters")
    private String emergencyContactName;

    @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Emergency contact phone should be valid")
    private String emergencyContactPhone;

    @Pattern(regexp = "^(SMS|CALL|EMAIL)$", message = "Preferred contact method must be SMS, CALL, or EMAIL")
    private String preferredContactMethod;

    @Size(max = 500, message = "Special requirements cannot exceed 500 characters")
    private String specialRequirements;

    @Pattern(regexp = "^(en|fr|rw)$", message = "Language preference must be en, fr, or rw")
    private String languagePreference;

    // Payment related fields
    private Double consultationFee;

    @Pattern(regexp = "^(PENDING|PAID|CANCELLED|REFUNDED)$", message = "Payment status must be PENDING, PAID, CANCELLED, or REFUNDED")
    private String paymentStatus;

    @Pattern(regexp = "^(CASH|MOBILE_MONEY|INSURANCE|CARD)$", message = "Payment method must be CASH, MOBILE_MONEY, INSURANCE, or CARD")
    private String paymentMethod;

    @Size(max = 100, message = "Payment reference cannot exceed 100 characters")
    private String paymentReference;

    /**
     * Converts this DTO to an Appointment entity for update operations
     */
    public Appointment toEntity() {
        Appointment appointment = new Appointment();
        appointment.setAppointmentDate(this.appointmentDate);
        appointment.setAppointmentType(this.type);
        appointment.setReason(this.reason);
        appointment.setNotes(this.notes);
        appointment.setHealthWorkerNotes(this.healthWorkerNotes);
        appointment.setDurationMinutes(this.durationMinutes);
        appointment.setIsEmergency(this.isEmergency);
        appointment.setIsFollowUp(this.isFollowUp);
        appointment.setConsultationFee(this.consultationFee);
        appointment.setPaymentStatus(this.paymentStatus);
        appointment.setPaymentMethod(this.paymentMethod);
        appointment.setPaymentReference(this.paymentReference);
        return appointment;
    }
}
