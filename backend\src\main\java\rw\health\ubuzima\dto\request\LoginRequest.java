package rw.health.ubuzima.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * DTO for login requests
 * 
 * <AUTHOR> Development Team
 */
@Data
public class LoginRequest {

    @NotBlank(message = "Username is required")
    private String username; // Can be email or phone number

    @NotBlank(message = "Password is required")
    private String password;

    private boolean rememberMe = false;
}
