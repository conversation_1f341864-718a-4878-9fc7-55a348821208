package rw.health.ubuzima.dto.request;

import jakarta.validation.constraints.*;
import lombok.Data;
import rw.health.ubuzima.enums.Gender;
import rw.health.ubuzima.enums.UserRole;

import java.time.LocalDate;

/**
 * DTO for user creation requests
 * 
 * <AUTHOR> Development Team
 */
@Data
public class UserCreateRequest {

    @NotBlank(message = "First name is required")
    @Size(min = 2, max = 50, message = "First name must be between 2 and 50 characters")
    private String firstName;

    @NotBlank(message = "Last name is required")
    @Size(min = 2, max = 50, message = "Last name must be between 2 and 50 characters")
    private String lastName;

    @Email(message = "Email should be valid")
    private String email;

    @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Phone number should be valid")
    private String phoneNumber;

    @NotBlank(message = "Password is required")
    @Size(min = 8, message = "Password must be at least 8 characters long")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]", 
             message = "Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character")
    private String password;

    @NotNull(message = "User role is required")
    private UserRole role;

    private Gender gender;

    @Past(message = "Date of birth must be in the past")
    private LocalDate dateOfBirth;

    @Size(max = 16, message = "National ID cannot exceed 16 characters")
    private String nationalId;

    @Size(max = 255, message = "Address cannot exceed 255 characters")
    private String address;

    @Size(max = 50, message = "District cannot exceed 50 characters")
    private String district;

    @Size(max = 50, message = "Sector cannot exceed 50 characters")
    private String sector;

    @Size(max = 50, message = "Cell cannot exceed 50 characters")
    private String cell;

    @Size(max = 50, message = "Village cannot exceed 50 characters")
    private String village;

    @Pattern(regexp = "^(en|fr|rw)$", message = "Preferred language must be en, fr, or rw")
    private String preferredLanguage = "rw";

    @Size(max = 100, message = "Emergency contact name cannot exceed 100 characters")
    private String emergencyContactName;

    @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Emergency contact phone should be valid")
    private String emergencyContactPhone;

    private String facilityId;
}
