package rw.health.ubuzima.dto.request;

import jakarta.validation.constraints.*;
import lombok.Data;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.Gender;

import java.time.LocalDate;

/**
 * DTO for user update requests
 * 
 * <AUTHOR> Development Team
 */
@Data
public class UserUpdateRequest {

    @Size(min = 2, max = 50, message = "First name must be between 2 and 50 characters")
    private String firstName;

    @Size(min = 2, max = 50, message = "Last name must be between 2 and 50 characters")
    private String lastName;

    @Email(message = "Email should be valid")
    private String email;

    @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Phone number should be valid")
    private String phoneNumber;

    private Gender gender;

    @Past(message = "Date of birth must be in the past")
    private LocalDate dateOfBirth;

    @Size(max = 16, message = "National ID cannot exceed 16 characters")
    private String nationalId;

    @Size(max = 255, message = "Address cannot exceed 255 characters")
    private String address;

    @Size(max = 50, message = "District cannot exceed 50 characters")
    private String district;

    @Size(max = 50, message = "Sector cannot exceed 50 characters")
    private String sector;

    @Size(max = 50, message = "Cell cannot exceed 50 characters")
    private String cell;

    @Size(max = 50, message = "Village cannot exceed 50 characters")
    private String village;

    @Pattern(regexp = "^(en|fr|rw)$", message = "Preferred language must be en, fr, or rw")
    private String preferredLanguage;

    @Size(max = 100, message = "Emergency contact name cannot exceed 100 characters")
    private String emergencyContactName;

    @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Emergency contact phone should be valid")
    private String emergencyContactPhone;

    private String profilePictureUrl;

    /**
     * Converts this DTO to a User entity for update operations
     */
    public User toEntity() {
        User user = new User();
        user.setFirstName(this.firstName);
        user.setLastName(this.lastName);
        user.setEmail(this.email);
        user.setPhoneNumber(this.phoneNumber);
        user.setGender(this.gender);
        user.setDateOfBirth(this.dateOfBirth);
        user.setNationalId(this.nationalId);
        user.setAddress(this.address);
        user.setDistrict(this.district);
        user.setSector(this.sector);
        user.setCell(this.cell);
        user.setVillage(this.village);
        user.setPreferredLanguage(this.preferredLanguage);
        user.setEmergencyContactName(this.emergencyContactName);
        user.setEmergencyContactPhone(this.emergencyContactPhone);
        user.setProfilePictureUrl(this.profilePictureUrl);
        return user;
    }
}
