package rw.health.ubuzima.dto.response;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Generic API response wrapper
 * 
 * <AUTHOR> Development Team
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApiResponseDto<T> {

    private boolean success;
    private String message;
    private T data;
    private LocalDateTime timestamp;
    private String path;
    private Integer status;

    /**
     * Creates a successful response with data
     */
    public static <T> ApiResponseDto<T> success(String message, T data) {
        return ApiResponseDto.<T>builder()
                .success(true)
                .message(message)
                .data(data)
                .timestamp(LocalDateTime.now())
                .status(200)
                .build();
    }

    /**
     * Creates a successful response without data
     */
    public static <T> ApiResponseDto<T> success(String message) {
        return success(message, null);
    }

    /**
     * Creates an error response
     */
    public static <T> ApiResponseDto<T> error(String message, Integer status) {
        return ApiResponseDto.<T>builder()
                .success(false)
                .message(message)
                .data(null)
                .timestamp(LocalDateTime.now())
                .status(status)
                .build();
    }

    /**
     * Creates an error response with data
     */
    public static <T> ApiResponseDto<T> error(String message, T data, Integer status) {
        return ApiResponseDto.<T>builder()
                .success(false)
                .message(message)
                .data(data)
                .timestamp(LocalDateTime.now())
                .status(status)
                .build();
    }

    /**
     * Creates a validation error response
     */
    public static <T> ApiResponseDto<T> validationError(String message, T errors) {
        return error(message, errors, 400);
    }

    /**
     * Creates a not found error response
     */
    public static <T> ApiResponseDto<T> notFound(String message) {
        return error(message, 404);
    }

    /**
     * Creates an unauthorized error response
     */
    public static <T> ApiResponseDto<T> unauthorized(String message) {
        return error(message, 401);
    }

    /**
     * Creates a forbidden error response
     */
    public static <T> ApiResponseDto<T> forbidden(String message) {
        return error(message, 403);
    }

    /**
     * Creates an internal server error response
     */
    public static <T> ApiResponseDto<T> internalError(String message) {
        return error(message, 500);
    }

    /**
     * Sets the request path for this response
     */
    public ApiResponseDto<T> withPath(String path) {
        this.path = path;
        return this;
    }
}
