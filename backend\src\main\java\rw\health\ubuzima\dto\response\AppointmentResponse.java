package rw.health.ubuzima.dto.response;

import lombok.Builder;
import lombok.Data;
import rw.health.ubuzima.entity.Appointment;
import rw.health.ubuzima.enums.AppointmentStatus;
import rw.health.ubuzima.enums.AppointmentType;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for appointment response data
 * 
 * <AUTHOR> Development Team
 */
@Data
@Builder
public class AppointmentResponse {

    private UUID id;
    private ClientInfo client;
    private HealthWorkerInfo healthWorker;
    private FacilityInfo facility;
    private LocalDateTime appointmentDate;
    private LocalDateTime endTime;
    private Integer durationMinutes;
    private AppointmentType appointmentType;
    private AppointmentStatus status;
    private String reason;
    private String notes;
    private String healthWorkerNotes;
    private Boolean isEmergency;
    private Boolean isFollowUp;
    private Boolean reminderSent;
    private LocalDateTime reminderSentAt;
    private LocalDateTime checkedInAt;
    private LocalDateTime startedAt;
    private LocalDateTime completedAt;
    private LocalDateTime cancelledAt;
    private String cancellationReason;
    private String cancelledBy;
    private LocalDateTime rescheduledFrom;
    private String rescheduleReason;
    private Double consultationFee;
    private String paymentStatus;
    private String paymentMethod;
    private String paymentReference;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    /**
     * Creates an AppointmentResponse from an Appointment entity
     */
    public static AppointmentResponse fromEntity(Appointment appointment) {
        AppointmentResponseBuilder builder = AppointmentResponse.builder()
                .id(appointment.getId())
                .appointmentDate(appointment.getAppointmentDate())
                .endTime(appointment.getEndTime())
                .durationMinutes(appointment.getDurationMinutes())
                .appointmentType(appointment.getAppointmentType())
                .status(appointment.getStatus())
                .reason(appointment.getReason())
                .notes(appointment.getNotes())
                .healthWorkerNotes(appointment.getHealthWorkerNotes())
                .isEmergency(appointment.getIsEmergency())
                .isFollowUp(appointment.getIsFollowUp())
                .reminderSent(appointment.getReminderSent())
                .reminderSentAt(appointment.getReminderSentAt())
                .checkedInAt(appointment.getCheckedInAt())
                .startedAt(appointment.getStartedAt())
                .completedAt(appointment.getCompletedAt())
                .cancelledAt(appointment.getCancelledAt())
                .cancellationReason(appointment.getCancellationReason())
                .cancelledBy(appointment.getCancelledBy())
                .rescheduledFrom(appointment.getRescheduledFrom())
                .rescheduleReason(appointment.getRescheduleReason())
                .consultationFee(appointment.getConsultationFee())
                .paymentStatus(appointment.getPaymentStatus())
                .paymentMethod(appointment.getPaymentMethod())
                .paymentReference(appointment.getPaymentReference())
                .createdAt(appointment.getCreatedAt())
                .updatedAt(appointment.getUpdatedAt());

        // Add client information
        if (appointment.getClient() != null) {
            builder.client(ClientInfo.fromUser(appointment.getClient()));
        }

        // Add health worker information
        if (appointment.getHealthWorker() != null) {
            builder.healthWorker(HealthWorkerInfo.fromUser(appointment.getHealthWorker()));
        }

        // Add facility information
        if (appointment.getFacility() != null) {
            builder.facility(FacilityInfo.fromFacility(appointment.getFacility()));
        }

        return builder.build();
    }

    /**
     * Creates a minimal AppointmentResponse with only essential fields
     */
    public static AppointmentResponse minimal(Appointment appointment) {
        return AppointmentResponse.builder()
                .id(appointment.getId())
                .appointmentDate(appointment.getAppointmentDate())
                .appointmentType(appointment.getAppointmentType())
                .status(appointment.getStatus())
                .reason(appointment.getReason())
                .isEmergency(appointment.getIsEmergency())
                .build();
    }

    // Nested classes for related information
    @Data
    @Builder
    public static class ClientInfo {
        private UUID id;
        private String firstName;
        private String lastName;
        private String fullName;
        private String email;
        private String phoneNumber;
        private String preferredLanguage;

        public static ClientInfo fromUser(rw.health.ubuzima.entity.User user) {
            return ClientInfo.builder()
                    .id(user.getId())
                    .firstName(user.getFirstName())
                    .lastName(user.getLastName())
                    .fullName(user.getFullName())
                    .email(user.getEmail())
                    .phoneNumber(user.getPhoneNumber())
                    .preferredLanguage(user.getPreferredLanguage())
                    .build();
        }
    }

    @Data
    @Builder
    public static class HealthWorkerInfo {
        private UUID id;
        private String firstName;
        private String lastName;
        private String fullName;
        private String email;
        private String phoneNumber;
        private String specialization;

        public static HealthWorkerInfo fromUser(rw.health.ubuzima.entity.User user) {
            return HealthWorkerInfo.builder()
                    .id(user.getId())
                    .firstName(user.getFirstName())
                    .lastName(user.getLastName())
                    .fullName(user.getFullName())
                    .email(user.getEmail())
                    .phoneNumber(user.getPhoneNumber())
                    .build();
        }
    }

    @Data
    @Builder
    public static class FacilityInfo {
        private UUID id;
        private String name;
        private String facilityType;
        private String address;
        private String district;
        private String sector;
        private String phoneNumber;
        private String operatingHours;
        private Boolean is24Hours;

        public static FacilityInfo fromFacility(rw.health.ubuzima.entity.HealthFacility facility) {
            return FacilityInfo.builder()
                    .id(facility.getId())
                    .name(facility.getName())
                    .facilityType(facility.getFacilityType().getDisplayName())
                    .address(facility.getAddress())
                    .district(facility.getDistrict())
                    .sector(facility.getSector())
                    .phoneNumber(facility.getPhoneNumber())
                    .operatingHours(facility.getOperatingHours())
                    .is24Hours(facility.getIs24Hours())
                    .build();
        }
    }

    // Utility methods
    public boolean isUpcoming() {
        return appointmentDate != null && appointmentDate.isAfter(LocalDateTime.now()) && 
               (status == AppointmentStatus.SCHEDULED || status == AppointmentStatus.CONFIRMED);
    }

    public boolean isPast() {
        return appointmentDate != null && appointmentDate.isBefore(LocalDateTime.now());
    }

    public boolean canBeCancelled() {
        return status == AppointmentStatus.SCHEDULED || status == AppointmentStatus.CONFIRMED;
    }

    public boolean canBeRescheduled() {
        return canBeCancelled() && appointmentDate != null && 
               appointmentDate.isAfter(LocalDateTime.now().plusHours(2));
    }

    public String getStatusDisplayName() {
        return status != null ? status.getDisplayName() : "";
    }

    public String getTypeDisplayName() {
        return appointmentType != null ? appointmentType.getDisplayName() : "";
    }

    public String getFormattedDuration() {
        if (durationMinutes == null) return "";
        int hours = durationMinutes / 60;
        int minutes = durationMinutes % 60;
        if (hours > 0) {
            return hours + "h " + minutes + "m";
        }
        return minutes + "m";
    }
}
