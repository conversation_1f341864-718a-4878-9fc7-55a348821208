package rw.health.ubuzima.dto.response;

import lombok.Data;
import lombok.Builder;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.Gender;
import rw.health.ubuzima.enums.UserRole;
import rw.health.ubuzima.enums.UserStatus;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for user response data
 * 
 * <AUTHOR> Development Team
 */
@Data
@Builder
public class UserResponse {

    private UUID id;
    private String firstName;
    private String lastName;
    private String fullName;
    private String email;
    private String phoneNumber;
    private UserRole role;
    private UserStatus status;
    private Gender gender;
    private LocalDate dateOfBirth;
    private String nationalId;
    private String address;
    private String district;
    private String sector;
    private String cell;
    private String village;
    private String profilePictureUrl;
    private String preferredLanguage;
    private String emergencyContactName;
    private String emergencyContactPhone;
    private LocalDateTime lastLoginAt;
    private Boolean emailVerified;
    private Boolean phoneVerified;
    private Boolean twoFactorEnabled;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private FacilityResponse facility;

    /**
     * Creates a UserResponse from a User entity
     */
    public static UserResponse fromEntity(User user) {
        UserResponseBuilder builder = UserResponse.builder()
                .id(user.getId())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .fullName(user.getFullName())
                .email(user.getEmail())
                .phoneNumber(user.getPhoneNumber())
                .role(user.getRole())
                .status(user.getStatus())
                .gender(user.getGender())
                .dateOfBirth(user.getDateOfBirth())
                .nationalId(user.getNationalId())
                .address(user.getAddress())
                .district(user.getDistrict())
                .sector(user.getSector())
                .cell(user.getCell())
                .village(user.getVillage())
                .profilePictureUrl(user.getProfilePictureUrl())
                .preferredLanguage(user.getPreferredLanguage())
                .emergencyContactName(user.getEmergencyContactName())
                .emergencyContactPhone(user.getEmergencyContactPhone())
                .lastLoginAt(user.getLastLoginAt())
                .emailVerified(user.getEmailVerified())
                .phoneVerified(user.getPhoneVerified())
                .twoFactorEnabled(user.getTwoFactorEnabled())
                .isActive(user.getIsActive())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt());

        // Add facility information if present
        if (user.getFacility() != null) {
            builder.facility(FacilityResponse.fromEntity(user.getFacility()));
        }

        return builder.build();
    }

    /**
     * Creates a minimal UserResponse with only essential fields
     */
    public static UserResponse minimal(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .fullName(user.getFullName())
                .email(user.getEmail())
                .phoneNumber(user.getPhoneNumber())
                .role(user.getRole())
                .status(user.getStatus())
                .isActive(user.getIsActive())
                .build();
    }

    // Nested class for facility information
    @Data
    @Builder
    public static class FacilityResponse {
        private UUID id;
        private String name;
        private String facilityType;
        private String district;
        private String sector;
        private String address;
        private String phoneNumber;

        public static FacilityResponse fromEntity(rw.health.ubuzima.entity.HealthFacility facility) {
            return FacilityResponse.builder()
                    .id(facility.getId())
                    .name(facility.getName())
                    .facilityType(facility.getFacilityType().getDisplayName())
                    .district(facility.getDistrict())
                    .sector(facility.getSector())
                    .address(facility.getAddress())
                    .phoneNumber(facility.getPhoneNumber())
                    .build();
        }
    }
}
