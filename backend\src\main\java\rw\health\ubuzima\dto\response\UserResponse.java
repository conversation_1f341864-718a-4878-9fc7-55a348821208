package rw.health.ubuzima.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import rw.health.ubuzima.enums.UserRole;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserResponse {
    private String id;
    private String name;
    private String email;
    private String phone;
    private UserRole role;
    private String facilityId;
    private String district;
    private String sector;
    private String cell;
    private String village;
    private LocalDateTime createdAt;
    private LocalDate lastLoginAt;
    private boolean isActive;
    private String profileImageUrl;
}
