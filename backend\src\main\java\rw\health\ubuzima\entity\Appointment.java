package rw.health.ubuzima.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import rw.health.ubuzima.enums.AppointmentStatus;
import rw.health.ubuzima.enums.AppointmentType;

import java.time.LocalDateTime;

/**
 * Appointment entity representing scheduled appointments between clients and health workers
 * 
 * <AUTHOR> Development Team
 */
@Entity
@Table(name = "appointments", indexes = {
    @Index(name = "idx_appointment_client", columnList = "client_id"),
    @Index(name = "idx_appointment_health_worker", columnList = "health_worker_id"),
    @Index(name = "idx_appointment_facility", columnList = "facility_id"),
    @Index(name = "idx_appointment_date", columnList = "appointment_date"),
    @Index(name = "idx_appointment_status", columnList = "status")
})
public class Appointment extends BaseEntity {

    @NotNull(message = "Client is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_id", nullable = false)
    private User client;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "health_worker_id")
    private User healthWorker;

    @NotNull(message = "Facility is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "facility_id", nullable = false)
    private HealthFacility facility;

    @NotNull(message = "Appointment date is required")
    @Column(name = "appointment_date", nullable = false)
    private LocalDateTime appointmentDate;

    @Column(name = "duration_minutes")
    private Integer durationMinutes = 30;

    @Enumerated(EnumType.STRING)
    @Column(name = "appointment_type", nullable = false)
    private AppointmentType appointmentType;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private AppointmentStatus status = AppointmentStatus.SCHEDULED;

    @Size(max = 500, message = "Reason cannot exceed 500 characters")
    @Column(name = "reason", length = 500)
    private String reason;

    @Size(max = 1000, message = "Notes cannot exceed 1000 characters")
    @Column(name = "notes", length = 1000)
    private String notes;

    @Size(max = 1000, message = "Health worker notes cannot exceed 1000 characters")
    @Column(name = "health_worker_notes", length = 1000)
    private String healthWorkerNotes;

    @Column(name = "is_emergency", nullable = false)
    private Boolean isEmergency = false;

    @Column(name = "is_follow_up", nullable = false)
    private Boolean isFollowUp = false;

    @Column(name = "reminder_sent", nullable = false)
    private Boolean reminderSent = false;

    @Column(name = "reminder_sent_at")
    private LocalDateTime reminderSentAt;

    @Column(name = "checked_in_at")
    private LocalDateTime checkedInAt;

    @Column(name = "started_at")
    private LocalDateTime startedAt;

    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    @Column(name = "cancelled_at")
    private LocalDateTime cancelledAt;

    @Size(max = 500, message = "Cancellation reason cannot exceed 500 characters")
    @Column(name = "cancellation_reason", length = 500)
    private String cancellationReason;

    @Column(name = "cancelled_by")
    private String cancelledBy;

    @Column(name = "rescheduled_from")
    private LocalDateTime rescheduledFrom;

    @Size(max = 500, message = "Reschedule reason cannot exceed 500 characters")
    @Column(name = "reschedule_reason", length = 500)
    private String rescheduleReason;

    @Column(name = "consultation_fee")
    private Double consultationFee;

    @Column(name = "payment_status", length = 20)
    private String paymentStatus = "PENDING";

    @Column(name = "payment_method", length = 50)
    private String paymentMethod;

    @Column(name = "payment_reference", length = 100)
    private String paymentReference;

    // Constructors
    public Appointment() {}

    public Appointment(User client, HealthFacility facility, LocalDateTime appointmentDate, AppointmentType appointmentType) {
        this.client = client;
        this.facility = facility;
        this.appointmentDate = appointmentDate;
        this.appointmentType = appointmentType;
    }

    // Getters and Setters
    public User getClient() {
        return client;
    }

    public void setClient(User client) {
        this.client = client;
    }

    public User getHealthWorker() {
        return healthWorker;
    }

    public void setHealthWorker(User healthWorker) {
        this.healthWorker = healthWorker;
    }

    public HealthFacility getFacility() {
        return facility;
    }

    public void setFacility(HealthFacility facility) {
        this.facility = facility;
    }

    public LocalDateTime getAppointmentDate() {
        return appointmentDate;
    }

    public void setAppointmentDate(LocalDateTime appointmentDate) {
        this.appointmentDate = appointmentDate;
    }

    public Integer getDurationMinutes() {
        return durationMinutes;
    }

    public void setDurationMinutes(Integer durationMinutes) {
        this.durationMinutes = durationMinutes;
    }

    public AppointmentType getAppointmentType() {
        return appointmentType;
    }

    public void setAppointmentType(AppointmentType appointmentType) {
        this.appointmentType = appointmentType;
    }

    public AppointmentStatus getStatus() {
        return status;
    }

    public void setStatus(AppointmentStatus status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getHealthWorkerNotes() {
        return healthWorkerNotes;
    }

    public void setHealthWorkerNotes(String healthWorkerNotes) {
        this.healthWorkerNotes = healthWorkerNotes;
    }

    public Boolean getIsEmergency() {
        return isEmergency;
    }

    public void setIsEmergency(Boolean isEmergency) {
        this.isEmergency = isEmergency;
    }

    public Boolean getIsFollowUp() {
        return isFollowUp;
    }

    public void setIsFollowUp(Boolean isFollowUp) {
        this.isFollowUp = isFollowUp;
    }

    public Boolean getReminderSent() {
        return reminderSent;
    }

    public void setReminderSent(Boolean reminderSent) {
        this.reminderSent = reminderSent;
    }

    public LocalDateTime getReminderSentAt() {
        return reminderSentAt;
    }

    public void setReminderSentAt(LocalDateTime reminderSentAt) {
        this.reminderSentAt = reminderSentAt;
    }

    public LocalDateTime getCheckedInAt() {
        return checkedInAt;
    }

    public void setCheckedInAt(LocalDateTime checkedInAt) {
        this.checkedInAt = checkedInAt;
    }

    public LocalDateTime getStartedAt() {
        return startedAt;
    }

    public void setStartedAt(LocalDateTime startedAt) {
        this.startedAt = startedAt;
    }

    public LocalDateTime getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(LocalDateTime completedAt) {
        this.completedAt = completedAt;
    }

    public LocalDateTime getCancelledAt() {
        return cancelledAt;
    }

    public void setCancelledAt(LocalDateTime cancelledAt) {
        this.cancelledAt = cancelledAt;
    }

    public String getCancellationReason() {
        return cancellationReason;
    }

    public void setCancellationReason(String cancellationReason) {
        this.cancellationReason = cancellationReason;
    }

    public String getCancelledBy() {
        return cancelledBy;
    }

    public void setCancelledBy(String cancelledBy) {
        this.cancelledBy = cancelledBy;
    }

    public LocalDateTime getRescheduledFrom() {
        return rescheduledFrom;
    }

    public void setRescheduledFrom(LocalDateTime rescheduledFrom) {
        this.rescheduledFrom = rescheduledFrom;
    }

    public String getRescheduleReason() {
        return rescheduleReason;
    }

    public void setRescheduleReason(String rescheduleReason) {
        this.rescheduleReason = rescheduleReason;
    }

    public Double getConsultationFee() {
        return consultationFee;
    }

    public void setConsultationFee(Double consultationFee) {
        this.consultationFee = consultationFee;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    // Utility methods
    public LocalDateTime getEndTime() {
        return appointmentDate.plusMinutes(durationMinutes);
    }

    public boolean isUpcoming() {
        return appointmentDate.isAfter(LocalDateTime.now()) && status == AppointmentStatus.SCHEDULED;
    }

    public boolean isPast() {
        return appointmentDate.isBefore(LocalDateTime.now());
    }

    public boolean canBeCancelled() {
        return status == AppointmentStatus.SCHEDULED && appointmentDate.isAfter(LocalDateTime.now().plusHours(2));
    }

    public boolean canBeRescheduled() {
        return status == AppointmentStatus.SCHEDULED && appointmentDate.isAfter(LocalDateTime.now().plusHours(2));
    }

    public void cancel(String reason, String cancelledBy) {
        this.status = AppointmentStatus.CANCELLED;
        this.cancelledAt = LocalDateTime.now();
        this.cancellationReason = reason;
        this.cancelledBy = cancelledBy;
    }

    public void reschedule(LocalDateTime newDate, String reason) {
        this.rescheduledFrom = this.appointmentDate;
        this.appointmentDate = newDate;
        this.rescheduleReason = reason;
        this.status = AppointmentStatus.SCHEDULED;
    }

    public void checkIn() {
        this.checkedInAt = LocalDateTime.now();
        this.status = AppointmentStatus.CHECKED_IN;
    }

    public void start() {
        this.startedAt = LocalDateTime.now();
        this.status = AppointmentStatus.IN_PROGRESS;
    }

    public void complete() {
        this.completedAt = LocalDateTime.now();
        this.status = AppointmentStatus.COMPLETED;
    }

    public void markNoShow() {
        this.status = AppointmentStatus.NO_SHOW;
    }
}
