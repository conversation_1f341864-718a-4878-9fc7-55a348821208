package rw.health.ubuzima.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import rw.health.ubuzima.enums.ContentCategory;
import rw.health.ubuzima.enums.ContentStatus;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * Audio Content entity for managing Kinyarwanda health education content
 * 
 * <AUTHOR> Development Team
 */
@Entity
@Table(name = "audio_content", indexes = {
    @Index(name = "idx_audio_content_category", columnList = "category"),
    @Index(name = "idx_audio_content_language", columnList = "language"),
    @Index(name = "idx_audio_content_status", columnList = "status"),
    @Index(name = "idx_audio_content_tags", columnList = "tags")
})
public class AudioContent extends BaseEntity {

    @NotBlank(message = "Title is required")
    @Size(min = 2, max = 200, message = "Title must be between 2 and 200 characters")
    @Column(name = "title", nullable = false, length = 200)
    private String title;

    @Size(max = 1000, message = "Description cannot exceed 1000 characters")
    @Column(name = "description", length = 1000)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "category", nullable = false)
    private ContentCategory category;

    @NotBlank(message = "Language is required")
    @Column(name = "language", nullable = false, length = 10)
    private String language = "rw"; // Default to Kinyarwanda

    @NotBlank(message = "File path is required")
    @Column(name = "file_path", nullable = false, length = 500)
    private String filePath;

    @Column(name = "file_url", length = 500)
    private String fileUrl;

    @Column(name = "file_size_bytes")
    private Long fileSizeBytes;

    @Column(name = "duration_seconds")
    private Integer durationSeconds;

    @Column(name = "mime_type", length = 100)
    private String mimeType;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ContentStatus status = ContentStatus.DRAFT;

    @Column(name = "tags", length = 500)
    private String tags; // Comma-separated tags for search

    @Column(name = "transcript", columnDefinition = "TEXT")
    private String transcript; // Text version for accessibility

    @Column(name = "keywords", length = 500)
    private String keywords; // Search keywords

    @Column(name = "target_audience", length = 100)
    private String targetAudience; // e.g., "women", "youth", "couples"

    @Column(name = "age_group", length = 50)
    private String ageGroup; // e.g., "18-25", "26-35", "all"

    @Column(name = "difficulty_level", length = 20)
    private String difficultyLevel = "BASIC"; // BASIC, INTERMEDIATE, ADVANCED

    @Column(name = "play_count", nullable = false)
    private Long playCount = 0L;

    @Column(name = "download_count", nullable = false)
    private Long downloadCount = 0L;

    @Column(name = "rating", precision = 3, scale = 2)
    private java.math.BigDecimal rating;

    @Column(name = "total_ratings", nullable = false)
    private Integer totalRatings = 0;

    @Column(name = "is_featured", nullable = false)
    private Boolean isFeatured = false;

    @Column(name = "is_offline_available", nullable = false)
    private Boolean isOfflineAvailable = true;

    @Column(name = "published_at")
    private LocalDateTime publishedAt;

    @Column(name = "expires_at")
    private LocalDateTime expiresAt;

    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "author_id")
    private User author;

    @OneToMany(mappedBy = "audioContent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<ContentRating> ratings = new HashSet<>();

    @OneToMany(mappedBy = "audioContent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<ContentPlayHistory> playHistory = new HashSet<>();

    // Constructors
    public AudioContent() {}

    public AudioContent(String title, ContentCategory category, String filePath) {
        this.title = title;
        this.category = category;
        this.filePath = filePath;
    }

    // Getters and Setters
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public ContentCategory getCategory() {
        return category;
    }

    public void setCategory(ContentCategory category) {
        this.category = category;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public Long getFileSizeBytes() {
        return fileSizeBytes;
    }

    public void setFileSizeBytes(Long fileSizeBytes) {
        this.fileSizeBytes = fileSizeBytes;
    }

    public Integer getDurationSeconds() {
        return durationSeconds;
    }

    public void setDurationSeconds(Integer durationSeconds) {
        this.durationSeconds = durationSeconds;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public ContentStatus getStatus() {
        return status;
    }

    public void setStatus(ContentStatus status) {
        this.status = status;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getTranscript() {
        return transcript;
    }

    public void setTranscript(String transcript) {
        this.transcript = transcript;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getTargetAudience() {
        return targetAudience;
    }

    public void setTargetAudience(String targetAudience) {
        this.targetAudience = targetAudience;
    }

    public String getAgeGroup() {
        return ageGroup;
    }

    public void setAgeGroup(String ageGroup) {
        this.ageGroup = ageGroup;
    }

    public String getDifficultyLevel() {
        return difficultyLevel;
    }

    public void setDifficultyLevel(String difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }

    public Long getPlayCount() {
        return playCount;
    }

    public void setPlayCount(Long playCount) {
        this.playCount = playCount;
    }

    public Long getDownloadCount() {
        return downloadCount;
    }

    public void setDownloadCount(Long downloadCount) {
        this.downloadCount = downloadCount;
    }

    public java.math.BigDecimal getRating() {
        return rating;
    }

    public void setRating(java.math.BigDecimal rating) {
        this.rating = rating;
    }

    public Integer getTotalRatings() {
        return totalRatings;
    }

    public void setTotalRatings(Integer totalRatings) {
        this.totalRatings = totalRatings;
    }

    public Boolean getIsFeatured() {
        return isFeatured;
    }

    public void setIsFeatured(Boolean isFeatured) {
        this.isFeatured = isFeatured;
    }

    public Boolean getIsOfflineAvailable() {
        return isOfflineAvailable;
    }

    public void setIsOfflineAvailable(Boolean isOfflineAvailable) {
        this.isOfflineAvailable = isOfflineAvailable;
    }

    public LocalDateTime getPublishedAt() {
        return publishedAt;
    }

    public void setPublishedAt(LocalDateTime publishedAt) {
        this.publishedAt = publishedAt;
    }

    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public User getAuthor() {
        return author;
    }

    public void setAuthor(User author) {
        this.author = author;
    }

    public Set<ContentRating> getRatings() {
        return ratings;
    }

    public void setRatings(Set<ContentRating> ratings) {
        this.ratings = ratings;
    }

    public Set<ContentPlayHistory> getPlayHistory() {
        return playHistory;
    }

    public void setPlayHistory(Set<ContentPlayHistory> playHistory) {
        this.playHistory = playHistory;
    }

    // Utility methods
    public void incrementPlayCount() {
        this.playCount++;
    }

    public void incrementDownloadCount() {
        this.downloadCount++;
    }

    public String getFormattedDuration() {
        if (durationSeconds == null) return "0:00";
        int minutes = durationSeconds / 60;
        int seconds = durationSeconds % 60;
        return String.format("%d:%02d", minutes, seconds);
    }

    public String getFormattedFileSize() {
        if (fileSizeBytes == null) return "0 KB";
        if (fileSizeBytes < 1024) return fileSizeBytes + " B";
        if (fileSizeBytes < 1024 * 1024) return (fileSizeBytes / 1024) + " KB";
        return String.format("%.1f MB", fileSizeBytes / (1024.0 * 1024.0));
    }

    public boolean isPublished() {
        return status == ContentStatus.PUBLISHED && publishedAt != null;
    }

    public boolean isExpired() {
        return expiresAt != null && expiresAt.isBefore(LocalDateTime.now());
    }

    public void publish() {
        this.status = ContentStatus.PUBLISHED;
        this.publishedAt = LocalDateTime.now();
    }

    public void unpublish() {
        this.status = ContentStatus.DRAFT;
        this.publishedAt = null;
    }

    public void updateRating(java.math.BigDecimal newRating) {
        if (this.rating == null) {
            this.rating = newRating;
            this.totalRatings = 1;
        } else {
            java.math.BigDecimal totalScore = this.rating.multiply(java.math.BigDecimal.valueOf(this.totalRatings));
            totalScore = totalScore.add(newRating);
            this.totalRatings++;
            this.rating = totalScore.divide(java.math.BigDecimal.valueOf(this.totalRatings), 2, java.math.BigDecimal.ROUND_HALF_UP);
        }
    }
}
