package rw.health.ubuzima.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

/**
 * Content Play History entity for tracking audio content usage
 * 
 * <AUTHOR> Development Team
 */
@Entity
@Table(name = "content_play_history", indexes = {
    @Index(name = "idx_play_history_content", columnList = "audio_content_id"),
    @Index(name = "idx_play_history_user", columnList = "user_id"),
    @Index(name = "idx_play_history_date", columnList = "played_at"),
    @Index(name = "idx_play_history_completed", columnList = "completed")
})
public class ContentPlayHistory extends BaseEntity {

    @NotNull(message = "Audio content is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "audio_content_id", nullable = false)
    private AudioContent audioContent;

    @NotNull(message = "User is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @NotNull(message = "Played at timestamp is required")
    @Column(name = "played_at", nullable = false)
    private LocalDateTime playedAt;

    @Column(name = "duration_played_seconds")
    private Integer durationPlayedSeconds;

    @Column(name = "total_duration_seconds")
    private Integer totalDurationSeconds;

    @Column(name = "completion_percentage", precision = 5, scale = 2)
    private java.math.BigDecimal completionPercentage;

    @Column(name = "completed", nullable = false)
    private Boolean completed = false;

    @Column(name = "device_type", length = 50)
    private String deviceType;

    @Column(name = "platform", length = 50)
    private String platform;

    @Column(name = "offline_play", nullable = false)
    private Boolean offlinePlay = false;

    @Column(name = "session_id", length = 100)
    private String sessionId;

    @Column(name = "ip_address", length = 45)
    private String ipAddress;

    @Column(name = "location", length = 100)
    private String location;

    // Constructors
    public ContentPlayHistory() {}

    public ContentPlayHistory(AudioContent audioContent, User user) {
        this.audioContent = audioContent;
        this.user = user;
        this.playedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public AudioContent getAudioContent() {
        return audioContent;
    }

    public void setAudioContent(AudioContent audioContent) {
        this.audioContent = audioContent;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public LocalDateTime getPlayedAt() {
        return playedAt;
    }

    public void setPlayedAt(LocalDateTime playedAt) {
        this.playedAt = playedAt;
    }

    public Integer getDurationPlayedSeconds() {
        return durationPlayedSeconds;
    }

    public void setDurationPlayedSeconds(Integer durationPlayedSeconds) {
        this.durationPlayedSeconds = durationPlayedSeconds;
        calculateCompletionPercentage();
    }

    public Integer getTotalDurationSeconds() {
        return totalDurationSeconds;
    }

    public void setTotalDurationSeconds(Integer totalDurationSeconds) {
        this.totalDurationSeconds = totalDurationSeconds;
        calculateCompletionPercentage();
    }

    public java.math.BigDecimal getCompletionPercentage() {
        return completionPercentage;
    }

    public void setCompletionPercentage(java.math.BigDecimal completionPercentage) {
        this.completionPercentage = completionPercentage;
    }

    public Boolean getCompleted() {
        return completed;
    }

    public void setCompleted(Boolean completed) {
        this.completed = completed;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Boolean getOfflinePlay() {
        return offlinePlay;
    }

    public void setOfflinePlay(Boolean offlinePlay) {
        this.offlinePlay = offlinePlay;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    // Utility methods
    private void calculateCompletionPercentage() {
        if (durationPlayedSeconds != null && totalDurationSeconds != null && totalDurationSeconds > 0) {
            double percentage = (durationPlayedSeconds.doubleValue() / totalDurationSeconds.doubleValue()) * 100;
            this.completionPercentage = java.math.BigDecimal.valueOf(percentage).setScale(2, java.math.BigDecimal.ROUND_HALF_UP);
            
            // Mark as completed if 80% or more is played
            this.completed = percentage >= 80.0;
        }
    }

    public String getFormattedDuration() {
        if (durationPlayedSeconds == null) return "0:00";
        int minutes = durationPlayedSeconds / 60;
        int seconds = durationPlayedSeconds % 60;
        return String.format("%d:%02d", minutes, seconds);
    }

    public boolean isRecentPlay() {
        return playedAt != null && playedAt.isAfter(LocalDateTime.now().minusHours(24));
    }
}
