package rw.health.ubuzima.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;

/**
 * Content Rating entity for user ratings of audio content
 * 
 * <AUTHOR> Development Team
 */
@Entity
@Table(name = "content_ratings", indexes = {
    @Index(name = "idx_content_rating_content", columnList = "audio_content_id"),
    @Index(name = "idx_content_rating_user", columnList = "user_id"),
    @Index(name = "idx_content_rating_rating", columnList = "rating")
}, uniqueConstraints = {
    @UniqueConstraint(columnNames = {"audio_content_id", "user_id"})
})
public class ContentRating extends BaseEntity {

    @NotNull(message = "Audio content is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "audio_content_id", nullable = false)
    private AudioContent audioContent;

    @NotNull(message = "User is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @NotNull(message = "Rating is required")
    @DecimalMin(value = "1.0", message = "Rating must be at least 1.0")
    @DecimalMax(value = "5.0", message = "Rating must be at most 5.0")
    @Column(name = "rating", nullable = false, precision = 3, scale = 2)
    private BigDecimal rating;

    @Size(max = 500, message = "Comment cannot exceed 500 characters")
    @Column(name = "comment", length = 500)
    private String comment;

    @Column(name = "is_helpful", nullable = false)
    private Boolean isHelpful = true;

    @Column(name = "helpful_count", nullable = false)
    private Integer helpfulCount = 0;

    @Column(name = "not_helpful_count", nullable = false)
    private Integer notHelpfulCount = 0;

    @Column(name = "is_flagged", nullable = false)
    private Boolean isFlagged = false;

    @Column(name = "flag_reason", length = 200)
    private String flagReason;

    // Constructors
    public ContentRating() {}

    public ContentRating(AudioContent audioContent, User user, BigDecimal rating) {
        this.audioContent = audioContent;
        this.user = user;
        this.rating = rating;
    }

    // Getters and Setters
    public AudioContent getAudioContent() {
        return audioContent;
    }

    public void setAudioContent(AudioContent audioContent) {
        this.audioContent = audioContent;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public BigDecimal getRating() {
        return rating;
    }

    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Boolean getIsHelpful() {
        return isHelpful;
    }

    public void setIsHelpful(Boolean isHelpful) {
        this.isHelpful = isHelpful;
    }

    public Integer getHelpfulCount() {
        return helpfulCount;
    }

    public void setHelpfulCount(Integer helpfulCount) {
        this.helpfulCount = helpfulCount;
    }

    public Integer getNotHelpfulCount() {
        return notHelpfulCount;
    }

    public void setNotHelpfulCount(Integer notHelpfulCount) {
        this.notHelpfulCount = notHelpfulCount;
    }

    public Boolean getIsFlagged() {
        return isFlagged;
    }

    public void setIsFlagged(Boolean isFlagged) {
        this.isFlagged = isFlagged;
    }

    public String getFlagReason() {
        return flagReason;
    }

    public void setFlagReason(String flagReason) {
        this.flagReason = flagReason;
    }

    // Utility methods
    public void incrementHelpfulCount() {
        this.helpfulCount++;
    }

    public void incrementNotHelpfulCount() {
        this.notHelpfulCount++;
    }

    public void flag(String reason) {
        this.isFlagged = true;
        this.flagReason = reason;
    }

    public void unflag() {
        this.isFlagged = false;
        this.flagReason = null;
    }

    public int getNetHelpfulness() {
        return helpfulCount - notHelpfulCount;
    }
}
