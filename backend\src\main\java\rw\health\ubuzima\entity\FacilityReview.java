package rw.health.ubuzima.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;

/**
 * Facility Review entity for user reviews and ratings of health facilities
 * 
 * <AUTHOR> Development Team
 */
@Entity
@Table(name = "facility_reviews", indexes = {
    @Index(name = "idx_review_facility", columnList = "facility_id"),
    @Index(name = "idx_review_user", columnList = "user_id"),
    @Index(name = "idx_review_rating", columnList = "rating")
})
public class FacilityReview extends BaseEntity {

    @NotNull(message = "Facility is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "facility_id", nullable = false)
    private HealthFacility facility;

    @NotNull(message = "User is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @NotNull(message = "Rating is required")
    @DecimalMin(value = "1.0", message = "Rating must be at least 1.0")
    @DecimalMax(value = "5.0", message = "Rating must be at most 5.0")
    @Column(name = "rating", nullable = false, precision = 3, scale = 2)
    private BigDecimal rating;

    @Size(max = 100, message = "Title cannot exceed 100 characters")
    @Column(name = "title", length = 100)
    private String title;

    @Size(max = 1000, message = "Comment cannot exceed 1000 characters")
    @Column(name = "comment", length = 1000)
    private String comment;

    @Column(name = "is_anonymous", nullable = false)
    private Boolean isAnonymous = false;

    @Column(name = "is_verified", nullable = false)
    private Boolean isVerified = false;

    @Column(name = "helpful_count", nullable = false)
    private Integer helpfulCount = 0;

    @Column(name = "not_helpful_count", nullable = false)
    private Integer notHelpfulCount = 0;

    @Column(name = "is_flagged", nullable = false)
    private Boolean isFlagged = false;

    @Column(name = "flag_reason", length = 200)
    private String flagReason;

    @Column(name = "moderated_by")
    private String moderatedBy;

    // Service-specific ratings
    @DecimalMin(value = "1.0")
    @DecimalMax(value = "5.0")
    @Column(name = "staff_rating", precision = 3, scale = 2)
    private BigDecimal staffRating;

    @DecimalMin(value = "1.0")
    @DecimalMax(value = "5.0")
    @Column(name = "cleanliness_rating", precision = 3, scale = 2)
    private BigDecimal cleanlinessRating;

    @DecimalMin(value = "1.0")
    @DecimalMax(value = "5.0")
    @Column(name = "wait_time_rating", precision = 3, scale = 2)
    private BigDecimal waitTimeRating;

    @DecimalMin(value = "1.0")
    @DecimalMax(value = "5.0")
    @Column(name = "communication_rating", precision = 3, scale = 2)
    private BigDecimal communicationRating;

    @DecimalMin(value = "1.0")
    @DecimalMax(value = "5.0")
    @Column(name = "facilities_rating", precision = 3, scale = 2)
    private BigDecimal facilitiesRating;

    // Constructors
    public FacilityReview() {}

    public FacilityReview(HealthFacility facility, User user, BigDecimal rating) {
        this.facility = facility;
        this.user = user;
        this.rating = rating;
    }

    // Getters and Setters
    public HealthFacility getFacility() {
        return facility;
    }

    public void setFacility(HealthFacility facility) {
        this.facility = facility;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public BigDecimal getRating() {
        return rating;
    }

    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Boolean getIsAnonymous() {
        return isAnonymous;
    }

    public void setIsAnonymous(Boolean isAnonymous) {
        this.isAnonymous = isAnonymous;
    }

    public Boolean getIsVerified() {
        return isVerified;
    }

    public void setIsVerified(Boolean isVerified) {
        this.isVerified = isVerified;
    }

    public Integer getHelpfulCount() {
        return helpfulCount;
    }

    public void setHelpfulCount(Integer helpfulCount) {
        this.helpfulCount = helpfulCount;
    }

    public Integer getNotHelpfulCount() {
        return notHelpfulCount;
    }

    public void setNotHelpfulCount(Integer notHelpfulCount) {
        this.notHelpfulCount = notHelpfulCount;
    }

    public Boolean getIsFlagged() {
        return isFlagged;
    }

    public void setIsFlagged(Boolean isFlagged) {
        this.isFlagged = isFlagged;
    }

    public String getFlagReason() {
        return flagReason;
    }

    public void setFlagReason(String flagReason) {
        this.flagReason = flagReason;
    }

    public String getModeratedBy() {
        return moderatedBy;
    }

    public void setModeratedBy(String moderatedBy) {
        this.moderatedBy = moderatedBy;
    }

    public BigDecimal getStaffRating() {
        return staffRating;
    }

    public void setStaffRating(BigDecimal staffRating) {
        this.staffRating = staffRating;
    }

    public BigDecimal getCleanlinessRating() {
        return cleanlinessRating;
    }

    public void setCleanlinessRating(BigDecimal cleanlinessRating) {
        this.cleanlinessRating = cleanlinessRating;
    }

    public BigDecimal getWaitTimeRating() {
        return waitTimeRating;
    }

    public void setWaitTimeRating(BigDecimal waitTimeRating) {
        this.waitTimeRating = waitTimeRating;
    }

    public BigDecimal getCommunicationRating() {
        return communicationRating;
    }

    public void setCommunicationRating(BigDecimal communicationRating) {
        this.communicationRating = communicationRating;
    }

    public BigDecimal getFacilitiesRating() {
        return facilitiesRating;
    }

    public void setFacilitiesRating(BigDecimal facilitiesRating) {
        this.facilitiesRating = facilitiesRating;
    }

    // Utility methods
    public void incrementHelpfulCount() {
        this.helpfulCount++;
    }

    public void incrementNotHelpfulCount() {
        this.notHelpfulCount++;
    }

    public void flag(String reason) {
        this.isFlagged = true;
        this.flagReason = reason;
    }

    public void unflag() {
        this.isFlagged = false;
        this.flagReason = null;
    }

    public void verify() {
        this.isVerified = true;
    }

    public void moderate(String moderator) {
        this.moderatedBy = moderator;
    }

    public BigDecimal getAverageDetailedRating() {
        BigDecimal total = BigDecimal.ZERO;
        int count = 0;

        if (staffRating != null) {
            total = total.add(staffRating);
            count++;
        }
        if (cleanlinessRating != null) {
            total = total.add(cleanlinessRating);
            count++;
        }
        if (waitTimeRating != null) {
            total = total.add(waitTimeRating);
            count++;
        }
        if (communicationRating != null) {
            total = total.add(communicationRating);
            count++;
        }
        if (facilitiesRating != null) {
            total = total.add(facilitiesRating);
            count++;
        }

        if (count == 0) {
            return rating;
        }

        return total.divide(BigDecimal.valueOf(count), 2, BigDecimal.ROUND_HALF_UP);
    }

    public boolean hasDetailedRatings() {
        return staffRating != null || cleanlinessRating != null || waitTimeRating != null ||
               communicationRating != null || facilitiesRating != null;
    }

    public int getNetHelpfulness() {
        return helpfulCount - notHelpfulCount;
    }
}
