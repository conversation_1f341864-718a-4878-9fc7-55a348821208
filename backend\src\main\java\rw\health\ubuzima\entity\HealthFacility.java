package rw.health.ubuzima.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import rw.health.ubuzima.enums.FacilityType;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;

/**
 * Health Facility entity representing health centers, hospitals, and clinics
 * 
 * <AUTHOR> Development Team
 */
@Entity
@Table(name = "health_facilities", indexes = {
    @Index(name = "idx_facility_name", columnList = "name"),
    @Index(name = "idx_facility_type", columnList = "facility_type"),
    @Index(name = "idx_facility_district", columnList = "district"),
    @Index(name = "idx_facility_location", columnList = "latitude, longitude")
})
public class HealthFacility extends BaseEntity {

    @NotBlank(message = "Facility name is required")
    @Size(min = 2, max = 100, message = "Facility name must be between 2 and 100 characters")
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "description", length = 500)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "facility_type", nullable = false)
    private FacilityType facilityType;

    @Column(name = "license_number", unique = true, length = 50)
    private String licenseNumber;

    @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Phone number should be valid")
    @Column(name = "phone_number", length = 20)
    private String phoneNumber;

    @Column(name = "email", length = 100)
    private String email;

    @Column(name = "website", length = 255)
    private String website;

    @Column(name = "address", nullable = false, length = 255)
    private String address;

    @Column(name = "district", nullable = false, length = 50)
    private String district;

    @Column(name = "sector", nullable = false, length = 50)
    private String sector;

    @Column(name = "cell", length = 50)
    private String cell;

    @Column(name = "village", length = 50)
    private String village;

    @Column(name = "latitude", precision = 10, scale = 8)
    private BigDecimal latitude;

    @Column(name = "longitude", precision = 11, scale = 8)
    private BigDecimal longitude;

    @Column(name = "operating_hours", length = 255)
    private String operatingHours;

    @Column(name = "emergency_contact", length = 20)
    private String emergencyContact;

    @Column(name = "bed_capacity")
    private Integer bedCapacity;

    @Column(name = "staff_count")
    private Integer staffCount;

    @Column(name = "services_offered", length = 1000)
    private String servicesOffered;

    @Column(name = "equipment_available", length = 1000)
    private String equipmentAvailable;

    @Column(name = "is_24_hours", nullable = false)
    private Boolean is24Hours = false;

    @Column(name = "has_emergency_services", nullable = false)
    private Boolean hasEmergencyServices = false;

    @Column(name = "has_maternity_ward", nullable = false)
    private Boolean hasMaternityWard = false;

    @Column(name = "has_family_planning", nullable = false)
    private Boolean hasFamilyPlanning = true;

    @Column(name = "has_laboratory", nullable = false)
    private Boolean hasLaboratory = false;

    @Column(name = "has_pharmacy", nullable = false)
    private Boolean hasPharmacy = false;

    @Column(name = "has_ambulance", nullable = false)
    private Boolean hasAmbulance = false;

    @Column(name = "accreditation_level", length = 50)
    private String accreditationLevel;

    @Column(name = "rating", precision = 3, scale = 2)
    private BigDecimal rating;

    @Column(name = "total_reviews")
    private Integer totalReviews = 0;

    // Relationships
    @OneToMany(mappedBy = "facility", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<User> staff = new HashSet<>();

    @OneToMany(mappedBy = "facility", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Appointment> appointments = new HashSet<>();

    @OneToMany(mappedBy = "facility", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<FacilityReview> reviews = new HashSet<>();

    // Constructors
    public HealthFacility() {}

    public HealthFacility(String name, FacilityType facilityType, String address, String district, String sector) {
        this.name = name;
        this.facilityType = facilityType;
        this.address = address;
        this.district = district;
        this.sector = sector;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public FacilityType getFacilityType() {
        return facilityType;
    }

    public void setFacilityType(FacilityType facilityType) {
        this.facilityType = facilityType;
    }

    public String getLicenseNumber() {
        return licenseNumber;
    }

    public void setLicenseNumber(String licenseNumber) {
        this.licenseNumber = licenseNumber;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getSector() {
        return sector;
    }

    public void setSector(String sector) {
        this.sector = sector;
    }

    public String getCell() {
        return cell;
    }

    public void setCell(String cell) {
        this.cell = cell;
    }

    public String getVillage() {
        return village;
    }

    public void setVillage(String village) {
        this.village = village;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public String getOperatingHours() {
        return operatingHours;
    }

    public void setOperatingHours(String operatingHours) {
        this.operatingHours = operatingHours;
    }

    public String getEmergencyContact() {
        return emergencyContact;
    }

    public void setEmergencyContact(String emergencyContact) {
        this.emergencyContact = emergencyContact;
    }

    public Integer getBedCapacity() {
        return bedCapacity;
    }

    public void setBedCapacity(Integer bedCapacity) {
        this.bedCapacity = bedCapacity;
    }

    public Integer getStaffCount() {
        return staffCount;
    }

    public void setStaffCount(Integer staffCount) {
        this.staffCount = staffCount;
    }

    public String getServicesOffered() {
        return servicesOffered;
    }

    public void setServicesOffered(String servicesOffered) {
        this.servicesOffered = servicesOffered;
    }

    public String getEquipmentAvailable() {
        return equipmentAvailable;
    }

    public void setEquipmentAvailable(String equipmentAvailable) {
        this.equipmentAvailable = equipmentAvailable;
    }

    public Boolean getIs24Hours() {
        return is24Hours;
    }

    public void setIs24Hours(Boolean is24Hours) {
        this.is24Hours = is24Hours;
    }

    public Boolean getHasEmergencyServices() {
        return hasEmergencyServices;
    }

    public void setHasEmergencyServices(Boolean hasEmergencyServices) {
        this.hasEmergencyServices = hasEmergencyServices;
    }

    public Boolean getHasMaternityWard() {
        return hasMaternityWard;
    }

    public void setHasMaternityWard(Boolean hasMaternityWard) {
        this.hasMaternityWard = hasMaternityWard;
    }

    public Boolean getHasFamilyPlanning() {
        return hasFamilyPlanning;
    }

    public void setHasFamilyPlanning(Boolean hasFamilyPlanning) {
        this.hasFamilyPlanning = hasFamilyPlanning;
    }

    public Boolean getHasLaboratory() {
        return hasLaboratory;
    }

    public void setHasLaboratory(Boolean hasLaboratory) {
        this.hasLaboratory = hasLaboratory;
    }

    public Boolean getHasPharmacy() {
        return hasPharmacy;
    }

    public void setHasPharmacy(Boolean hasPharmacy) {
        this.hasPharmacy = hasPharmacy;
    }

    public Boolean getHasAmbulance() {
        return hasAmbulance;
    }

    public void setHasAmbulance(Boolean hasAmbulance) {
        this.hasAmbulance = hasAmbulance;
    }

    public String getAccreditationLevel() {
        return accreditationLevel;
    }

    public void setAccreditationLevel(String accreditationLevel) {
        this.accreditationLevel = accreditationLevel;
    }

    public BigDecimal getRating() {
        return rating;
    }

    public void setRating(BigDecimal rating) {
        this.rating = rating;
    }

    public Integer getTotalReviews() {
        return totalReviews;
    }

    public void setTotalReviews(Integer totalReviews) {
        this.totalReviews = totalReviews;
    }

    public Set<User> getStaff() {
        return staff;
    }

    public void setStaff(Set<User> staff) {
        this.staff = staff;
    }

    public Set<Appointment> getAppointments() {
        return appointments;
    }

    public void setAppointments(Set<Appointment> appointments) {
        this.appointments = appointments;
    }

    public Set<FacilityReview> getReviews() {
        return reviews;
    }

    public void setReviews(Set<FacilityReview> reviews) {
        this.reviews = reviews;
    }

    // Utility methods
    public String getFullAddress() {
        StringBuilder address = new StringBuilder();
        address.append(this.address);
        if (village != null) address.append(", ").append(village);
        if (cell != null) address.append(", ").append(cell);
        address.append(", ").append(sector);
        address.append(", ").append(district);
        return address.toString();
    }

    public boolean hasCoordinates() {
        return latitude != null && longitude != null;
    }

    public void updateRating(BigDecimal newRating) {
        if (this.rating == null) {
            this.rating = newRating;
            this.totalReviews = 1;
        } else {
            BigDecimal totalScore = this.rating.multiply(BigDecimal.valueOf(this.totalReviews));
            totalScore = totalScore.add(newRating);
            this.totalReviews++;
            this.rating = totalScore.divide(BigDecimal.valueOf(this.totalReviews), 2, BigDecimal.ROUND_HALF_UP);
        }
    }
}
