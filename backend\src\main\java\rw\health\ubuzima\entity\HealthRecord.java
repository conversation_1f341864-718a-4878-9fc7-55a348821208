package rw.health.ubuzima.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import rw.health.ubuzima.enums.RecordType;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Health Record entity representing medical records and health data for clients
 * 
 * <AUTHOR> Development Team
 */
@Entity
@Table(name = "health_records", indexes = {
    @Index(name = "idx_health_record_client", columnList = "client_id"),
    @Index(name = "idx_health_record_type", columnList = "record_type"),
    @Index(name = "idx_health_record_date", columnList = "record_date"),
    @Index(name = "idx_health_record_health_worker", columnList = "health_worker_id")
})
public class HealthRecord extends BaseEntity {

    @NotNull(message = "Client is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_id", nullable = false)
    private User client;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "health_worker_id")
    private User healthWorker;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "facility_id")
    private HealthFacility facility;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "appointment_id")
    private Appointment appointment;

    @NotNull(message = "Record type is required")
    @Enumerated(EnumType.STRING)
    @Column(name = "record_type", nullable = false)
    private RecordType recordType;

    @NotNull(message = "Record date is required")
    @Column(name = "record_date", nullable = false)
    private LocalDate recordDate;

    @Size(max = 100, message = "Title cannot exceed 100 characters")
    @Column(name = "title", length = 100)
    private String title;

    @Size(max = 2000, message = "Description cannot exceed 2000 characters")
    @Column(name = "description", length = 2000)
    private String description;

    @Size(max = 1000, message = "Symptoms cannot exceed 1000 characters")
    @Column(name = "symptoms", length = 1000)
    private String symptoms;

    @Size(max = 1000, message = "Diagnosis cannot exceed 1000 characters")
    @Column(name = "diagnosis", length = 1000)
    private String diagnosis;

    @Size(max = 1000, message = "Treatment cannot exceed 1000 characters")
    @Column(name = "treatment", length = 1000)
    private String treatment;

    @Size(max = 1000, message = "Medications cannot exceed 1000 characters")
    @Column(name = "medications", length = 1000)
    private String medications;

    @Size(max = 1000, message = "Recommendations cannot exceed 1000 characters")
    @Column(name = "recommendations", length = 1000)
    private String recommendations;

    @Size(max = 500, message = "Follow up instructions cannot exceed 500 characters")
    @Column(name = "follow_up_instructions", length = 500)
    private String followUpInstructions;

    @Column(name = "follow_up_date")
    private LocalDate followUpDate;

    // Vital Signs
    @Column(name = "weight_kg", precision = 5, scale = 2)
    private Double weightKg;

    @Column(name = "height_cm", precision = 5, scale = 2)
    private Double heightCm;

    @Column(name = "bmi", precision = 4, scale = 2)
    private Double bmi;

    @Column(name = "blood_pressure_systolic")
    private Integer bloodPressureSystolic;

    @Column(name = "blood_pressure_diastolic")
    private Integer bloodPressureDiastolic;

    @Column(name = "heart_rate")
    private Integer heartRate;

    @Column(name = "temperature_celsius", precision = 4, scale = 2)
    private Double temperatureCelsius;

    @Column(name = "respiratory_rate")
    private Integer respiratoryRate;

    @Column(name = "oxygen_saturation")
    private Integer oxygenSaturation;

    // Family Planning Specific
    @Column(name = "contraceptive_method", length = 100)
    private String contraceptiveMethod;

    @Column(name = "contraceptive_start_date")
    private LocalDate contraceptiveStartDate;

    @Column(name = "last_menstrual_period")
    private LocalDate lastMenstrualPeriod;

    @Column(name = "cycle_length_days")
    private Integer cycleLengthDays;

    @Column(name = "is_pregnant")
    private Boolean isPregnant;

    @Column(name = "pregnancy_weeks")
    private Integer pregnancyWeeks;

    @Column(name = "expected_delivery_date")
    private LocalDate expectedDeliveryDate;

    @Column(name = "number_of_pregnancies")
    private Integer numberOfPregnancies;

    @Column(name = "number_of_births")
    private Integer numberOfBirths;

    @Column(name = "number_of_miscarriages")
    private Integer numberOfMiscarriages;

    // Laboratory Results
    @Column(name = "lab_results", columnDefinition = "TEXT")
    private String labResults;

    @Column(name = "hiv_status", length = 20)
    private String hivStatus;

    @Column(name = "hiv_test_date")
    private LocalDate hivTestDate;

    @Column(name = "sti_screening_results", length = 500)
    private String stiScreeningResults;

    @Column(name = "sti_test_date")
    private LocalDate stiTestDate;

    // Additional Data (JSON format)
    @ElementCollection
    @CollectionTable(name = "health_record_data", joinColumns = @JoinColumn(name = "health_record_id"))
    @MapKeyColumn(name = "data_key")
    @Column(name = "data_value", length = 1000)
    private Map<String, String> additionalData = new HashMap<>();

    @Column(name = "is_confidential", nullable = false)
    private Boolean isConfidential = false;

    @Column(name = "is_emergency", nullable = false)
    private Boolean isEmergency = false;

    @Size(max = 500, message = "Notes cannot exceed 500 characters")
    @Column(name = "notes", length = 500)
    private String notes;

    @Column(name = "next_appointment_recommended")
    private LocalDate nextAppointmentRecommended;

    // Constructors
    public HealthRecord() {}

    public HealthRecord(User client, RecordType recordType, LocalDate recordDate) {
        this.client = client;
        this.recordType = recordType;
        this.recordDate = recordDate;
    }

    // Getters and Setters
    public User getClient() {
        return client;
    }

    public void setClient(User client) {
        this.client = client;
    }

    public User getHealthWorker() {
        return healthWorker;
    }

    public void setHealthWorker(User healthWorker) {
        this.healthWorker = healthWorker;
    }

    public HealthFacility getFacility() {
        return facility;
    }

    public void setFacility(HealthFacility facility) {
        this.facility = facility;
    }

    public Appointment getAppointment() {
        return appointment;
    }

    public void setAppointment(Appointment appointment) {
        this.appointment = appointment;
    }

    public RecordType getRecordType() {
        return recordType;
    }

    public void setRecordType(RecordType recordType) {
        this.recordType = recordType;
    }

    public LocalDate getRecordDate() {
        return recordDate;
    }

    public void setRecordDate(LocalDate recordDate) {
        this.recordDate = recordDate;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSymptoms() {
        return symptoms;
    }

    public void setSymptoms(String symptoms) {
        this.symptoms = symptoms;
    }

    public String getDiagnosis() {
        return diagnosis;
    }

    public void setDiagnosis(String diagnosis) {
        this.diagnosis = diagnosis;
    }

    public String getTreatment() {
        return treatment;
    }

    public void setTreatment(String treatment) {
        this.treatment = treatment;
    }

    public String getMedications() {
        return medications;
    }

    public void setMedications(String medications) {
        this.medications = medications;
    }

    public String getRecommendations() {
        return recommendations;
    }

    public void setRecommendations(String recommendations) {
        this.recommendations = recommendations;
    }

    public String getFollowUpInstructions() {
        return followUpInstructions;
    }

    public void setFollowUpInstructions(String followUpInstructions) {
        this.followUpInstructions = followUpInstructions;
    }

    public LocalDate getFollowUpDate() {
        return followUpDate;
    }

    public void setFollowUpDate(LocalDate followUpDate) {
        this.followUpDate = followUpDate;
    }

    // Vital Signs Getters and Setters
    public Double getWeightKg() {
        return weightKg;
    }

    public void setWeightKg(Double weightKg) {
        this.weightKg = weightKg;
        calculateBMI();
    }

    public Double getHeightCm() {
        return heightCm;
    }

    public void setHeightCm(Double heightCm) {
        this.heightCm = heightCm;
        calculateBMI();
    }

    public Double getBmi() {
        return bmi;
    }

    public void setBmi(Double bmi) {
        this.bmi = bmi;
    }

    public Integer getBloodPressureSystolic() {
        return bloodPressureSystolic;
    }

    public void setBloodPressureSystolic(Integer bloodPressureSystolic) {
        this.bloodPressureSystolic = bloodPressureSystolic;
    }

    public Integer getBloodPressureDiastolic() {
        return bloodPressureDiastolic;
    }

    public void setBloodPressureDiastolic(Integer bloodPressureDiastolic) {
        this.bloodPressureDiastolic = bloodPressureDiastolic;
    }

    public Integer getHeartRate() {
        return heartRate;
    }

    public void setHeartRate(Integer heartRate) {
        this.heartRate = heartRate;
    }

    public Double getTemperatureCelsius() {
        return temperatureCelsius;
    }

    public void setTemperatureCelsius(Double temperatureCelsius) {
        this.temperatureCelsius = temperatureCelsius;
    }

    public Integer getRespiratoryRate() {
        return respiratoryRate;
    }

    public void setRespiratoryRate(Integer respiratoryRate) {
        this.respiratoryRate = respiratoryRate;
    }

    public Integer getOxygenSaturation() {
        return oxygenSaturation;
    }

    public void setOxygenSaturation(Integer oxygenSaturation) {
        this.oxygenSaturation = oxygenSaturation;
    }

    // Family Planning Getters and Setters
    public String getContraceptiveMethod() {
        return contraceptiveMethod;
    }

    public void setContraceptiveMethod(String contraceptiveMethod) {
        this.contraceptiveMethod = contraceptiveMethod;
    }

    public LocalDate getContraceptiveStartDate() {
        return contraceptiveStartDate;
    }

    public void setContraceptiveStartDate(LocalDate contraceptiveStartDate) {
        this.contraceptiveStartDate = contraceptiveStartDate;
    }

    public LocalDate getLastMenstrualPeriod() {
        return lastMenstrualPeriod;
    }

    public void setLastMenstrualPeriod(LocalDate lastMenstrualPeriod) {
        this.lastMenstrualPeriod = lastMenstrualPeriod;
    }

    public Integer getCycleLengthDays() {
        return cycleLengthDays;
    }

    public void setCycleLengthDays(Integer cycleLengthDays) {
        this.cycleLengthDays = cycleLengthDays;
    }

    public Boolean getIsPregnant() {
        return isPregnant;
    }

    public void setIsPregnant(Boolean isPregnant) {
        this.isPregnant = isPregnant;
    }

    public Integer getPregnancyWeeks() {
        return pregnancyWeeks;
    }

    public void setPregnancyWeeks(Integer pregnancyWeeks) {
        this.pregnancyWeeks = pregnancyWeeks;
    }

    public LocalDate getExpectedDeliveryDate() {
        return expectedDeliveryDate;
    }

    public void setExpectedDeliveryDate(LocalDate expectedDeliveryDate) {
        this.expectedDeliveryDate = expectedDeliveryDate;
    }

    public Integer getNumberOfPregnancies() {
        return numberOfPregnancies;
    }

    public void setNumberOfPregnancies(Integer numberOfPregnancies) {
        this.numberOfPregnancies = numberOfPregnancies;
    }

    public Integer getNumberOfBirths() {
        return numberOfBirths;
    }

    public void setNumberOfBirths(Integer numberOfBirths) {
        this.numberOfBirths = numberOfBirths;
    }

    public Integer getNumberOfMiscarriages() {
        return numberOfMiscarriages;
    }

    public void setNumberOfMiscarriages(Integer numberOfMiscarriages) {
        this.numberOfMiscarriages = numberOfMiscarriages;
    }

    // Laboratory Results Getters and Setters
    public String getLabResults() {
        return labResults;
    }

    public void setLabResults(String labResults) {
        this.labResults = labResults;
    }

    public String getHivStatus() {
        return hivStatus;
    }

    public void setHivStatus(String hivStatus) {
        this.hivStatus = hivStatus;
    }

    public LocalDate getHivTestDate() {
        return hivTestDate;
    }

    public void setHivTestDate(LocalDate hivTestDate) {
        this.hivTestDate = hivTestDate;
    }

    public String getStiScreeningResults() {
        return stiScreeningResults;
    }

    public void setStiScreeningResults(String stiScreeningResults) {
        this.stiScreeningResults = stiScreeningResults;
    }

    public LocalDate getStiTestDate() {
        return stiTestDate;
    }

    public void setStiTestDate(LocalDate stiTestDate) {
        this.stiTestDate = stiTestDate;
    }

    public Map<String, String> getAdditionalData() {
        return additionalData;
    }

    public void setAdditionalData(Map<String, String> additionalData) {
        this.additionalData = additionalData;
    }

    public Boolean getIsConfidential() {
        return isConfidential;
    }

    public void setIsConfidential(Boolean isConfidential) {
        this.isConfidential = isConfidential;
    }

    public Boolean getIsEmergency() {
        return isEmergency;
    }

    public void setIsEmergency(Boolean isEmergency) {
        this.isEmergency = isEmergency;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public LocalDate getNextAppointmentRecommended() {
        return nextAppointmentRecommended;
    }

    public void setNextAppointmentRecommended(LocalDate nextAppointmentRecommended) {
        this.nextAppointmentRecommended = nextAppointmentRecommended;
    }

    // Utility methods
    private void calculateBMI() {
        if (weightKg != null && heightCm != null && heightCm > 0) {
            double heightM = heightCm / 100.0;
            this.bmi = weightKg / (heightM * heightM);
            this.bmi = Math.round(this.bmi * 100.0) / 100.0; // Round to 2 decimal places
        }
    }

    public String getBloodPressure() {
        if (bloodPressureSystolic != null && bloodPressureDiastolic != null) {
            return bloodPressureSystolic + "/" + bloodPressureDiastolic;
        }
        return null;
    }

    public boolean isVitalSignsComplete() {
        return weightKg != null && heightCm != null && bloodPressureSystolic != null && 
               bloodPressureDiastolic != null && heartRate != null && temperatureCelsius != null;
    }

    public boolean requiresFollowUp() {
        return followUpDate != null && followUpDate.isAfter(LocalDate.now());
    }

    public void addAdditionalData(String key, String value) {
        if (additionalData == null) {
            additionalData = new HashMap<>();
        }
        additionalData.put(key, value);
    }

    public String getAdditionalData(String key) {
        return additionalData != null ? additionalData.get(key) : null;
    }
}
