package rw.health.ubuzima.enums;

/**
 * Enumeration for appointment status in the Ubuzima system
 * 
 * <AUTHOR> Development Team
 */
public enum AppointmentStatus {
    SCHEDULED("Scheduled", "Ya<PERSON>gu<PERSON>"),
    CONFIRMED("Confirmed", "<PERSON><PERSON><PERSON><PERSON>"),
    CHECKED_IN("Checked In", "<PERSON><PERSON><PERSON>"),
    IN_PROGRESS("In Progress", "Iragenda"),
    COMPLETED("Completed", "Yarangiye"),
    CANCELLED("Cancelled", "Yahagaritswe"),
    NO_SHOW("No Show", "Ntiyaje"),
    RESCHEDULED("Rescheduled", "Yahinduwe");

    private final String displayName;
    private final String kinyarwandaName;

    AppointmentStatus(String displayName, String kinyarwandaName) {
        this.displayName = displayName;
        this.kinyarwandaName = kinyarwandaName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getKinyarwandaName() {
        return kinyarwandaName;
    }

    public boolean isActive() {
        return this == SCHEDULED || this == CONFIRMED || this == CHECKED_IN || this == IN_PROGRESS;
    }

    public boolean isCompleted() {
        return this == COMPLETED;
    }

    public boolean isCancelled() {
        return this == CANCELLED || this == NO_SHOW;
    }

    public boolean canBeModified() {
        return this == SCHEDULED || this == CONFIRMED;
    }

    public boolean requiresAction() {
        return this == CHECKED_IN || this == IN_PROGRESS;
    }
}
