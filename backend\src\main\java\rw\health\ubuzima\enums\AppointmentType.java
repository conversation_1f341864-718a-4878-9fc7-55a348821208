package rw.health.ubuzima.enums;

/**
 * Enumeration for appointment types in the Ubuzima system
 * 
 * <AUTHOR> Development Team
 */
public enum AppointmentType {
    FAMILY_PLANNING("Family Planning", "Kurinda inda"),
    PRENATAL_CARE("Prenatal Care", "Kwita ku nda"),
    POSTNATAL_CARE("Postnatal Care", "K<PERSON><PERSON> nyuma yo kubyara"),
    CONTRACEPTION_CONSULTATION("Contraception Consultation", "Inama yo kurinda inda"),
    STI_SCREENING("STI Screening", "Gusuzuma indwara zandurira"),
    GENERAL_CONSULTATION("General Consultation", "Inama rusange"),
    FOLLOW_UP("Follow-up", "Guku<PERSON><PERSON><PERSON>"),
    EMERGENCY("Emergency", "Byihutirwa"),
    VACCINATION("Vaccination", "Gukingira"),
    HEALTH_EDUCATION("Health Education", "Kwigisha ubuzima"),
    COUNSELING("Counseling", "Ubujyana<PERSON>"),
    LABORATORY_TESTS("Laboratory Tests", "Ibizamini bya laboratoire");

    private final String displayName;
    private final String kinyarwandaName;

    AppointmentType(String displayName, String kinyarwandaName) {
        this.displayName = displayName;
        this.kinyarwandaName = kinyarwandaName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getKinyarwandaName() {
        return kinyarwandaName;
    }

    public boolean isFamilyPlanningRelated() {
        return this == FAMILY_PLANNING || this == CONTRACEPTION_CONSULTATION;
    }

    public boolean isMaternityRelated() {
        return this == PRENATAL_CARE || this == POSTNATAL_CARE;
    }

    public boolean isEmergency() {
        return this == EMERGENCY;
    }

    public int getDefaultDurationMinutes() {
        switch (this) {
            case EMERGENCY:
                return 60;
            case FAMILY_PLANNING:
            case CONTRACEPTION_CONSULTATION:
            case COUNSELING:
                return 45;
            case PRENATAL_CARE:
            case POSTNATAL_CARE:
                return 30;
            case HEALTH_EDUCATION:
                return 60;
            case LABORATORY_TESTS:
                return 15;
            default:
                return 30;
        }
    }
}
