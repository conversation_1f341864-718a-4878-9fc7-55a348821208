package rw.health.ubuzima.enums;

/**
 * Enumeration for audio content categories in the Ubuzima system
 * 
 * <AUTHOR> Development Team
 */
public enum ContentCategory {
    FAMILY_PLANNING("Family Planning", "Kurinda inda"),
    CONTRACEPTION("Contraception", "Kurinda inda"),
    REPRODUCTIVE_HEALTH("Reproductive Health", "Ubuzima bw'imyororokere"),
    MATERNAL_HEALTH("Maternal Health", "Ubuzima bw'ababyeyi"),
    PRENATAL_CARE("Prenatal Care", "Kwita ku nda"),
    POSTNATAL_CARE("Postnatal Care", "K<PERSON>ta nyuma yo kubyara"),
    ADOLESCENT_HEALTH("Adolescent Health", "Ubuzima bw'ingimbi"),
    SEXUAL_HEALTH("Sexual Health", "Ubuzima bw'imibonano mpuzabitsina"),
    STI_PREVENTION("STI Prevention", "Kurinda indwara zandurira"),
    HIV_AIDS("HIV/AIDS", "SIDA"),
    NUTRITION("Nutrition", "Imirire myiza"),
    HYGIENE("Hygiene", "Isuku"),
    MENTAL_HEALTH("Mental Health", "Ubuzima bwo mu mutwe"),
    GENERAL_HEALTH("General Health", "Ubuzima rusange"),
    EMERGENCY_CARE("Emergency Care", "Ubufasha bw'ihutirwa"),
    CHILD_HEALTH("Child Health", "Ubuzima bw'abana"),
    VACCINATION("Vaccination", "Gukingira"),
    BREASTFEEDING("Breastfeeding", "Konka"),
    HEALTH_EDUCATION("Health Education", "Kwigisha ubuzima"),
    COMMUNITY_HEALTH("Community Health", "Ubuzima bw'abaturage");

    private final String displayName;
    private final String kinyarwandaName;

    ContentCategory(String displayName, String kinyarwandaName) {
        this.displayName = displayName;
        this.kinyarwandaName = kinyarwandaName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getKinyarwandaName() {
        return kinyarwandaName;
    }

    public boolean isFamilyPlanningRelated() {
        return this == FAMILY_PLANNING || this == CONTRACEPTION || this == REPRODUCTIVE_HEALTH;
    }

    public boolean isMaternityRelated() {
        return this == MATERNAL_HEALTH || this == PRENATAL_CARE || this == POSTNATAL_CARE || this == BREASTFEEDING;
    }

    public boolean isYouthRelated() {
        return this == ADOLESCENT_HEALTH || this == SEXUAL_HEALTH;
    }

    public boolean isPreventionRelated() {
        return this == STI_PREVENTION || this == HIV_AIDS || this == VACCINATION;
    }

    public String getIcon() {
        switch (this) {
            case FAMILY_PLANNING:
            case CONTRACEPTION:
                return "family_planning";
            case REPRODUCTIVE_HEALTH:
            case SEXUAL_HEALTH:
                return "reproductive_health";
            case MATERNAL_HEALTH:
            case PRENATAL_CARE:
            case POSTNATAL_CARE:
                return "maternal_health";
            case ADOLESCENT_HEALTH:
                return "youth_health";
            case STI_PREVENTION:
            case HIV_AIDS:
                return "prevention";
            case NUTRITION:
                return "nutrition";
            case HYGIENE:
                return "hygiene";
            case MENTAL_HEALTH:
                return "mental_health";
            case EMERGENCY_CARE:
                return "emergency";
            case CHILD_HEALTH:
                return "child_health";
            case VACCINATION:
                return "vaccination";
            case BREASTFEEDING:
                return "breastfeeding";
            default:
                return "general_health";
        }
    }

    public String getColor() {
        switch (this) {
            case FAMILY_PLANNING:
            case CONTRACEPTION:
                return "#FF6B35"; // Orange
            case REPRODUCTIVE_HEALTH:
            case SEXUAL_HEALTH:
                return "#E91E63"; // Pink
            case MATERNAL_HEALTH:
            case PRENATAL_CARE:
            case POSTNATAL_CARE:
                return "#9C27B0"; // Purple
            case ADOLESCENT_HEALTH:
                return "#2196F3"; // Blue
            case STI_PREVENTION:
            case HIV_AIDS:
                return "#F44336"; // Red
            case NUTRITION:
                return "#4CAF50"; // Green
            case HYGIENE:
                return "#00BCD4"; // Cyan
            case MENTAL_HEALTH:
                return "#673AB7"; // Deep Purple
            case EMERGENCY_CARE:
                return "#FF5722"; // Deep Orange
            case CHILD_HEALTH:
                return "#FFEB3B"; // Yellow
            case VACCINATION:
                return "#795548"; // Brown
            case BREASTFEEDING:
                return "#FFC107"; // Amber
            default:
                return "#607D8B"; // Blue Grey
        }
    }
}
