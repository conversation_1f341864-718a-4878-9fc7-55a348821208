package rw.health.ubuzima.enums;

/**
 * Enumeration for content status in the Ubuzima system
 * 
 * <AUTHOR> Development Team
 */
public enum ContentStatus {
    DRAFT("Draft", "<PERSON>gishushanyo"),
    UNDER_REVIEW("Under Review", "Gisuzumwa"),
    APPROVED("Approved", "<PERSON>emej<PERSON>"),
    PUBLISHED("Published", "Cyasohowe"),
    ARCHIVED("Archived", "Cyashyizwe mu bubiko"),
    REJECTED("Rejected", "Cyanze"),
    EXPIRED("Expired", "Cyarangiye");

    private final String displayName;
    private final String kinyarwandaName;

    ContentStatus(String displayName, String kinyarwandaName) {
        this.displayName = displayName;
        this.kinyarwandaName = kinyarwandaName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getKinyarwandaName() {
        return kinyarwandaName;
    }

    public boolean isPublic() {
        return this == PUBLISHED;
    }

    public boolean canBeEdited() {
        return this == DRAFT || this == REJECTED;
    }

    public boolean requiresReview() {
        return this == UNDER_REVIEW;
    }

    public boolean isActive() {
        return this == PUBLISHED;
    }

    public String getColor() {
        switch (this) {
            case DRAFT:
                return "#9E9E9E"; // Grey
            case UNDER_REVIEW:
                return "#FF9800"; // Orange
            case APPROVED:
                return "#2196F3"; // Blue
            case PUBLISHED:
                return "#4CAF50"; // Green
            case ARCHIVED:
                return "#607D8B"; // Blue Grey
            case REJECTED:
                return "#F44336"; // Red
            case EXPIRED:
                return "#795548"; // Brown
            default:
                return "#9E9E9E"; // Grey
        }
    }
}
