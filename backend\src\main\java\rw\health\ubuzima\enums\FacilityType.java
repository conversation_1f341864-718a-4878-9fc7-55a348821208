package rw.health.ubuzima.enums;

/**
 * Enumeration for health facility types in the Ubuzima system
 * 
 * <AUTHOR> Development Team
 */
public enum FacilityType {
    HOSPITAL("Hospital", "Ibitaro"),
    HEALTH_CENTER("Health Center", "Ikigo cy'ubuzima"),
    CLINIC("Clinic", "Kliniki"),
    DISPENSARY("Dispensary", "Farumasi"),
    MATERNITY("Maternity", "Uruhinja"),
    LABORATORY("Laboratory", "Laboratoire"),
    PHARMACY("Pharmacy", "Farumasi"),
    COMMUNITY_HEALTH_POST("Community Health Post", "Ikigo cy'ubuzima cy'abaturage");

    private final String displayName;
    private final String kinyarwandaName;

    FacilityType(String displayName, String kinyarwandaName) {
        this.displayName = displayName;
        this.kinyarwandaName = kinyarwandaName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getKinyarwandaName() {
        return kinyarwandaName;
    }

    public boolean isHospital() {
        return this == HOSPITAL;
    }

    public boolean isHealthCenter() {
        return this == HEALTH_CENTER;
    }

    public boolean providesEmergencyServices() {
        return this == HOSPITAL || this == HEALTH_CENTER;
    }

    public boolean providesMaternityServices() {
        return this == HOSPITAL || this == HEALTH_CENTER || this == MATERNITY;
    }
}
