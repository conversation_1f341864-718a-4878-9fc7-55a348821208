package rw.health.ubuzima.enums;

/**
 * Enumeration for gender in the Ubuzima system
 * 
 * <AUTHOR> Development Team
 */
public enum Gender {
    MALE("Male", "Gabo"),
    FEMALE("Female", "<PERSON>"),
    OTHER("Other", "<PERSON><PERSON>i"),
    PREFER_NOT_TO_SAY("Prefer not to say", "Sinshaka kubivuga");

    private final String displayName;
    private final String kinyarwandaName;

    Gender(String displayName, String kinyarwandaName) {
        this.displayName = displayName;
        this.kinyarwandaName = kinyarwandaName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getKinyarwandaName() {
        return kinyarwandaName;
    }
}
