package rw.health.ubuzima.enums;

/**
 * Enumeration for health record types in the Ubuzima system
 * 
 * <AUTHOR> Development Team
 */
public enum RecordType {
    CONSULTATION("Consultation", "Inama"),
    FAMILY_PLANNING("Family Planning", "Kurinda inda"),
    PRENATAL_CARE("Prenatal Care", "Kwita ku nda"),
    POSTNATAL_CARE("Postnatal Care", "Kwita nyuma yo kubyara"),
    CONTRACEPTION("Contraception", "Kurinda inda"),
    STI_SCREENING("STI Screening", "Gusuzuma indwara zandurira"),
    HIV_TESTING("HIV Testing", "Gusuzuma SIDA"),
    VACCINATION("Vaccination", "<PERSON>ukingira"),
    LABORATORY_RESULTS("Laboratory Results", "Ibisubizo bya laboratoire"),
    VITAL_SIGNS("Vital Signs", "Ibimenyetso by'ubuzima"),
    MEDICATION("Medication", "<PERSON>mit<PERSON>"),
    EMERGENCY("Emergency", "<PERSON><PERSON><PERSON>rwa"),
    FOLLOW_UP("Follow-up", "<PERSON><PERSON><PERSON><PERSON><PERSON>"),
    HEALTH_EDUCATION("Health Education", "Kwigisha ubuzima"),
    COUNSELING("Counseling", "Ubujyanama"),
    DISCHARGE_SUMMARY("Discharge Summary", "Incamake y'kuva mu bitaro"),
    REFERRAL("Referral", "Kohereza"),
    SURGERY("Surgery", "Kubaga"),
    THERAPY("Therapy", "Ubuvuzi"),
    MENTAL_HEALTH("Mental Health", "Ubuzima bwo mu mutwe");

    private final String displayName;
    private final String kinyarwandaName;

    RecordType(String displayName, String kinyarwandaName) {
        this.displayName = displayName;
        this.kinyarwandaName = kinyarwandaName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getKinyarwandaName() {
        return kinyarwandaName;
    }

    public boolean isFamilyPlanningRelated() {
        return this == FAMILY_PLANNING || this == CONTRACEPTION || this == PRENATAL_CARE || this == POSTNATAL_CARE;
    }

    public boolean isScreeningRelated() {
        return this == STI_SCREENING || this == HIV_TESTING || this == LABORATORY_RESULTS;
    }

    public boolean isEmergencyRelated() {
        return this == EMERGENCY;
    }

    public boolean requiresSpecialPermissions() {
        return this == HIV_TESTING || this == STI_SCREENING || this == MENTAL_HEALTH;
    }

    public boolean isConfidentialByDefault() {
        return this == HIV_TESTING || this == STI_SCREENING || this == MENTAL_HEALTH || this == COUNSELING;
    }
}
