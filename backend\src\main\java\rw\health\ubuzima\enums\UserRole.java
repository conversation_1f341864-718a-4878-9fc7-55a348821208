package rw.health.ubuzima.enums;

/**
 * Enumeration for user roles in the Ubuzima system
 * 
 * <AUTHOR> Development Team
 */
public enum UserRole {
    CLIENT("Client", "Umunyangire"),
    HEALTH_WORKER("Health Worker", "<PERSON><PERSON><PERSON><PERSON> w'ubuzima"),
    ADMIN("Administrator", "Umuyobozi"),
    SUPER_ADMIN("Super Administrator", "Umuyobozi Mukuru");

    private final String displayName;
    private final String kinyarwandaName;

    UserRole(String displayName, String kinyarwandaName) {
        this.displayName = displayName;
        this.kinyarwandaName = kinyarwandaName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getKinyarwandaName() {
        return kinyarwandaName;
    }

    public boolean isAdmin() {
        return this == ADMIN || this == SUPER_ADMIN;
    }

    public boolean isHealthWorker() {
        return this == HEALTH_WORKER;
    }

    public boolean isClient() {
        return this == CLIENT;
    }
}
