package rw.health.ubuzima.enums;

/**
 * Enumeration for user status in the Ubuzima system
 * 
 * <AUTHOR> Development Team
 */
public enum UserStatus {
    ACTIVE("Active", "Akora"),
    INACTIVE("Inactive", "Ntakora"),
    SUSPENDED("Suspended", "Yahagaritswe"),
    PENDING_VERIFICATION("Pending Verification", "Ategereje kwemezwa"),
    LOCKED("Locked", "Yafunzwe");

    private final String displayName;
    private final String kinyarwandaName;

    UserStatus(String displayName, String kinyarwandaName) {
        this.displayName = displayName;
        this.kinyarwandaName = kinyarwandaName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getKinyarwandaName() {
        return kinyarwandaName;
    }

    public boolean isActive() {
        return this == ACTIVE;
    }

    public boolean canLogin() {
        return this == ACTIVE;
    }
}
