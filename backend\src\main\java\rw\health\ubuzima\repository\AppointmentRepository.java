package rw.health.ubuzima.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import rw.health.ubuzima.entity.Appointment;
import rw.health.ubuzima.entity.HealthFacility;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.AppointmentStatus;
import rw.health.ubuzima.enums.AppointmentType;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Appointment entity operations
 * 
 * <AUTHOR> Development Team
 */
@Repository
public interface AppointmentRepository extends JpaRepository<Appointment, UUID> {

    // Client-based queries
    List<Appointment> findByClient(User client);
    
    Page<Appointment> findByClient(User client, Pageable pageable);
    
    List<Appointment> findByClientAndStatus(User client, AppointmentStatus status);
    
    Page<Appointment> findByClientAndStatus(User client, AppointmentStatus status, Pageable pageable);
    
    @Query("SELECT a FROM Appointment a WHERE a.client.id = :clientId ORDER BY a.appointmentDate DESC")
    Page<Appointment> findByClientId(@Param("clientId") UUID clientId, Pageable pageable);

    // Health worker-based queries
    List<Appointment> findByHealthWorker(User healthWorker);
    
    Page<Appointment> findByHealthWorker(User healthWorker, Pageable pageable);
    
    List<Appointment> findByHealthWorkerAndStatus(User healthWorker, AppointmentStatus status);
    
    Page<Appointment> findByHealthWorkerAndStatus(User healthWorker, AppointmentStatus status, Pageable pageable);
    
    @Query("SELECT a FROM Appointment a WHERE a.healthWorker.id = :healthWorkerId ORDER BY a.appointmentDate ASC")
    Page<Appointment> findByHealthWorkerId(@Param("healthWorkerId") UUID healthWorkerId, Pageable pageable);

    // Facility-based queries
    List<Appointment> findByFacility(HealthFacility facility);
    
    Page<Appointment> findByFacility(HealthFacility facility, Pageable pageable);
    
    List<Appointment> findByFacilityAndStatus(HealthFacility facility, AppointmentStatus status);
    
    Page<Appointment> findByFacilityAndStatus(HealthFacility facility, AppointmentStatus status, Pageable pageable);

    // Status-based queries
    List<Appointment> findByStatus(AppointmentStatus status);
    
    Page<Appointment> findByStatus(AppointmentStatus status, Pageable pageable);
    
    List<Appointment> findByStatusIn(List<AppointmentStatus> statuses);
    
    Page<Appointment> findByStatusIn(List<AppointmentStatus> statuses, Pageable pageable);

    // Type-based queries
    List<Appointment> findByAppointmentType(AppointmentType appointmentType);
    
    Page<Appointment> findByAppointmentType(AppointmentType appointmentType, Pageable pageable);

    // Date-based queries
    List<Appointment> findByAppointmentDateBetween(LocalDateTime start, LocalDateTime end);
    
    Page<Appointment> findByAppointmentDateBetween(LocalDateTime start, LocalDateTime end, Pageable pageable);
    
    @Query("SELECT a FROM Appointment a WHERE DATE(a.appointmentDate) = :date")
    List<Appointment> findByAppointmentDate(@Param("date") LocalDate date);
    
    @Query("SELECT a FROM Appointment a WHERE DATE(a.appointmentDate) = :date AND a.facility.id = :facilityId")
    List<Appointment> findByAppointmentDateAndFacility(@Param("date") LocalDate date, @Param("facilityId") UUID facilityId);

    // Upcoming appointments
    @Query("SELECT a FROM Appointment a WHERE a.appointmentDate > :now AND a.status IN ('SCHEDULED', 'CONFIRMED') ORDER BY a.appointmentDate ASC")
    List<Appointment> findUpcomingAppointments(@Param("now") LocalDateTime now);
    
    @Query("SELECT a FROM Appointment a WHERE a.client.id = :clientId AND a.appointmentDate > :now AND a.status IN ('SCHEDULED', 'CONFIRMED') ORDER BY a.appointmentDate ASC")
    List<Appointment> findUpcomingAppointmentsByClient(@Param("clientId") UUID clientId, @Param("now") LocalDateTime now);
    
    @Query("SELECT a FROM Appointment a WHERE a.healthWorker.id = :healthWorkerId AND a.appointmentDate > :now AND a.status IN ('SCHEDULED', 'CONFIRMED') ORDER BY a.appointmentDate ASC")
    List<Appointment> findUpcomingAppointmentsByHealthWorker(@Param("healthWorkerId") UUID healthWorkerId, @Param("now") LocalDateTime now);

    // Today's appointments
    @Query("SELECT a FROM Appointment a WHERE DATE(a.appointmentDate) = CURRENT_DATE AND a.facility.id = :facilityId ORDER BY a.appointmentDate ASC")
    List<Appointment> findTodaysAppointmentsByFacility(@Param("facilityId") UUID facilityId);
    
    @Query("SELECT a FROM Appointment a WHERE DATE(a.appointmentDate) = CURRENT_DATE AND a.healthWorker.id = :healthWorkerId ORDER BY a.appointmentDate ASC")
    List<Appointment> findTodaysAppointmentsByHealthWorker(@Param("healthWorkerId") UUID healthWorkerId);

    // Emergency appointments
    List<Appointment> findByIsEmergencyTrue();
    
    @Query("SELECT a FROM Appointment a WHERE a.isEmergency = true AND a.facility.id = :facilityId ORDER BY a.appointmentDate ASC")
    List<Appointment> findEmergencyAppointmentsByFacility(@Param("facilityId") UUID facilityId);

    // Follow-up appointments
    List<Appointment> findByIsFollowUpTrue();
    
    @Query("SELECT a FROM Appointment a WHERE a.isFollowUp = true AND a.client.id = :clientId ORDER BY a.appointmentDate DESC")
    List<Appointment> findFollowUpAppointmentsByClient(@Param("clientId") UUID clientId);

    // Overdue appointments
    @Query("SELECT a FROM Appointment a WHERE a.appointmentDate < :now AND a.status = 'SCHEDULED'")
    List<Appointment> findOverdueAppointments(@Param("now") LocalDateTime now);

    // Reminder queries
    @Query("SELECT a FROM Appointment a WHERE a.reminderSent = false AND a.appointmentDate BETWEEN :start AND :end AND a.status IN ('SCHEDULED', 'CONFIRMED')")
    List<Appointment> findAppointmentsNeedingReminders(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end);

    // Conflict detection
    @Query("SELECT a FROM Appointment a WHERE " +
           "a.healthWorker.id = :healthWorkerId AND " +
           "a.status IN ('SCHEDULED', 'CONFIRMED', 'CHECKED_IN', 'IN_PROGRESS') AND " +
           "((a.appointmentDate <= :startTime AND :startTime < a.appointmentDate + INTERVAL a.durationMinutes MINUTE) OR " +
           "(a.appointmentDate < :endTime AND :endTime <= a.appointmentDate + INTERVAL a.durationMinutes MINUTE) OR " +
           "(:startTime <= a.appointmentDate AND a.appointmentDate + INTERVAL a.durationMinutes MINUTE <= :endTime))")
    List<Appointment> findConflictingAppointments(
        @Param("healthWorkerId") UUID healthWorkerId,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime
    );

    // Statistics queries
    @Query("SELECT COUNT(a) FROM Appointment a WHERE a.facility.id = :facilityId AND a.status = :status")
    long countByFacilityAndStatus(@Param("facilityId") UUID facilityId, @Param("status") AppointmentStatus status);
    
    @Query("SELECT COUNT(a) FROM Appointment a WHERE a.healthWorker.id = :healthWorkerId AND DATE(a.appointmentDate) = CURRENT_DATE")
    long countTodaysAppointmentsByHealthWorker(@Param("healthWorkerId") UUID healthWorkerId);
    
    @Query("SELECT COUNT(a) FROM Appointment a WHERE a.client.id = :clientId AND a.status = 'COMPLETED'")
    long countCompletedAppointmentsByClient(@Param("clientId") UUID clientId);

    // Appointment history
    @Query("SELECT a FROM Appointment a WHERE a.client.id = :clientId AND a.status = 'COMPLETED' ORDER BY a.completedAt DESC")
    Page<Appointment> findCompletedAppointmentsByClient(@Param("clientId") UUID clientId, Pageable pageable);

    // Search queries
    @Query("SELECT a FROM Appointment a WHERE " +
           "(LOWER(a.client.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(a.client.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(a.reason) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND " +
           "a.facility.id = :facilityId")
    Page<Appointment> searchAppointmentsByFacility(
        @Param("searchTerm") String searchTerm,
        @Param("facilityId") UUID facilityId,
        Pageable pageable
    );

    // Complex filter queries
    @Query("SELECT a FROM Appointment a WHERE " +
           "(:clientId IS NULL OR a.client.id = :clientId) AND " +
           "(:healthWorkerId IS NULL OR a.healthWorker.id = :healthWorkerId) AND " +
           "(:facilityId IS NULL OR a.facility.id = :facilityId) AND " +
           "(:status IS NULL OR a.status = :status) AND " +
           "(:appointmentType IS NULL OR a.appointmentType = :appointmentType) AND " +
           "(:startDate IS NULL OR a.appointmentDate >= :startDate) AND " +
           "(:endDate IS NULL OR a.appointmentDate <= :endDate)")
    Page<Appointment> findWithFilters(
        @Param("clientId") UUID clientId,
        @Param("healthWorkerId") UUID healthWorkerId,
        @Param("facilityId") UUID facilityId,
        @Param("status") AppointmentStatus status,
        @Param("appointmentType") AppointmentType appointmentType,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate,
        Pageable pageable
    );

    // Availability queries
    @Query("SELECT DISTINCT TIME(a.appointmentDate) FROM Appointment a WHERE " +
           "a.healthWorker.id = :healthWorkerId AND " +
           "DATE(a.appointmentDate) = :date AND " +
           "a.status IN ('SCHEDULED', 'CONFIRMED', 'CHECKED_IN', 'IN_PROGRESS')")
    List<String> findBookedTimeSlots(@Param("healthWorkerId") UUID healthWorkerId, @Param("date") LocalDate date);

    // Recent appointments
    @Query("SELECT a FROM Appointment a WHERE a.createdAt >= :since ORDER BY a.createdAt DESC")
    List<Appointment> findRecentlyCreatedAppointments(@Param("since") LocalDateTime since);

    // Cancelled appointments
    @Query("SELECT a FROM Appointment a WHERE a.status = 'CANCELLED' AND a.cancelledAt >= :since")
    List<Appointment> findRecentlyCancelledAppointments(@Param("since") LocalDateTime since);

    // No-show appointments
    @Query("SELECT a FROM Appointment a WHERE a.status = 'NO_SHOW' AND DATE(a.appointmentDate) >= :since")
    List<Appointment> findNoShowAppointments(@Param("since") LocalDate since);

    // Payment-related queries
    @Query("SELECT a FROM Appointment a WHERE a.paymentStatus = :paymentStatus")
    List<Appointment> findByPaymentStatus(@Param("paymentStatus") String paymentStatus);
    
    @Query("SELECT a FROM Appointment a WHERE a.consultationFee IS NOT NULL AND a.paymentStatus = 'PENDING'")
    List<Appointment> findUnpaidAppointments();

    // Rescheduled appointments
    @Query("SELECT a FROM Appointment a WHERE a.rescheduledFrom IS NOT NULL ORDER BY a.appointmentDate DESC")
    List<Appointment> findRescheduledAppointments();

    // Next available appointment slot
    @Query("SELECT MIN(a.appointmentDate) FROM Appointment a WHERE " +
           "a.healthWorker.id = :healthWorkerId AND " +
           "a.appointmentDate > :after AND " +
           "a.status IN ('SCHEDULED', 'CONFIRMED')")
    Optional<LocalDateTime> findNextAvailableSlot(@Param("healthWorkerId") UUID healthWorkerId, @Param("after") LocalDateTime after);
}
