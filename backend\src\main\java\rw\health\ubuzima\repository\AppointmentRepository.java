package rw.health.ubuzima.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import rw.health.ubuzima.entity.Appointment;
import rw.health.ubuzima.entity.HealthFacility;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.AppointmentStatus;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AppointmentRepository extends JpaRepository<Appointment, Long> {

    List<Appointment> findByUser(User user);

    List<Appointment> findByHealthWorker(User healthWorker);

    List<Appointment> findByHealthFacility(HealthFacility healthFacility);

    List<Appointment> findByStatus(AppointmentStatus status);

    @Query("SELECT a FROM Appointment a WHERE a.user = :user AND a.scheduledDate BETWEEN :startDate AND :endDate")
    List<Appointment> findByUserAndDateRange(@Param("user") User user, 
                                           @Param("startDate") LocalDateTime startDate, 
                                           @Param("endDate") LocalDateTime endDate);

    @Query("SELECT a FROM Appointment a WHERE a.healthWorker = :healthWorker AND a.scheduledDate BETWEEN :startDate AND :endDate")
    List<Appointment> findByHealthWorkerAndDateRange(@Param("healthWorker") User healthWorker, 
                                                   @Param("startDate") LocalDateTime startDate, 
                                                   @Param("endDate") LocalDateTime endDate);

    List<Appointment> findByUserOrderByScheduledDateDesc(User user);

    @Query("SELECT a FROM Appointment a WHERE a.scheduledDate < :now AND a.status = :status AND a.reminderSent = false")
    List<Appointment> findUpcomingAppointmentsForReminder(@Param("now") LocalDateTime now, @Param("status") AppointmentStatus status);
}
