package rw.health.ubuzima.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import rw.health.ubuzima.entity.ContraceptionMethod;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.ContraceptionType;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface ContraceptionMethodRepository extends JpaRepository<ContraceptionMethod, Long> {
    
    List<ContraceptionMethod> findByUserOrderByStartDateDesc(User user);
    
    List<ContraceptionMethod> findByUserAndIsActive(User user, Boolean isActive);
    
    Optional<ContraceptionMethod> findByUserAndIsActiveTrue(User user);
    
    List<ContraceptionMethod> findByUserAndType(User user, ContraceptionType type);
    
    @Query("SELECT cm FROM ContraceptionMethod cm WHERE cm.user = :user AND cm.nextAppointment BETWEEN :startDate AND :endDate")
    List<ContraceptionMethod> findMethodsWithAppointmentsBetween(@Param("user") User user, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    @Query("SELECT cm FROM ContraceptionMethod cm WHERE cm.user = :user AND cm.startDate <= :date AND (cm.endDate IS NULL OR cm.endDate >= :date) AND cm.isActive = true")
    List<ContraceptionMethod> findActiveMethodsForDate(@Param("user") User user, @Param("date") LocalDate date);
}
