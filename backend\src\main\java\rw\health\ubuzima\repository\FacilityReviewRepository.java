package rw.health.ubuzima.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import rw.health.ubuzima.entity.FacilityReview;
import rw.health.ubuzima.entity.HealthFacility;
import rw.health.ubuzima.entity.User;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for FacilityReview entity operations
 * 
 * <AUTHOR> Development Team
 */
@Repository
public interface FacilityReviewRepository extends JpaRepository<FacilityReview, UUID> {

    // Basic queries
    List<FacilityReview> findByFacility(HealthFacility facility);
    
    Page<FacilityReview> findByFacility(HealthFacility facility, Pageable pageable);
    
    List<FacilityReview> findByUser(User user);
    
    Page<FacilityReview> findByUser(User user, Pageable pageable);
    
    Optional<FacilityReview> findByFacilityAndUser(HealthFacility facility, User user);
    
    boolean existsByFacilityAndUser(HealthFacility facility, User user);

    // Facility-based queries
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.facility.id = :facilityId ORDER BY fr.createdAt DESC")
    Page<FacilityReview> findByFacilityId(@Param("facilityId") UUID facilityId, Pageable pageable);
    
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.isVerified = true ORDER BY fr.createdAt DESC")
    List<FacilityReview> findVerifiedReviewsByFacility(@Param("facilityId") UUID facilityId);
    
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.isFlagged = false ORDER BY fr.createdAt DESC")
    Page<FacilityReview> findUnflaggedReviewsByFacility(@Param("facilityId") UUID facilityId, Pageable pageable);

    // User-based queries
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.user.id = :userId ORDER BY fr.createdAt DESC")
    Page<FacilityReview> findByUserId(@Param("userId") UUID userId, Pageable pageable);

    // Rating-based queries
    List<FacilityReview> findByRating(BigDecimal rating);
    
    List<FacilityReview> findByRatingGreaterThanEqual(BigDecimal rating);
    
    List<FacilityReview> findByRatingLessThanEqual(BigDecimal rating);
    
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.rating >= :minRating ORDER BY fr.rating DESC")
    List<FacilityReview> findByFacilityAndMinRating(@Param("facilityId") UUID facilityId, @Param("minRating") BigDecimal minRating);

    // Verification status queries
    List<FacilityReview> findByIsVerifiedTrue();
    
    List<FacilityReview> findByIsVerifiedFalse();
    
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.isVerified = true")
    List<FacilityReview> findVerifiedReviewsByFacilityId(@Param("facilityId") UUID facilityId);

    // Flag status queries
    List<FacilityReview> findByIsFlaggedTrue();
    
    List<FacilityReview> findByIsFlaggedFalse();
    
    Page<FacilityReview> findByIsFlaggedTrue(Pageable pageable);
    
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.isFlagged = true AND fr.moderatedBy IS NULL")
    List<FacilityReview> findFlaggedUnmoderatedReviews();

    // Anonymous reviews
    List<FacilityReview> findByIsAnonymousTrue();
    
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.isAnonymous = false")
    List<FacilityReview> findNonAnonymousReviewsByFacility(@Param("facilityId") UUID facilityId);

    // Helpfulness queries
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.facility.id = :facilityId ORDER BY (fr.helpfulCount - fr.notHelpfulCount) DESC")
    List<FacilityReview> findMostHelpfulReviewsByFacility(@Param("facilityId") UUID facilityId);
    
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.helpfulCount > :threshold ORDER BY fr.helpfulCount DESC")
    List<FacilityReview> findHelpfulReviews(@Param("threshold") int threshold);

    // Recent reviews
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.createdAt >= :since ORDER BY fr.createdAt DESC")
    List<FacilityReview> findRecentReviews(@Param("since") LocalDateTime since);
    
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.createdAt >= :since ORDER BY fr.createdAt DESC")
    List<FacilityReview> findRecentReviewsByFacility(@Param("facilityId") UUID facilityId, @Param("since") LocalDateTime since);

    // Statistics queries
    @Query("SELECT COUNT(fr) FROM FacilityReview fr WHERE fr.facility.id = :facilityId")
    long countReviewsByFacility(@Param("facilityId") UUID facilityId);
    
    @Query("SELECT AVG(fr.rating) FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.isFlagged = false")
    Double getAverageRatingByFacility(@Param("facilityId") UUID facilityId);
    
    @Query("SELECT COUNT(fr) FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.rating = :rating")
    long countReviewsByFacilityAndRating(@Param("facilityId") UUID facilityId, @Param("rating") BigDecimal rating);

    // Rating distribution
    @Query("SELECT fr.rating, COUNT(fr) FROM FacilityReview fr WHERE fr.facility.id = :facilityId GROUP BY fr.rating ORDER BY fr.rating DESC")
    List<Object[]> getRatingDistributionByFacility(@Param("facilityId") UUID facilityId);

    // Detailed ratings statistics
    @Query("SELECT AVG(fr.staffRating) FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.staffRating IS NOT NULL")
    Double getAverageStaffRatingByFacility(@Param("facilityId") UUID facilityId);
    
    @Query("SELECT AVG(fr.cleanlinessRating) FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.cleanlinessRating IS NOT NULL")
    Double getAverageCleanlinessRatingByFacility(@Param("facilityId") UUID facilityId);
    
    @Query("SELECT AVG(fr.waitTimeRating) FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.waitTimeRating IS NOT NULL")
    Double getAverageWaitTimeRatingByFacility(@Param("facilityId") UUID facilityId);
    
    @Query("SELECT AVG(fr.communicationRating) FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.communicationRating IS NOT NULL")
    Double getAverageCommunicationRatingByFacility(@Param("facilityId") UUID facilityId);
    
    @Query("SELECT AVG(fr.facilitiesRating) FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.facilitiesRating IS NOT NULL")
    Double getAverageFacilitiesRatingByFacility(@Param("facilityId") UUID facilityId);

    // Search queries
    @Query("SELECT fr FROM FacilityReview fr WHERE " +
           "(LOWER(fr.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(fr.comment) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND " +
           "fr.facility.id = :facilityId AND fr.isFlagged = false")
    Page<FacilityReview> searchReviewsByFacility(
        @Param("searchTerm") String searchTerm,
        @Param("facilityId") UUID facilityId,
        Pageable pageable
    );

    // Moderation queries
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.moderatedBy = :moderator ORDER BY fr.updatedAt DESC")
    List<FacilityReview> findReviewsByModerator(@Param("moderator") String moderator);
    
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.isFlagged = true AND fr.moderatedBy IS NULL ORDER BY fr.createdAt ASC")
    List<FacilityReview> findPendingModerationReviews();

    // Top reviews
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.isFlagged = false ORDER BY fr.rating DESC, fr.helpfulCount DESC")
    List<FacilityReview> findTopReviewsByFacility(@Param("facilityId") UUID facilityId, Pageable pageable);

    // Reviews with comments
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.comment IS NOT NULL AND fr.comment != '' AND fr.isFlagged = false ORDER BY fr.createdAt DESC")
    List<FacilityReview> findReviewsWithCommentsByFacility(@Param("facilityId") UUID facilityId);

    // Reviews without comments
    @Query("SELECT fr FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND (fr.comment IS NULL OR fr.comment = '') ORDER BY fr.createdAt DESC")
    List<FacilityReview> findReviewsWithoutCommentsByFacility(@Param("facilityId") UUID facilityId);

    // Complex filter queries
    @Query("SELECT fr FROM FacilityReview fr WHERE " +
           "(:facilityId IS NULL OR fr.facility.id = :facilityId) AND " +
           "(:userId IS NULL OR fr.user.id = :userId) AND " +
           "(:minRating IS NULL OR fr.rating >= :minRating) AND " +
           "(:maxRating IS NULL OR fr.rating <= :maxRating) AND " +
           "(:isVerified IS NULL OR fr.isVerified = :isVerified) AND " +
           "(:isFlagged IS NULL OR fr.isFlagged = :isFlagged) AND " +
           "(:isAnonymous IS NULL OR fr.isAnonymous = :isAnonymous)")
    Page<FacilityReview> findWithFilters(
        @Param("facilityId") UUID facilityId,
        @Param("userId") UUID userId,
        @Param("minRating") BigDecimal minRating,
        @Param("maxRating") BigDecimal maxRating,
        @Param("isVerified") Boolean isVerified,
        @Param("isFlagged") Boolean isFlagged,
        @Param("isAnonymous") Boolean isAnonymous,
        Pageable pageable
    );

    // User review history
    @Query("SELECT COUNT(fr) FROM FacilityReview fr WHERE fr.user.id = :userId")
    long countReviewsByUser(@Param("userId") UUID userId);
    
    @Query("SELECT AVG(fr.rating) FROM FacilityReview fr WHERE fr.user.id = :userId")
    Double getAverageRatingByUser(@Param("userId") UUID userId);

    // Facility comparison
    @Query("SELECT fr.facility.id, AVG(fr.rating), COUNT(fr) FROM FacilityReview fr WHERE fr.facility.district = :district GROUP BY fr.facility.id ORDER BY AVG(fr.rating) DESC")
    List<Object[]> getFacilityRankingsByDistrict(@Param("district") String district);

    // Review trends
    @Query("SELECT DATE(fr.createdAt), COUNT(fr) FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.createdAt >= :since GROUP BY DATE(fr.createdAt) ORDER BY DATE(fr.createdAt)")
    List<Object[]> getReviewTrendsByFacility(@Param("facilityId") UUID facilityId, @Param("since") LocalDateTime since);
    
    @Query("SELECT DATE(fr.createdAt), AVG(fr.rating) FROM FacilityReview fr WHERE fr.facility.id = :facilityId AND fr.createdAt >= :since GROUP BY DATE(fr.createdAt) ORDER BY DATE(fr.createdAt)")
    List<Object[]> getRatingTrendsByFacility(@Param("facilityId") UUID facilityId, @Param("since") LocalDateTime since);

    // Bulk operations
    @Query("UPDATE FacilityReview fr SET fr.isVerified = :isVerified WHERE fr.id IN :reviewIds")
    int updateVerificationStatus(@Param("reviewIds") List<UUID> reviewIds, @Param("isVerified") boolean isVerified);
    
    @Query("UPDATE FacilityReview fr SET fr.isFlagged = :isFlagged, fr.moderatedBy = :moderator WHERE fr.id IN :reviewIds")
    int updateFlagStatus(@Param("reviewIds") List<UUID> reviewIds, @Param("isFlagged") boolean isFlagged, @Param("moderator") String moderator);
}
