package rw.health.ubuzima.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import rw.health.ubuzima.entity.HealthFacility;
import rw.health.ubuzima.enums.FacilityType;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for HealthFacility entity operations
 * 
 * <AUTHOR> Development Team
 */
@Repository
public interface HealthFacilityRepository extends JpaRepository<HealthFacility, UUID> {

    // Basic finder methods
    Optional<HealthFacility> findByName(String name);
    
    Optional<HealthFacility> findByLicenseNumber(String licenseNumber);
    
    boolean existsByName(String name);
    
    boolean existsByLicenseNumber(String licenseNumber);

    // Type-based queries
    List<HealthFacility> findByFacilityType(FacilityType facilityType);
    
    Page<HealthFacility> findByFacilityType(FacilityType facilityType, Pageable pageable);

    // Location-based queries
    List<HealthFacility> findByDistrict(String district);
    
    Page<HealthFacility> findByDistrict(String district, Pageable pageable);
    
    List<HealthFacility> findByDistrictAndSector(String district, String sector);
    
    Page<HealthFacility> findByDistrictAndSector(String district, String sector, Pageable pageable);
    
    List<HealthFacility> findByDistrictAndSectorAndCell(String district, String sector, String cell);

    // Service-based queries
    List<HealthFacility> findByHasFamilyPlanningTrue();
    
    List<HealthFacility> findByHasEmergencyServicesTrue();
    
    List<HealthFacility> findByHasMaternityWardTrue();
    
    List<HealthFacility> findByHasLaboratoryTrue();
    
    List<HealthFacility> findByHasPharmacyTrue();
    
    List<HealthFacility> findByIs24HoursTrue();

    // Active facilities
    List<HealthFacility> findByIsActiveTrue();
    
    Page<HealthFacility> findByIsActiveTrue(Pageable pageable);

    // Geographic proximity queries
    @Query("SELECT f FROM HealthFacility f WHERE " +
           "f.latitude IS NOT NULL AND f.longitude IS NOT NULL AND " +
           "f.isActive = true AND " +
           "(6371 * acos(cos(radians(:latitude)) * cos(radians(f.latitude)) * " +
           "cos(radians(f.longitude) - radians(:longitude)) + " +
           "sin(radians(:latitude)) * sin(radians(f.latitude)))) <= :radiusKm " +
           "ORDER BY (6371 * acos(cos(radians(:latitude)) * cos(radians(f.latitude)) * " +
           "cos(radians(f.longitude) - radians(:longitude)) + " +
           "sin(radians(:latitude)) * sin(radians(f.latitude))))")
    List<HealthFacility> findNearbyFacilities(
        @Param("latitude") BigDecimal latitude,
        @Param("longitude") BigDecimal longitude,
        @Param("radiusKm") double radiusKm
    );

    @Query("SELECT f FROM HealthFacility f WHERE " +
           "f.latitude IS NOT NULL AND f.longitude IS NOT NULL AND " +
           "f.isActive = true AND f.facilityType = :facilityType AND " +
           "(6371 * acos(cos(radians(:latitude)) * cos(radians(f.latitude)) * " +
           "cos(radians(f.longitude) - radians(:longitude)) + " +
           "sin(radians(:latitude)) * sin(radians(f.latitude)))) <= :radiusKm " +
           "ORDER BY (6371 * acos(cos(radians(:latitude)) * cos(radians(f.latitude)) * " +
           "cos(radians(f.longitude) - radians(:longitude)) + " +
           "sin(radians(:latitude)) * sin(radians(f.latitude))))")
    List<HealthFacility> findNearbyFacilitiesByType(
        @Param("latitude") BigDecimal latitude,
        @Param("longitude") BigDecimal longitude,
        @Param("radiusKm") double radiusKm,
        @Param("facilityType") FacilityType facilityType
    );

    // Search queries
    @Query("SELECT f FROM HealthFacility f WHERE " +
           "(LOWER(f.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(f.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(f.district) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(f.sector) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND " +
           "f.isActive = true")
    Page<HealthFacility> searchFacilities(@Param("searchTerm") String searchTerm, Pageable pageable);

    @Query("SELECT f FROM HealthFacility f WHERE " +
           "(LOWER(f.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(f.description) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND " +
           "f.facilityType = :facilityType AND f.isActive = true")
    Page<HealthFacility> searchFacilitiesByType(
        @Param("searchTerm") String searchTerm,
        @Param("facilityType") FacilityType facilityType,
        Pageable pageable
    );

    // Rating-based queries
    @Query("SELECT f FROM HealthFacility f WHERE f.rating >= :minRating AND f.isActive = true ORDER BY f.rating DESC")
    List<HealthFacility> findByMinimumRating(@Param("minRating") BigDecimal minRating);
    
    @Query("SELECT f FROM HealthFacility f WHERE f.isActive = true ORDER BY f.rating DESC")
    List<HealthFacility> findTopRatedFacilities(Pageable pageable);

    // Service availability queries
    @Query("SELECT f FROM HealthFacility f WHERE " +
           "(:hasFamilyPlanning IS NULL OR f.hasFamilyPlanning = :hasFamilyPlanning) AND " +
           "(:hasEmergencyServices IS NULL OR f.hasEmergencyServices = :hasEmergencyServices) AND " +
           "(:hasMaternityWard IS NULL OR f.hasMaternityWard = :hasMaternityWard) AND " +
           "(:hasLaboratory IS NULL OR f.hasLaboratory = :hasLaboratory) AND " +
           "(:hasPharmacy IS NULL OR f.hasPharmacy = :hasPharmacy) AND " +
           "(:is24Hours IS NULL OR f.is24Hours = :is24Hours) AND " +
           "f.isActive = true")
    Page<HealthFacility> findByServices(
        @Param("hasFamilyPlanning") Boolean hasFamilyPlanning,
        @Param("hasEmergencyServices") Boolean hasEmergencyServices,
        @Param("hasMaternityWard") Boolean hasMaternityWard,
        @Param("hasLaboratory") Boolean hasLaboratory,
        @Param("hasPharmacy") Boolean hasPharmacy,
        @Param("is24Hours") Boolean is24Hours,
        Pageable pageable
    );

    // Statistics queries
    @Query("SELECT COUNT(f) FROM HealthFacility f WHERE f.facilityType = :facilityType AND f.isActive = true")
    long countByFacilityType(@Param("facilityType") FacilityType facilityType);
    
    @Query("SELECT COUNT(f) FROM HealthFacility f WHERE f.district = :district AND f.isActive = true")
    long countByDistrict(@Param("district") String district);
    
    @Query("SELECT f.district, COUNT(f) FROM HealthFacility f WHERE f.isActive = true GROUP BY f.district")
    List<Object[]> countFacilitiesByDistrict();
    
    @Query("SELECT f.facilityType, COUNT(f) FROM HealthFacility f WHERE f.isActive = true GROUP BY f.facilityType")
    List<Object[]> countFacilitiesByType();

    // Capacity queries
    @Query("SELECT SUM(f.bedCapacity) FROM HealthFacility f WHERE f.district = :district AND f.bedCapacity IS NOT NULL AND f.isActive = true")
    Long getTotalBedCapacityByDistrict(@Param("district") String district);
    
    @Query("SELECT SUM(f.staffCount) FROM HealthFacility f WHERE f.district = :district AND f.staffCount IS NOT NULL AND f.isActive = true")
    Long getTotalStaffCountByDistrict(@Param("district") String district);

    // Complex filter queries
    @Query("SELECT f FROM HealthFacility f WHERE " +
           "(:facilityType IS NULL OR f.facilityType = :facilityType) AND " +
           "(:district IS NULL OR f.district = :district) AND " +
           "(:sector IS NULL OR f.sector = :sector) AND " +
           "(:minRating IS NULL OR f.rating >= :minRating) AND " +
           "(:hasFamilyPlanning IS NULL OR f.hasFamilyPlanning = :hasFamilyPlanning) AND " +
           "(:hasEmergencyServices IS NULL OR f.hasEmergencyServices = :hasEmergencyServices) AND " +
           "f.isActive = true")
    Page<HealthFacility> findWithFilters(
        @Param("facilityType") FacilityType facilityType,
        @Param("district") String district,
        @Param("sector") String sector,
        @Param("minRating") BigDecimal minRating,
        @Param("hasFamilyPlanning") Boolean hasFamilyPlanning,
        @Param("hasEmergencyServices") Boolean hasEmergencyServices,
        Pageable pageable
    );

    // Facilities with coordinates
    @Query("SELECT f FROM HealthFacility f WHERE f.latitude IS NOT NULL AND f.longitude IS NOT NULL AND f.isActive = true")
    List<HealthFacility> findFacilitiesWithCoordinates();

    // Facilities without coordinates
    @Query("SELECT f FROM HealthFacility f WHERE (f.latitude IS NULL OR f.longitude IS NULL) AND f.isActive = true")
    List<HealthFacility> findFacilitiesWithoutCoordinates();

    // Emergency facilities
    @Query("SELECT f FROM HealthFacility f WHERE " +
           "(f.hasEmergencyServices = true OR f.is24Hours = true) AND " +
           "f.isActive = true")
    List<HealthFacility> findEmergencyFacilities();

    // Family planning facilities
    @Query("SELECT f FROM HealthFacility f WHERE f.hasFamilyPlanning = true AND f.isActive = true")
    List<HealthFacility> findFamilyPlanningFacilities();
    
    @Query("SELECT f FROM HealthFacility f WHERE " +
           "f.hasFamilyPlanning = true AND f.district = :district AND f.isActive = true")
    List<HealthFacility> findFamilyPlanningFacilitiesByDistrict(@Param("district") String district);

    // Recently added facilities
    @Query("SELECT f FROM HealthFacility f WHERE f.createdAt >= :since AND f.isActive = true ORDER BY f.createdAt DESC")
    List<HealthFacility> findRecentlyAddedFacilities(@Param("since") java.time.LocalDateTime since);

    // Facilities needing updates
    @Query("SELECT f FROM HealthFacility f WHERE " +
           "(f.latitude IS NULL OR f.longitude IS NULL OR " +
           "f.phoneNumber IS NULL OR f.operatingHours IS NULL) AND " +
           "f.isActive = true")
    List<HealthFacility> findFacilitiesNeedingUpdates();
}
