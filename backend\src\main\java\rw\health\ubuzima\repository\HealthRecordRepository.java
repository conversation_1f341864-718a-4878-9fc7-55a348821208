package rw.health.ubuzima.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import rw.health.ubuzima.entity.HealthRecord;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.RecordType;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface HealthRecordRepository extends JpaRepository<HealthRecord, Long> {

    List<HealthRecord> findByUser(User user);

    List<HealthRecord> findByUserAndRecordType(User user, RecordType recordType);

    @Query("SELECT hr FROM HealthRecord hr WHERE hr.user = :user AND hr.recordedAt BETWEEN :startDate AND :endDate")
    List<HealthRecord> findByUserAndDateRange(@Param("user") User user, 
                                            @Param("startDate") LocalDateTime startDate, 
                                            @Param("endDate") LocalDateTime endDate);

    @Query("SELECT hr FROM HealthRecord hr WHERE hr.user = :user AND hr.recordType = :recordType AND hr.recordedAt BETWEEN :startDate AND :endDate")
    List<HealthRecord> findByUserAndRecordTypeAndDateRange(@Param("user") User user, 
                                                         @Param("recordType") RecordType recordType,
                                                         @Param("startDate") LocalDateTime startDate, 
                                                         @Param("endDate") LocalDateTime endDate);

    List<HealthRecord> findByUserOrderByRecordedAtDesc(User user);
}
