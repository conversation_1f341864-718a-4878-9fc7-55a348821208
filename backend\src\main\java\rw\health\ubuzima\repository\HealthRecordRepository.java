package rw.health.ubuzima.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import rw.health.ubuzima.entity.HealthRecord;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.RecordType;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * Repository interface for HealthRecord entity operations
 * 
 * <AUTHOR> Development Team
 */
@Repository
public interface HealthRecordRepository extends JpaRepository<HealthRecord, UUID> {

    // Client-based queries
    List<HealthRecord> findByClient(User client);
    
    Page<HealthRecord> findByClient(User client, Pageable pageable);
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId ORDER BY hr.recordDate DESC")
    Page<HealthRecord> findByClientId(@Param("clientId") UUID clientId, Pageable pageable);
    
    List<HealthRecord> findByClientAndRecordType(User client, RecordType recordType);
    
    Page<HealthRecord> findByClientAndRecordType(User client, RecordType recordType, Pageable pageable);

    // Health worker-based queries
    List<HealthRecord> findByHealthWorker(User healthWorker);
    
    Page<HealthRecord> findByHealthWorker(User healthWorker, Pageable pageable);
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.healthWorker.id = :healthWorkerId ORDER BY hr.recordDate DESC")
    Page<HealthRecord> findByHealthWorkerId(@Param("healthWorkerId") UUID healthWorkerId, Pageable pageable);

    // Record type-based queries
    List<HealthRecord> findByRecordType(RecordType recordType);
    
    Page<HealthRecord> findByRecordType(RecordType recordType, Pageable pageable);
    
    List<HealthRecord> findByRecordTypeIn(List<RecordType> recordTypes);

    // Date-based queries
    List<HealthRecord> findByRecordDateBetween(LocalDate startDate, LocalDate endDate);
    
    Page<HealthRecord> findByRecordDateBetween(LocalDate startDate, LocalDate endDate, Pageable pageable);
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.recordDate BETWEEN :startDate AND :endDate ORDER BY hr.recordDate DESC")
    List<HealthRecord> findByClientAndDateRange(
        @Param("clientId") UUID clientId,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate
    );

    // Recent records
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.recordDate >= :since ORDER BY hr.recordDate DESC")
    List<HealthRecord> findRecentRecords(@Param("since") LocalDate since);
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.recordDate >= :since ORDER BY hr.recordDate DESC")
    List<HealthRecord> findRecentRecordsByClient(@Param("clientId") UUID clientId, @Param("since") LocalDate since);

    // Family planning specific queries
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.recordType IN ('FAMILY_PLANNING', 'CONTRACEPTION', 'PRENATAL_CARE', 'POSTNATAL_CARE') ORDER BY hr.recordDate DESC")
    List<HealthRecord> findFamilyPlanningRecordsByClient(@Param("clientId") UUID clientId);
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.contraceptiveMethod IS NOT NULL ORDER BY hr.recordDate DESC")
    List<HealthRecord> findContraceptionRecordsByClient(@Param("clientId") UUID clientId);
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.isPregnant = true ORDER BY hr.recordDate DESC")
    List<HealthRecord> findPregnancyRecordsByClient(@Param("clientId") UUID clientId);

    // Vital signs queries
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.recordType = 'VITAL_SIGNS' ORDER BY hr.recordDate DESC")
    List<HealthRecord> findVitalSignsByClient(@Param("clientId") UUID clientId);
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND (hr.weightKg IS NOT NULL OR hr.heightCm IS NOT NULL OR hr.bloodPressureSystolic IS NOT NULL) ORDER BY hr.recordDate DESC")
    List<HealthRecord> findRecordsWithVitalSignsByClient(@Param("clientId") UUID clientId);

    // Laboratory results queries
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.recordType IN ('LABORATORY_RESULTS', 'HIV_TESTING', 'STI_SCREENING') ORDER BY hr.recordDate DESC")
    List<HealthRecord> findLabResultsByClient(@Param("clientId") UUID clientId);
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.hivStatus IS NOT NULL ORDER BY hr.recordDate DESC")
    List<HealthRecord> findHivTestResultsByClient(@Param("clientId") UUID clientId);
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.stiScreeningResults IS NOT NULL ORDER BY hr.recordDate DESC")
    List<HealthRecord> findStiTestResultsByClient(@Param("clientId") UUID clientId);

    // Confidential records
    List<HealthRecord> findByIsConfidentialTrue();
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.isConfidential = true ORDER BY hr.recordDate DESC")
    List<HealthRecord> findConfidentialRecordsByClient(@Param("clientId") UUID clientId);

    // Emergency records
    List<HealthRecord> findByIsEmergencyTrue();
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.isEmergency = true ORDER BY hr.recordDate DESC")
    List<HealthRecord> findEmergencyRecordsByClient(@Param("clientId") UUID clientId);

    // Follow-up queries
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.followUpDate IS NOT NULL AND hr.followUpDate >= CURRENT_DATE ORDER BY hr.followUpDate ASC")
    List<HealthRecord> findRecordsRequiringFollowUp();
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.followUpDate IS NOT NULL AND hr.followUpDate >= CURRENT_DATE ORDER BY hr.followUpDate ASC")
    List<HealthRecord> findFollowUpRecordsByClient(@Param("clientId") UUID clientId);
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.followUpDate = :date")
    List<HealthRecord> findRecordsWithFollowUpDate(@Param("date") LocalDate date);

    // Search queries
    @Query("SELECT hr FROM HealthRecord hr WHERE " +
           "(LOWER(hr.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(hr.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(hr.diagnosis) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(hr.symptoms) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND " +
           "hr.client.id = :clientId")
    Page<HealthRecord> searchRecordsByClient(
        @Param("searchTerm") String searchTerm,
        @Param("clientId") UUID clientId,
        Pageable pageable
    );

    // Statistics queries
    @Query("SELECT COUNT(hr) FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.recordType = :recordType")
    long countByClientAndRecordType(@Param("clientId") UUID clientId, @Param("recordType") RecordType recordType);
    
    @Query("SELECT COUNT(hr) FROM HealthRecord hr WHERE hr.healthWorker.id = :healthWorkerId AND hr.recordDate BETWEEN :startDate AND :endDate")
    long countByHealthWorkerAndDateRange(
        @Param("healthWorkerId") UUID healthWorkerId,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate
    );
    
    @Query("SELECT hr.recordType, COUNT(hr) FROM HealthRecord hr WHERE hr.client.id = :clientId GROUP BY hr.recordType")
    List<Object[]> countRecordTypesByClient(@Param("clientId") UUID clientId);

    // Latest record queries
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.recordType = :recordType ORDER BY hr.recordDate DESC LIMIT 1")
    HealthRecord findLatestRecordByClientAndType(@Param("clientId") UUID clientId, @Param("recordType") RecordType recordType);
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId ORDER BY hr.recordDate DESC LIMIT 1")
    HealthRecord findLatestRecordByClient(@Param("clientId") UUID clientId);

    // Medication queries
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.medications IS NOT NULL ORDER BY hr.recordDate DESC")
    List<HealthRecord> findMedicationRecordsByClient(@Param("clientId") UUID clientId);
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.recordType = 'MEDICATION' ORDER BY hr.recordDate DESC")
    List<HealthRecord> findMedicationHistoryByClient(@Param("clientId") UUID clientId);

    // Appointment-related records
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.appointment.id = :appointmentId")
    List<HealthRecord> findByAppointmentId(@Param("appointmentId") UUID appointmentId);

    // Facility-based queries
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.facility.id = :facilityId ORDER BY hr.recordDate DESC")
    Page<HealthRecord> findByFacilityId(@Param("facilityId") UUID facilityId, Pageable pageable);
    
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.facility.id = :facilityId AND hr.recordDate BETWEEN :startDate AND :endDate")
    List<HealthRecord> findByFacilityAndDateRange(
        @Param("facilityId") UUID facilityId,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate
    );

    // Complex filter queries
    @Query("SELECT hr FROM HealthRecord hr WHERE " +
           "(:clientId IS NULL OR hr.client.id = :clientId) AND " +
           "(:healthWorkerId IS NULL OR hr.healthWorker.id = :healthWorkerId) AND " +
           "(:facilityId IS NULL OR hr.facility.id = :facilityId) AND " +
           "(:recordType IS NULL OR hr.recordType = :recordType) AND " +
           "(:startDate IS NULL OR hr.recordDate >= :startDate) AND " +
           "(:endDate IS NULL OR hr.recordDate <= :endDate) AND " +
           "(:isConfidential IS NULL OR hr.isConfidential = :isConfidential)")
    Page<HealthRecord> findWithFilters(
        @Param("clientId") UUID clientId,
        @Param("healthWorkerId") UUID healthWorkerId,
        @Param("facilityId") UUID facilityId,
        @Param("recordType") RecordType recordType,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate,
        @Param("isConfidential") Boolean isConfidential,
        Pageable pageable
    );

    // BMI tracking
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.bmi IS NOT NULL ORDER BY hr.recordDate DESC")
    List<HealthRecord> findBmiRecordsByClient(@Param("clientId") UUID clientId);

    // Blood pressure tracking
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.bloodPressureSystolic IS NOT NULL AND hr.bloodPressureDiastolic IS NOT NULL ORDER BY hr.recordDate DESC")
    List<HealthRecord> findBloodPressureRecordsByClient(@Param("clientId") UUID clientId);

    // Pregnancy tracking
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.client.id = :clientId AND hr.pregnancyWeeks IS NOT NULL ORDER BY hr.recordDate DESC")
    List<HealthRecord> findPregnancyProgressByClient(@Param("clientId") UUID clientId);

    // Records needing next appointment
    @Query("SELECT hr FROM HealthRecord hr WHERE hr.nextAppointmentRecommended IS NOT NULL AND hr.nextAppointmentRecommended <= CURRENT_DATE")
    List<HealthRecord> findRecordsNeedingNextAppointment();
}
