package rw.health.ubuzima.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import rw.health.ubuzima.entity.HealthFacility;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.UserRole;
import rw.health.ubuzima.enums.UserStatus;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for User entity operations
 * 
 * <AUTHOR> Development Team
 */
@Repository
public interface UserRepository extends JpaRepository<User, UUID> {

    // Basic finder methods
    Optional<User> findByEmail(String email);
    
    Optional<User> findByPhoneNumber(String phoneNumber);
    
    Optional<User> findByNationalId(String nationalId);
    
    Optional<User> findByEmailOrPhoneNumber(String email, String phoneNumber);
    
    boolean existsByEmail(String email);
    
    boolean existsByPhoneNumber(String phoneNumber);
    
    boolean existsByNationalId(String nationalId);

    // Role-based queries
    List<User> findByRole(UserRole role);
    
    Page<User> findByRole(UserRole role, Pageable pageable);
    
    List<User> findByRoleAndStatus(UserRole role, UserStatus status);
    
    Page<User> findByRoleAndStatus(UserRole role, UserStatus status, Pageable pageable);

    // Status-based queries
    List<User> findByStatus(UserStatus status);
    
    Page<User> findByStatus(UserStatus status, Pageable pageable);
    
    List<User> findByIsActiveTrue();
    
    Page<User> findByIsActiveTrue(Pageable pageable);

    // Facility-based queries
    List<User> findByFacility(HealthFacility facility);
    
    Page<User> findByFacility(HealthFacility facility, Pageable pageable);
    
    List<User> findByFacilityAndRole(HealthFacility facility, UserRole role);
    
    Page<User> findByFacilityAndRole(HealthFacility facility, UserRole role, Pageable pageable);

    // Location-based queries
    List<User> findByDistrict(String district);
    
    List<User> findByDistrictAndSector(String district, String sector);
    
    Page<User> findByDistrict(String district, Pageable pageable);

    // Authentication and security queries
    @Query("SELECT u FROM User u WHERE u.email = :email AND u.status = 'ACTIVE' AND u.isActive = true")
    Optional<User> findActiveUserByEmail(@Param("email") String email);
    
    @Query("SELECT u FROM User u WHERE u.phoneNumber = :phoneNumber AND u.status = 'ACTIVE' AND u.isActive = true")
    Optional<User> findActiveUserByPhoneNumber(@Param("phoneNumber") String phoneNumber);
    
    @Query("SELECT u FROM User u WHERE (u.email = :identifier OR u.phoneNumber = :identifier) AND u.status = 'ACTIVE' AND u.isActive = true")
    Optional<User> findActiveUserByEmailOrPhoneNumber(@Param("identifier") String identifier);

    // Password reset queries
    Optional<User> findByPasswordResetToken(String token);
    
    @Query("SELECT u FROM User u WHERE u.passwordResetToken = :token AND u.passwordResetExpiresAt > :now")
    Optional<User> findByValidPasswordResetToken(@Param("token") String token, @Param("now") LocalDateTime now);

    // Email verification queries
    Optional<User> findByEmailVerificationToken(String token);
    
    Optional<User> findByPhoneVerificationToken(String token);

    // Account security queries
    @Query("SELECT u FROM User u WHERE u.lockedUntil IS NOT NULL AND u.lockedUntil > :now")
    List<User> findLockedUsers(@Param("now") LocalDateTime now);
    
    @Query("SELECT u FROM User u WHERE u.loginAttempts >= :maxAttempts")
    List<User> findUsersWithExcessiveLoginAttempts(@Param("maxAttempts") int maxAttempts);

    // Health worker specific queries
    @Query("SELECT u FROM User u WHERE u.role = 'HEALTH_WORKER' AND u.facility.id = :facilityId AND u.status = 'ACTIVE'")
    List<User> findActiveHealthWorkersByFacility(@Param("facilityId") UUID facilityId);
    
    @Query("SELECT u FROM User u WHERE u.role = 'HEALTH_WORKER' AND u.facility.district = :district AND u.status = 'ACTIVE'")
    List<User> findActiveHealthWorkersByDistrict(@Param("district") String district);

    // Client specific queries
    @Query("SELECT u FROM User u WHERE u.role = 'CLIENT' AND u.facility.id = :facilityId")
    Page<User> findClientsByFacility(@Param("facilityId") UUID facilityId, Pageable pageable);
    
    @Query("SELECT u FROM User u WHERE u.role = 'CLIENT' AND u.district = :district")
    Page<User> findClientsByDistrict(@Param("district") String district, Pageable pageable);

    // Admin queries
    @Query("SELECT u FROM User u WHERE u.role IN ('ADMIN', 'SUPER_ADMIN') AND u.status = 'ACTIVE'")
    List<User> findActiveAdmins();

    // Search queries
    @Query("SELECT u FROM User u WHERE " +
           "(LOWER(u.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "u.phoneNumber LIKE CONCAT('%', :searchTerm, '%')) AND " +
           "u.isActive = true")
    Page<User> searchUsers(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    @Query("SELECT u FROM User u WHERE " +
           "(LOWER(u.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "u.phoneNumber LIKE CONCAT('%', :searchTerm, '%')) AND " +
           "u.role = :role AND u.isActive = true")
    Page<User> searchUsersByRole(@Param("searchTerm") String searchTerm, @Param("role") UserRole role, Pageable pageable);

    // Statistics queries
    @Query("SELECT COUNT(u) FROM User u WHERE u.role = :role AND u.status = 'ACTIVE'")
    long countActiveUsersByRole(@Param("role") UserRole role);
    
    @Query("SELECT COUNT(u) FROM User u WHERE u.facility.id = :facilityId AND u.status = 'ACTIVE'")
    long countActiveUsersByFacility(@Param("facilityId") UUID facilityId);
    
    @Query("SELECT COUNT(u) FROM User u WHERE u.district = :district AND u.status = 'ACTIVE'")
    long countActiveUsersByDistrict(@Param("district") String district);
    
    @Query("SELECT COUNT(u) FROM User u WHERE u.createdAt >= :startDate AND u.createdAt <= :endDate")
    long countUsersCreatedBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    // Recent activity queries
    @Query("SELECT u FROM User u WHERE u.lastLoginAt >= :since ORDER BY u.lastLoginAt DESC")
    List<User> findUsersWithRecentActivity(@Param("since") LocalDateTime since);
    
    @Query("SELECT u FROM User u WHERE u.lastLoginAt IS NULL OR u.lastLoginAt < :since")
    List<User> findInactiveUsers(@Param("since") LocalDateTime since);

    // Verification status queries
    @Query("SELECT u FROM User u WHERE u.emailVerified = false AND u.createdAt < :cutoffDate")
    List<User> findUnverifiedEmailUsers(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    @Query("SELECT u FROM User u WHERE u.phoneVerified = false AND u.createdAt < :cutoffDate")
    List<User> findUnverifiedPhoneUsers(@Param("cutoffDate") LocalDateTime cutoffDate);

    // Bulk operations
    @Query("UPDATE User u SET u.status = :status WHERE u.id IN :userIds")
    int updateUserStatus(@Param("userIds") List<UUID> userIds, @Param("status") UserStatus status);
    
    @Query("UPDATE User u SET u.isActive = :isActive WHERE u.id IN :userIds")
    int updateUserActiveStatus(@Param("userIds") List<UUID> userIds, @Param("isActive") boolean isActive);

    // Custom complex queries
    @Query("SELECT u FROM User u WHERE " +
           "(:role IS NULL OR u.role = :role) AND " +
           "(:status IS NULL OR u.status = :status) AND " +
           "(:facilityId IS NULL OR u.facility.id = :facilityId) AND " +
           "(:district IS NULL OR u.district = :district) AND " +
           "u.isActive = true")
    Page<User> findUsersWithFilters(
        @Param("role") UserRole role,
        @Param("status") UserStatus status,
        @Param("facilityId") UUID facilityId,
        @Param("district") String district,
        Pageable pageable
    );
}
