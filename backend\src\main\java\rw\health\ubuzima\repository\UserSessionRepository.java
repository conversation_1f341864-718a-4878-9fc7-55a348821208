package rw.health.ubuzima.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.entity.UserSession;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for UserSession entity operations
 * 
 * <AUTHOR> Development Team
 */
@Repository
public interface UserSessionRepository extends JpaRepository<UserSession, UUID> {

    // Session token queries
    Optional<UserSession> findBySessionToken(String sessionToken);
    
    Optional<UserSession> findByRefreshToken(String refreshToken);
    
    @Query("SELECT us FROM UserSession us WHERE us.sessionToken = :sessionToken AND us.isActive = true AND us.expiresAt > :now")
    Optional<UserSession> findValidSessionByToken(@Param("sessionToken") String sessionToken, @Param("now") LocalDateTime now);
    
    @Query("SELECT us FROM UserSession us WHERE us.refreshToken = :refreshToken AND us.isActive = true AND us.refreshExpiresAt > :now")
    Optional<UserSession> findValidSessionByRefreshToken(@Param("refreshToken") String refreshToken, @Param("now") LocalDateTime now);

    // User-based queries
    List<UserSession> findByUser(User user);
    
    List<UserSession> findByUserAndIsActiveTrue(User user);
    
    @Query("SELECT us FROM UserSession us WHERE us.user.id = :userId AND us.isActive = true")
    List<UserSession> findActiveSessionsByUserId(@Param("userId") UUID userId);
    
    @Query("SELECT us FROM UserSession us WHERE us.user.id = :userId ORDER BY us.lastActivityAt DESC")
    List<UserSession> findSessionsByUserIdOrderByActivity(@Param("userId") UUID userId);

    // Active sessions
    @Query("SELECT us FROM UserSession us WHERE us.isActive = true AND us.expiresAt > :now")
    List<UserSession> findActiveSessions(@Param("now") LocalDateTime now);
    
    @Query("SELECT COUNT(us) FROM UserSession us WHERE us.isActive = true AND us.expiresAt > :now")
    long countActiveSessions(@Param("now") LocalDateTime now);

    // Expired sessions
    @Query("SELECT us FROM UserSession us WHERE us.isActive = true AND us.expiresAt <= :now")
    List<UserSession> findExpiredSessions(@Param("now") LocalDateTime now);
    
    @Query("SELECT us FROM UserSession us WHERE us.isActive = true AND us.refreshExpiresAt <= :now")
    List<UserSession> findExpiredRefreshSessions(@Param("now") LocalDateTime now);

    // Device-based queries
    List<UserSession> findByDeviceId(String deviceId);
    
    List<UserSession> findByDeviceType(String deviceType);
    
    @Query("SELECT us FROM UserSession us WHERE us.user.id = :userId AND us.deviceId = :deviceId AND us.isActive = true")
    List<UserSession> findActiveSessionsByUserAndDevice(@Param("userId") UUID userId, @Param("deviceId") String deviceId);

    // IP address queries
    List<UserSession> findByIpAddress(String ipAddress);
    
    @Query("SELECT us FROM UserSession us WHERE us.user.id = :userId AND us.ipAddress = :ipAddress AND us.isActive = true")
    List<UserSession> findActiveSessionsByUserAndIp(@Param("userId") UUID userId, @Param("ipAddress") String ipAddress);

    // Location-based queries
    List<UserSession> findByLocation(String location);

    // Activity-based queries
    @Query("SELECT us FROM UserSession us WHERE us.lastActivityAt >= :since ORDER BY us.lastActivityAt DESC")
    List<UserSession> findSessionsWithRecentActivity(@Param("since") LocalDateTime since);
    
    @Query("SELECT us FROM UserSession us WHERE us.user.id = :userId AND us.lastActivityAt >= :since ORDER BY us.lastActivityAt DESC")
    List<UserSession> findUserSessionsWithRecentActivity(@Param("userId") UUID userId, @Param("since") LocalDateTime since);

    // Cleanup operations
    @Modifying
    @Query("UPDATE UserSession us SET us.isActive = false WHERE us.expiresAt <= :now")
    int deactivateExpiredSessions(@Param("now") LocalDateTime now);
    
    @Modifying
    @Query("DELETE FROM UserSession us WHERE us.isActive = false AND us.logoutAt < :cutoffDate")
    int deleteOldInactiveSessions(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    @Modifying
    @Query("UPDATE UserSession us SET us.isActive = false WHERE us.user.id = :userId AND us.id != :currentSessionId")
    int deactivateOtherUserSessions(@Param("userId") UUID userId, @Param("currentSessionId") UUID currentSessionId);

    // Session management
    @Modifying
    @Query("UPDATE UserSession us SET us.isActive = false, us.logoutAt = :logoutTime, us.logoutReason = :reason WHERE us.user.id = :userId")
    int logoutAllUserSessions(@Param("userId") UUID userId, @Param("logoutTime") LocalDateTime logoutTime, @Param("reason") String reason);
    
    @Modifying
    @Query("UPDATE UserSession us SET us.lastActivityAt = :activityTime WHERE us.sessionToken = :sessionToken")
    int updateSessionActivity(@Param("sessionToken") String sessionToken, @Param("activityTime") LocalDateTime activityTime);

    // Security queries
    @Query("SELECT us FROM UserSession us WHERE us.user.id = :userId AND us.ipAddress != :currentIp AND us.isActive = true")
    List<UserSession> findSuspiciousSessionsByUser(@Param("userId") UUID userId, @Param("currentIp") String currentIp);
    
    @Query("SELECT COUNT(DISTINCT us.ipAddress) FROM UserSession us WHERE us.user.id = :userId AND us.createdAt >= :since")
    long countDistinctIpAddressesByUser(@Param("userId") UUID userId, @Param("since") LocalDateTime since);
    
    @Query("SELECT COUNT(DISTINCT us.deviceId) FROM UserSession us WHERE us.user.id = :userId AND us.createdAt >= :since")
    long countDistinctDevicesByUser(@Param("userId") UUID userId, @Param("since") LocalDateTime since);

    // Statistics queries
    @Query("SELECT COUNT(us) FROM UserSession us WHERE us.user.id = :userId")
    long countTotalSessionsByUser(@Param("userId") UUID userId);
    
    @Query("SELECT COUNT(us) FROM UserSession us WHERE us.createdAt >= :since")
    long countSessionsCreatedSince(@Param("since") LocalDateTime since);
    
    @Query("SELECT us.deviceType, COUNT(us) FROM UserSession us WHERE us.createdAt >= :since GROUP BY us.deviceType")
    List<Object[]> countSessionsByDeviceType(@Param("since") LocalDateTime since);

    // Recent sessions
    @Query("SELECT us FROM UserSession us WHERE us.user.id = :userId ORDER BY us.createdAt DESC")
    List<UserSession> findRecentSessionsByUser(@Param("userId") UUID userId);
    
    @Query("SELECT us FROM UserSession us ORDER BY us.createdAt DESC")
    List<UserSession> findRecentSessions();

    // Concurrent sessions
    @Query("SELECT COUNT(us) FROM UserSession us WHERE us.user.id = :userId AND us.isActive = true AND us.expiresAt > :now")
    long countConcurrentSessionsByUser(@Param("userId") UUID userId, @Param("now") LocalDateTime now);
    
    @Query("SELECT us.user.id, COUNT(us) FROM UserSession us WHERE us.isActive = true AND us.expiresAt > :now GROUP BY us.user.id HAVING COUNT(us) > :maxSessions")
    List<Object[]> findUsersWithExcessiveSessions(@Param("now") LocalDateTime now, @Param("maxSessions") long maxSessions);

    // Session duration analysis
    @Query("SELECT AVG(TIMESTAMPDIFF(MINUTE, us.createdAt, us.logoutAt)) FROM UserSession us WHERE us.logoutAt IS NOT NULL AND us.createdAt >= :since")
    Double getAverageSessionDuration(@Param("since") LocalDateTime since);
    
    @Query("SELECT us FROM UserSession us WHERE us.isActive = true AND us.createdAt < :longSessionThreshold")
    List<UserSession> findLongRunningSessions(@Param("longSessionThreshold") LocalDateTime longSessionThreshold);

    // User agent analysis
    @Query("SELECT DISTINCT us.userAgent FROM UserSession us WHERE us.user.id = :userId")
    List<String> findDistinctUserAgentsByUser(@Param("userId") UUID userId);
    
    @Query("SELECT us.userAgent, COUNT(us) FROM UserSession us WHERE us.createdAt >= :since GROUP BY us.userAgent ORDER BY COUNT(us) DESC")
    List<Object[]> countSessionsByUserAgent(@Param("since") LocalDateTime since);

    // Logout analysis
    @Query("SELECT us FROM UserSession us WHERE us.logoutAt IS NOT NULL AND us.logoutAt >= :since ORDER BY us.logoutAt DESC")
    List<UserSession> findRecentLogouts(@Param("since") LocalDateTime since);
    
    @Query("SELECT us.logoutReason, COUNT(us) FROM UserSession us WHERE us.logoutAt >= :since AND us.logoutReason IS NOT NULL GROUP BY us.logoutReason")
    List<Object[]> countLogoutReasons(@Param("since") LocalDateTime since);

    // Session validation
    @Query("SELECT us FROM UserSession us WHERE us.sessionToken = :sessionToken AND us.isActive = true")
    Optional<UserSession> findActiveSessionByToken(@Param("sessionToken") String sessionToken);
    
    boolean existsBySessionTokenAndIsActiveTrueAndExpiresAtAfter(String sessionToken, LocalDateTime now);
    
    boolean existsByRefreshTokenAndIsActiveTrueAndRefreshExpiresAtAfter(String refreshToken, LocalDateTime now);
}
