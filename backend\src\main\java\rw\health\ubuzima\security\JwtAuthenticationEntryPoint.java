package rw.health.ubuzima.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;
import rw.health.ubuzima.dto.response.ApiResponseDto;

import java.io.IOException;

/**
 * JWT Authentication Entry Point for handling unauthorized access
 * 
 * <AUTHOR> Development Team
 */
@Slf4j
@Component
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                        AuthenticationException authException) throws IOException {
        
        log.error("Unauthorized access attempt: {}", authException.getMessage());
        
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        
        ApiResponseDto<Object> errorResponse = ApiResponseDto.unauthorized(
            "Access denied. Please provide valid authentication credentials."
        ).withPath(request.getRequestURI());
        
        objectMapper.writeValue(response.getOutputStream(), errorResponse);
    }
}
