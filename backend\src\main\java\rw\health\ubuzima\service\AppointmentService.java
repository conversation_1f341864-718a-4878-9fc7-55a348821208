package rw.health.ubuzima.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import rw.health.ubuzima.entity.Appointment;
import rw.health.ubuzima.enums.AppointmentStatus;
import rw.health.ubuzima.enums.AppointmentType;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service interface for Appointment operations
 * 
 * <AUTHOR> Development Team
 */
public interface AppointmentService {

    // CRUD operations
    Appointment createAppointment(UUID clientId, UUID facilityId, UUID healthWorkerId, 
                                LocalDateTime appointmentDate, AppointmentType type, String reason);
    
    Appointment updateAppointment(UUID appointmentId, Appointment appointmentUpdates);
    
    Appointment getAppointmentById(UUID appointmentId);
    
    Optional<Appointment> findAppointmentById(UUID appointmentId);
    
    Page<Appointment> getAllAppointments(Pageable pageable);
    
    void deleteAppointment(UUID appointmentId);
    
    void cancelAppointment(UUID appointmentId, String reason, String cancelledBy);

    // Client operations
    Page<Appointment> getAppointmentsByClient(UUID clientId, Pageable pageable);
    
    List<Appointment> getUpcomingAppointmentsByClient(UUID clientId);
    
    List<Appointment> getAppointmentHistoryByClient(UUID clientId);

    // Health worker operations
    Page<Appointment> getAppointmentsByHealthWorker(UUID healthWorkerId, Pageable pageable);
    
    List<Appointment> getTodaysAppointmentsByHealthWorker(UUID healthWorkerId);
    
    List<Appointment> getUpcomingAppointmentsByHealthWorker(UUID healthWorkerId);

    // Facility operations
    Page<Appointment> getAppointmentsByFacility(UUID facilityId, Pageable pageable);
    
    List<Appointment> getTodaysAppointmentsByFacility(UUID facilityId);

    // Status management
    Appointment updateAppointmentStatus(UUID appointmentId, AppointmentStatus status);
    
    Appointment confirmAppointment(UUID appointmentId);
    
    Appointment checkInAppointment(UUID appointmentId);
    
    Appointment startAppointment(UUID appointmentId);
    
    Appointment completeAppointment(UUID appointmentId);
    
    Appointment markNoShow(UUID appointmentId);

    // Scheduling operations
    Appointment rescheduleAppointment(UUID appointmentId, LocalDateTime newDate, String reason);
    
    List<String> getAvailableTimeSlots(UUID healthWorkerId, LocalDate date);
    
    boolean isTimeSlotAvailable(UUID healthWorkerId, LocalDateTime startTime, LocalDateTime endTime);
    
    List<Appointment> getConflictingAppointments(UUID healthWorkerId, LocalDateTime startTime, LocalDateTime endTime);

    // Search and filtering
    Page<Appointment> searchAppointments(String searchTerm, Pageable pageable);
    
    Page<Appointment> getAppointmentsByStatus(AppointmentStatus status, Pageable pageable);
    
    Page<Appointment> getAppointmentsByType(AppointmentType type, Pageable pageable);
    
    Page<Appointment> getAppointmentsByDateRange(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    // Statistics
    long getTotalAppointmentCount();
    
    long getAppointmentCountByStatus(AppointmentStatus status);
    
    long getAppointmentCountByFacility(UUID facilityId);
    
    long getAppointmentCountByHealthWorker(UUID healthWorkerId);

    // Reminders and notifications
    void sendAppointmentReminder(UUID appointmentId);
    
    List<Appointment> getAppointmentsNeedingReminders();
    
    void sendAppointmentConfirmation(UUID appointmentId);

    // Emergency and priority
    List<Appointment> getEmergencyAppointments();
    
    Appointment createEmergencyAppointment(UUID clientId, UUID facilityId, String reason);

    // Follow-up management
    List<Appointment> getFollowUpAppointments();
    
    Appointment createFollowUpAppointment(UUID originalAppointmentId, LocalDateTime followUpDate);

    // Validation
    boolean canClientBookAppointment(UUID clientId, LocalDateTime appointmentDate);
    
    boolean canAppointmentBeModified(UUID appointmentId);
    
    boolean canAppointmentBeCancelled(UUID appointmentId);

    // Reporting
    List<Appointment> getOverdueAppointments();
    
    List<Appointment> getRecentlyCancelledAppointments();
    
    List<Appointment> getNoShowAppointments();

    // Advanced filtering
    Page<Appointment> getAppointmentsWithFilters(
        UUID clientId,
        UUID healthWorkerId,
        UUID facilityId,
        AppointmentStatus status,
        AppointmentType type,
        LocalDateTime startDate,
        LocalDateTime endDate,
        Pageable pageable
    );
}
