package rw.health.ubuzima.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;
import rw.health.ubuzima.entity.AudioContent;
import rw.health.ubuzima.enums.ContentCategory;
import rw.health.ubuzima.enums.ContentStatus;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service interface for AudioContent operations
 * 
 * <AUTHOR> Development Team
 */
public interface AudioContentService {

    // CRUD operations
    AudioContent createAudioContent(String title, ContentCategory category, String language, 
                                  MultipartFile audioFile, UUID authorId);
    
    AudioContent updateAudioContent(UUID contentId, AudioContent contentUpdates);
    
    AudioContent getAudioContentById(UUID contentId);
    
    Optional<AudioContent> findAudioContentById(UUID contentId);
    
    Page<AudioContent> getAllAudioContent(Pageable pageable);
    
    void deleteAudioContent(UUID contentId);

    // Content management
    AudioContent publishContent(UUID contentId);
    
    AudioContent unpublishContent(UUID contentId);
    
    AudioContent archiveContent(UUID contentId);
    
    AudioContent updateContentStatus(UUID contentId, ContentStatus status);

    // Search and filtering
    Page<AudioContent> searchAudioContent(String searchTerm, Pageable pageable);
    
    Page<AudioContent> getAudioContentByCategory(ContentCategory category, Pageable pageable);
    
    Page<AudioContent> getAudioContentByLanguage(String language, Pageable pageable);
    
    Page<AudioContent> getAudioContentByStatus(ContentStatus status, Pageable pageable);
    
    List<AudioContent> getFeaturedContent();
    
    List<AudioContent> getPopularContent(int limit);

    // Content discovery
    List<AudioContent> getRecommendedContent(UUID userId);
    
    List<AudioContent> getRelatedContent(UUID contentId);
    
    List<AudioContent> getContentByTags(List<String> tags);

    // User interaction
    void recordPlay(UUID contentId, UUID userId, boolean offlinePlay);
    
    void recordDownload(UUID contentId, UUID userId);
    
    AudioContent rateContent(UUID contentId, UUID userId, java.math.BigDecimal rating, String comment);

    // Content statistics
    long getTotalContentCount();
    
    long getContentCountByCategory(ContentCategory category);
    
    long getContentCountByStatus(ContentStatus status);
    
    long getTotalPlayCount();
    
    long getTotalDownloadCount();

    // File management
    String uploadAudioFile(MultipartFile file, String category);
    
    void deleteAudioFile(String filePath);
    
    String getStreamingUrl(UUID contentId);
    
    byte[] getAudioFileContent(UUID contentId);

    // Offline content
    List<AudioContent> getOfflineAvailableContent();
    
    List<AudioContent> getContentForOfflineSync(UUID userId);
    
    void markContentForOfflineSync(UUID contentId, UUID userId);

    // Content validation
    boolean isContentAccessible(UUID contentId, UUID userId);
    
    boolean canUserDownloadContent(UUID contentId, UUID userId);
    
    boolean isContentExpired(UUID contentId);

    // Analytics
    List<AudioContent> getMostPlayedContent(int limit);
    
    List<AudioContent> getMostDownloadedContent(int limit);
    
    List<AudioContent> getRecentlyAddedContent(int limit);
    
    java.util.Map<ContentCategory, Long> getContentDistributionByCategory();

    // Advanced filtering
    Page<AudioContent> getContentWithFilters(
        ContentCategory category,
        String language,
        ContentStatus status,
        String targetAudience,
        String difficultyLevel,
        Boolean isFeatured,
        Pageable pageable
    );

    // Content moderation
    AudioContent approveContent(UUID contentId, UUID moderatorId);
    
    AudioContent rejectContent(UUID contentId, UUID moderatorId, String reason);
    
    List<AudioContent> getContentPendingReview();

    // Bulk operations
    void publishMultipleContent(List<UUID> contentIds);
    
    void archiveMultipleContent(List<UUID> contentIds);
    
    void deleteMultipleContent(List<UUID> contentIds);
}
