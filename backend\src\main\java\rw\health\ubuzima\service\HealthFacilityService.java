package rw.health.ubuzima.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import rw.health.ubuzima.entity.HealthFacility;
import rw.health.ubuzima.enums.FacilityType;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service interface for HealthFacility operations
 * 
 * <AUTHOR> Development Team
 */
public interface HealthFacilityService {

    // CRUD operations
    HealthFacility createFacility(String name, FacilityType facilityType, String address, 
                                String district, String sector);
    
    HealthFacility updateFacility(UUID facilityId, HealthFacility facilityUpdates);
    
    HealthFacility getFacilityById(UUID facilityId);
    
    Optional<HealthFacility> findFacilityById(UUID facilityId);
    
    Page<HealthFacility> getAllFacilities(Pageable pageable);
    
    void deleteFacility(UUID facilityId);
    
    void softDeleteFacility(UUID facilityId);

    // Search and filtering
    Page<HealthFacility> searchFacilities(String searchTerm, Pageable pageable);
    
    List<HealthFacility> getFacilitiesByType(FacilityType facilityType);
    
    List<HealthFacility> getFacilitiesByDistrict(String district);
    
    List<HealthFacility> getFacilitiesByDistrictAndSector(String district, String sector);

    // Location-based operations
    List<HealthFacility> getNearbyFacilities(BigDecimal latitude, BigDecimal longitude, double radiusKm);
    
    List<HealthFacility> getNearbyFacilitiesByType(BigDecimal latitude, BigDecimal longitude, 
                                                  double radiusKm, FacilityType facilityType);

    // Service-based filtering
    List<HealthFacility> getFamilyPlanningFacilities();
    
    List<HealthFacility> getEmergencyFacilities();
    
    List<HealthFacility> getFacilitiesWithMaternityWard();
    
    List<HealthFacility> getFacilitiesWithLaboratory();
    
    List<HealthFacility> getFacilitiesWithPharmacy();
    
    List<HealthFacility> get24HourFacilities();

    // Rating and reviews
    HealthFacility updateFacilityRating(UUID facilityId, BigDecimal newRating);
    
    List<HealthFacility> getTopRatedFacilities(int limit);
    
    List<HealthFacility> getFacilitiesByMinRating(BigDecimal minRating);

    // Statistics
    long getTotalFacilityCount();
    
    long getFacilityCountByType(FacilityType facilityType);
    
    long getFacilityCountByDistrict(String district);
    
    Map<String, Long> getFacilityCountByDistrict();
    
    Map<FacilityType, Long> getFacilityCountByType();

    // Capacity and resources
    Long getTotalBedCapacityByDistrict(String district);
    
    Long getTotalStaffCountByDistrict(String district);

    // Validation
    boolean existsByName(String name);
    
    boolean existsByLicenseNumber(String licenseNumber);
    
    boolean isValidFacility(UUID facilityId);

    // Advanced filtering
    Page<HealthFacility> getFacilitiesWithFilters(
        FacilityType facilityType,
        String district,
        String sector,
        BigDecimal minRating,
        Boolean hasFamilyPlanning,
        Boolean hasEmergencyServices,
        Pageable pageable
    );

    // Facility management
    HealthFacility updateFacilityServices(UUID facilityId, Map<String, Boolean> services);
    
    HealthFacility updateFacilityLocation(UUID facilityId, BigDecimal latitude, BigDecimal longitude);
    
    HealthFacility updateFacilityOperatingHours(UUID facilityId, String operatingHours);

    // Staff management
    HealthFacility assignStaffToFacility(UUID facilityId, UUID staffId);
    
    HealthFacility removeStaffFromFacility(UUID facilityId, UUID staffId);
    
    List<rw.health.ubuzima.entity.User> getFacilityStaff(UUID facilityId);

    // Reporting
    List<HealthFacility> getFacilitiesNeedingUpdates();
    
    List<HealthFacility> getFacilitiesWithoutCoordinates();
    
    List<HealthFacility> getRecentlyAddedFacilities(java.time.LocalDateTime since);

    // Integration support
    HealthFacility syncFacilityWithExternalSystem(UUID facilityId);
    
    void importFacilitiesFromExternalSystem(List<HealthFacility> facilities);
}
