package rw.health.ubuzima.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.UserRole;
import rw.health.ubuzima.enums.UserStatus;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * Service interface for User operations
 *
 * <AUTHOR> Development Team
 */
public interface UserService {

    // CRUD operations
    User createUser(String firstName, String lastName, String email, String phoneNumber,
                   String password, UserRole role);

    User updateUser(UUID userId, User userUpdates);

    User getUserById(UUID userId);

    Optional<User> findUserById(UUID userId);

    Page<User> getAllUsers(Pageable pageable);

    void deleteUser(UUID userId);

    void softDeleteUser(UUID userId);

    // Authentication and authorization
    Optional<User> findByEmail(String email);
    
    Optional<User> findByPhoneNumber(String phoneNumber);
    
    Optional<User> findByEmailOrPhoneNumber(String identifier);
    
    Optional<User> findActiveUserByEmailOrPhoneNumber(String identifier);
    
    boolean existsByEmail(String email);
    
    boolean existsByPhoneNumber(String phoneNumber);
    
    boolean existsByNationalId(String nationalId);

    // Role-based operations
    Page<User> getUsersByRole(UserRole role, Pageable pageable);

    List<User> getActiveHealthWorkers();

    List<User> getActiveAdmins();

    Page<User> getClientsByFacility(UUID facilityId, Pageable pageable);

    List<User> getHealthWorkersByFacility(UUID facilityId);

    // Status management
    User updateUserStatus(UUID userId, UserStatus status);

    User activateUser(UUID userId);

    User deactivateUser(UUID userId);

    User suspendUser(UUID userId, String reason);

    Page<User> getUsersByStatus(UserStatus status, Pageable pageable);

    // Location-based operations
    Page<User> getUsersByDistrict(String district, Pageable pageable);

    // Search operations
    Page<User> searchUsers(String searchTerm, Pageable pageable);

    Page<User> searchUsersByRole(String searchTerm, UserRole role, Pageable pageable);

    // Password management
    void updatePassword(UUID userId, String newPassword);

    // Account security
    void incrementLoginAttempts(UUID userId);

    void resetLoginAttempts(UUID userId);

    boolean isAccountLocked(UUID userId);

    // Statistics and analytics
    long getTotalUserCount();

    long getActiveUserCount();

    long getUserCountByRole(UserRole role);

    // User validation
    boolean isValidUser(UUID userId);

    // User activity tracking
    void updateLastLogin(UUID userId);
}
