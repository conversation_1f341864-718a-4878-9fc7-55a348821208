package rw.health.ubuzima.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import rw.health.ubuzima.entity.Appointment;
import rw.health.ubuzima.entity.HealthFacility;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.AppointmentStatus;
import rw.health.ubuzima.enums.AppointmentType;
import rw.health.ubuzima.exception.ResourceNotFoundException;
import rw.health.ubuzima.repository.AppointmentRepository;
import rw.health.ubuzima.repository.HealthFacilityRepository;
import rw.health.ubuzima.repository.UserRepository;
import rw.health.ubuzima.service.AppointmentService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Implementation of AppointmentService interface
 * 
 * <AUTHOR> Development Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class AppointmentServiceImpl implements AppointmentService {

    private final AppointmentRepository appointmentRepository;
    private final UserRepository userRepository;
    private final HealthFacilityRepository facilityRepository;

    @Override
    public Appointment createAppointment(UUID clientId, UUID facilityId, UUID healthWorkerId,
                                       LocalDateTime appointmentDate, AppointmentType type, String reason) {
        log.info("Creating appointment for client: {}, facility: {}, health worker: {}", 
                clientId, facilityId, healthWorkerId);

        User client = userRepository.findById(clientId)
                .orElseThrow(() -> new ResourceNotFoundException("Client not found with ID: " + clientId));
        
        HealthFacility facility = facilityRepository.findById(facilityId)
                .orElseThrow(() -> new ResourceNotFoundException("Facility not found with ID: " + facilityId));
        
        User healthWorker = null;
        if (healthWorkerId != null) {
            healthWorker = userRepository.findById(healthWorkerId)
                    .orElseThrow(() -> new ResourceNotFoundException("Health worker not found with ID: " + healthWorkerId));
        }

        // Check for conflicts
        if (healthWorker != null && !isTimeSlotAvailable(healthWorkerId, appointmentDate, 
                appointmentDate.plusMinutes(type.getDefaultDurationMinutes()))) {
            throw new IllegalArgumentException("Time slot is not available");
        }

        Appointment appointment = new Appointment(client, facility, appointmentDate, type);
        appointment.setHealthWorker(healthWorker);
        appointment.setReason(reason);
        appointment.setDurationMinutes(type.getDefaultDurationMinutes());

        Appointment savedAppointment = appointmentRepository.save(appointment);
        log.info("Successfully created appointment with ID: {}", savedAppointment.getId());
        
        return savedAppointment;
    }

    @Override
    public Appointment updateAppointment(UUID appointmentId, Appointment appointmentUpdates) {
        log.info("Updating appointment with ID: {}", appointmentId);
        
        Appointment existingAppointment = getAppointmentById(appointmentId);
        
        if (appointmentUpdates.getAppointmentDate() != null) {
            existingAppointment.setAppointmentDate(appointmentUpdates.getAppointmentDate());
        }
        if (appointmentUpdates.getReason() != null) {
            existingAppointment.setReason(appointmentUpdates.getReason());
        }
        if (appointmentUpdates.getNotes() != null) {
            existingAppointment.setNotes(appointmentUpdates.getNotes());
        }
        if (appointmentUpdates.getHealthWorkerNotes() != null) {
            existingAppointment.setHealthWorkerNotes(appointmentUpdates.getHealthWorkerNotes());
        }

        Appointment savedAppointment = appointmentRepository.save(existingAppointment);
        log.info("Successfully updated appointment with ID: {}", savedAppointment.getId());
        
        return savedAppointment;
    }

    @Override
    @Transactional(readOnly = true)
    public Appointment getAppointmentById(UUID appointmentId) {
        return appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new ResourceNotFoundException("Appointment not found with ID: " + appointmentId));
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Appointment> findAppointmentById(UUID appointmentId) {
        return appointmentRepository.findById(appointmentId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Appointment> getAllAppointments(Pageable pageable) {
        return appointmentRepository.findAll(pageable);
    }

    @Override
    public void deleteAppointment(UUID appointmentId) {
        log.info("Deleting appointment with ID: {}", appointmentId);
        
        Appointment appointment = getAppointmentById(appointmentId);
        appointmentRepository.delete(appointment);
        
        log.info("Successfully deleted appointment with ID: {}", appointmentId);
    }

    @Override
    public void cancelAppointment(UUID appointmentId, String reason, String cancelledBy) {
        log.info("Cancelling appointment with ID: {}", appointmentId);
        
        Appointment appointment = getAppointmentById(appointmentId);
        appointment.cancel(reason, cancelledBy);
        appointmentRepository.save(appointment);
        
        log.info("Successfully cancelled appointment with ID: {}", appointmentId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Appointment> getAppointmentsByClient(UUID clientId, Pageable pageable) {
        return appointmentRepository.findByClientId(clientId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Appointment> getUpcomingAppointmentsByClient(UUID clientId) {
        return appointmentRepository.findUpcomingAppointmentsByClient(clientId, LocalDateTime.now());
    }

    @Override
    @Transactional(readOnly = true)
    public List<Appointment> getAppointmentHistoryByClient(UUID clientId) {
        return appointmentRepository.findCompletedAppointmentsByClient(clientId, Pageable.unpaged()).getContent();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Appointment> getAppointmentsByHealthWorker(UUID healthWorkerId, Pageable pageable) {
        return appointmentRepository.findByHealthWorkerId(healthWorkerId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Appointment> getTodaysAppointmentsByHealthWorker(UUID healthWorkerId) {
        return appointmentRepository.findTodaysAppointmentsByHealthWorker(healthWorkerId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Appointment> getUpcomingAppointmentsByHealthWorker(UUID healthWorkerId) {
        return appointmentRepository.findUpcomingAppointmentsByHealthWorker(healthWorkerId, LocalDateTime.now());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Appointment> getAppointmentsByFacility(UUID facilityId, Pageable pageable) {
        HealthFacility facility = facilityRepository.findById(facilityId)
                .orElseThrow(() -> new ResourceNotFoundException("Facility not found with ID: " + facilityId));
        return appointmentRepository.findByFacility(facility, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Appointment> getTodaysAppointmentsByFacility(UUID facilityId) {
        return appointmentRepository.findTodaysAppointmentsByFacility(facilityId);
    }

    @Override
    public Appointment updateAppointmentStatus(UUID appointmentId, AppointmentStatus status) {
        log.info("Updating status for appointment ID: {} to {}", appointmentId, status);
        
        Appointment appointment = getAppointmentById(appointmentId);
        appointment.setStatus(status);
        
        Appointment savedAppointment = appointmentRepository.save(appointment);
        log.info("Successfully updated appointment status for ID: {}", appointmentId);
        
        return savedAppointment;
    }

    @Override
    public Appointment confirmAppointment(UUID appointmentId) {
        Appointment appointment = getAppointmentById(appointmentId);
        appointment.setStatus(AppointmentStatus.CONFIRMED);
        return appointmentRepository.save(appointment);
    }

    @Override
    public Appointment checkInAppointment(UUID appointmentId) {
        Appointment appointment = getAppointmentById(appointmentId);
        appointment.checkIn();
        return appointmentRepository.save(appointment);
    }

    @Override
    public Appointment startAppointment(UUID appointmentId) {
        Appointment appointment = getAppointmentById(appointmentId);
        appointment.start();
        return appointmentRepository.save(appointment);
    }

    @Override
    public Appointment completeAppointment(UUID appointmentId) {
        Appointment appointment = getAppointmentById(appointmentId);
        appointment.complete();
        return appointmentRepository.save(appointment);
    }

    @Override
    public Appointment markNoShow(UUID appointmentId) {
        Appointment appointment = getAppointmentById(appointmentId);
        appointment.markNoShow();
        return appointmentRepository.save(appointment);
    }

    @Override
    public Appointment rescheduleAppointment(UUID appointmentId, LocalDateTime newDate, String reason) {
        log.info("Rescheduling appointment ID: {} to new date: {}", appointmentId, newDate);
        
        Appointment appointment = getAppointmentById(appointmentId);
        appointment.reschedule(newDate, reason);
        
        Appointment savedAppointment = appointmentRepository.save(appointment);
        log.info("Successfully rescheduled appointment ID: {}", appointmentId);
        
        return savedAppointment;
    }

    @Override
    @Transactional(readOnly = true)
    public List<String> getAvailableTimeSlots(UUID healthWorkerId, LocalDate date) {
        return appointmentRepository.findBookedTimeSlots(healthWorkerId, date);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isTimeSlotAvailable(UUID healthWorkerId, LocalDateTime startTime, LocalDateTime endTime) {
        List<Appointment> conflicts = appointmentRepository.findConflictingAppointments(healthWorkerId, startTime, endTime);
        return conflicts.isEmpty();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Appointment> getConflictingAppointments(UUID healthWorkerId, LocalDateTime startTime, LocalDateTime endTime) {
        return appointmentRepository.findConflictingAppointments(healthWorkerId, startTime, endTime);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Appointment> searchAppointments(String searchTerm, Pageable pageable) {
        // This would need to be implemented based on specific search requirements
        return appointmentRepository.findAll(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Appointment> getAppointmentsByStatus(AppointmentStatus status, Pageable pageable) {
        return appointmentRepository.findByStatus(status, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Appointment> getAppointmentsByType(AppointmentType type, Pageable pageable) {
        return appointmentRepository.findByAppointmentType(type, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Appointment> getAppointmentsByDateRange(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        return appointmentRepository.findByAppointmentDateBetween(startDate, endDate, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public long getTotalAppointmentCount() {
        return appointmentRepository.count();
    }

    @Override
    @Transactional(readOnly = true)
    public long getAppointmentCountByStatus(AppointmentStatus status) {
        return appointmentRepository.findByStatus(status, Pageable.unpaged()).getTotalElements();
    }

    @Override
    @Transactional(readOnly = true)
    public long getAppointmentCountByFacility(UUID facilityId) {
        return appointmentRepository.countByFacilityAndStatus(facilityId, AppointmentStatus.SCHEDULED);
    }

    @Override
    @Transactional(readOnly = true)
    public long getAppointmentCountByHealthWorker(UUID healthWorkerId) {
        return appointmentRepository.countTodaysAppointmentsByHealthWorker(healthWorkerId);
    }

    @Override
    public void sendAppointmentReminder(UUID appointmentId) {
        // Implementation for sending reminders would go here
        log.info("Sending reminder for appointment ID: {}", appointmentId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Appointment> getAppointmentsNeedingReminders() {
        LocalDateTime reminderStart = LocalDateTime.now().plusHours(24);
        LocalDateTime reminderEnd = LocalDateTime.now().plusHours(25);
        return appointmentRepository.findAppointmentsNeedingReminders(reminderStart, reminderEnd);
    }

    @Override
    public void sendAppointmentConfirmation(UUID appointmentId) {
        // Implementation for sending confirmations would go here
        log.info("Sending confirmation for appointment ID: {}", appointmentId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Appointment> getEmergencyAppointments() {
        return appointmentRepository.findByIsEmergencyTrue();
    }

    @Override
    public Appointment createEmergencyAppointment(UUID clientId, UUID facilityId, String reason) {
        return createAppointment(clientId, facilityId, null, LocalDateTime.now().plusHours(1), 
                               AppointmentType.EMERGENCY, reason);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Appointment> getFollowUpAppointments() {
        return appointmentRepository.findByIsFollowUpTrue();
    }

    @Override
    public Appointment createFollowUpAppointment(UUID originalAppointmentId, LocalDateTime followUpDate) {
        Appointment originalAppointment = getAppointmentById(originalAppointmentId);
        
        Appointment followUp = new Appointment(
            originalAppointment.getClient(),
            originalAppointment.getFacility(),
            followUpDate,
            AppointmentType.FOLLOW_UP
        );
        followUp.setHealthWorker(originalAppointment.getHealthWorker());
        followUp.setIsFollowUp(true);
        followUp.setReason("Follow-up appointment");
        
        return appointmentRepository.save(followUp);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canClientBookAppointment(UUID clientId, LocalDateTime appointmentDate) {
        // Business logic to determine if client can book appointment
        return appointmentDate.isAfter(LocalDateTime.now());
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canAppointmentBeModified(UUID appointmentId) {
        Appointment appointment = getAppointmentById(appointmentId);
        return appointment.canBeRescheduled();
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canAppointmentBeCancelled(UUID appointmentId) {
        Appointment appointment = getAppointmentById(appointmentId);
        return appointment.canBeCancelled();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Appointment> getOverdueAppointments() {
        return appointmentRepository.findOverdueAppointments(LocalDateTime.now());
    }

    @Override
    @Transactional(readOnly = true)
    public List<Appointment> getRecentlyCancelledAppointments() {
        LocalDateTime since = LocalDateTime.now().minusDays(7);
        return appointmentRepository.findRecentlyCancelledAppointments(since);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Appointment> getNoShowAppointments() {
        LocalDate since = LocalDate.now().minusDays(30);
        return appointmentRepository.findNoShowAppointments(since);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Appointment> getAppointmentsWithFilters(UUID clientId, UUID healthWorkerId, UUID facilityId,
                                                       AppointmentStatus status, AppointmentType type,
                                                       LocalDateTime startDate, LocalDateTime endDate,
                                                       Pageable pageable) {
        return appointmentRepository.findWithFilters(clientId, healthWorkerId, facilityId, status, type,
                                                    startDate, endDate, pageable);
    }
}
