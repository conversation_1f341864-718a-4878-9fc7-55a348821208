package rw.health.ubuzima.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.UserRole;
import rw.health.ubuzima.enums.UserStatus;
import rw.health.ubuzima.exception.ResourceNotFoundException;
import rw.health.ubuzima.exception.DuplicateResourceException;
import rw.health.ubuzima.repository.UserRepository;
import rw.health.ubuzima.service.UserService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * Implementation of UserService interface
 * 
 * <AUTHOR> Development Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    public User createUser(String firstName, String lastName, String email, String phoneNumber, 
                          String password, UserRole role) {
        log.info("Creating new user with email: {}", email);
        
        // Check for existing user
        if (email != null && userRepository.existsByEmail(email)) {
            throw new DuplicateResourceException("User with email " + email + " already exists");
        }
        
        if (phoneNumber != null && userRepository.existsByPhoneNumber(phoneNumber)) {
            throw new DuplicateResourceException("User with phone number " + phoneNumber + " already exists");
        }

        User user = new User();
        user.setFirstName(firstName);
        user.setLastName(lastName);
        user.setEmail(email);
        user.setPhoneNumber(phoneNumber);
        user.setPasswordHash(passwordEncoder.encode(password));
        user.setRole(role);
        user.setStatus(UserStatus.ACTIVE);

        User savedUser = userRepository.save(user);
        log.info("Successfully created user with ID: {}", savedUser.getId());
        
        return savedUser;
    }

    @Override
    public User updateUser(UUID userId, User userUpdates) {
        log.info("Updating user with ID: {}", userId);
        
        User existingUser = getUserById(userId);
        
        // Update fields if provided
        if (userUpdates.getFirstName() != null) {
            existingUser.setFirstName(userUpdates.getFirstName());
        }
        if (userUpdates.getLastName() != null) {
            existingUser.setLastName(userUpdates.getLastName());
        }
        if (userUpdates.getEmail() != null && !userUpdates.getEmail().equals(existingUser.getEmail())) {
            if (userRepository.existsByEmail(userUpdates.getEmail())) {
                throw new DuplicateResourceException("Email already exists");
            }
            existingUser.setEmail(userUpdates.getEmail());
            existingUser.setEmailVerified(false);
        }
        if (userUpdates.getPhoneNumber() != null && !userUpdates.getPhoneNumber().equals(existingUser.getPhoneNumber())) {
            if (userRepository.existsByPhoneNumber(userUpdates.getPhoneNumber())) {
                throw new DuplicateResourceException("Phone number already exists");
            }
            existingUser.setPhoneNumber(userUpdates.getPhoneNumber());
            existingUser.setPhoneVerified(false);
        }
        if (userUpdates.getGender() != null) {
            existingUser.setGender(userUpdates.getGender());
        }
        if (userUpdates.getDateOfBirth() != null) {
            existingUser.setDateOfBirth(userUpdates.getDateOfBirth());
        }
        if (userUpdates.getAddress() != null) {
            existingUser.setAddress(userUpdates.getAddress());
        }
        if (userUpdates.getDistrict() != null) {
            existingUser.setDistrict(userUpdates.getDistrict());
        }
        if (userUpdates.getSector() != null) {
            existingUser.setSector(userUpdates.getSector());
        }
        if (userUpdates.getCell() != null) {
            existingUser.setCell(userUpdates.getCell());
        }
        if (userUpdates.getVillage() != null) {
            existingUser.setVillage(userUpdates.getVillage());
        }
        if (userUpdates.getPreferredLanguage() != null) {
            existingUser.setPreferredLanguage(userUpdates.getPreferredLanguage());
        }

        User savedUser = userRepository.save(existingUser);
        log.info("Successfully updated user with ID: {}", savedUser.getId());
        
        return savedUser;
    }

    @Override
    @Transactional(readOnly = true)
    public User getUserById(UUID userId) {
        return userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<User> findUserById(UUID userId) {
        return userRepository.findById(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<User> getAllUsers(Pageable pageable) {
        return userRepository.findByIsActiveTrue(pageable);
    }

    @Override
    public void deleteUser(UUID userId) {
        log.info("Deleting user with ID: {}", userId);
        
        User user = getUserById(userId);
        userRepository.delete(user);
        
        log.info("Successfully deleted user with ID: {}", userId);
    }

    @Override
    public void softDeleteUser(UUID userId) {
        log.info("Soft deleting user with ID: {}", userId);
        
        User user = getUserById(userId);
        user.setIsActive(false);
        user.setStatus(UserStatus.INACTIVE);
        userRepository.save(user);
        
        log.info("Successfully soft deleted user with ID: {}", userId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<User> findByPhoneNumber(String phoneNumber) {
        return userRepository.findByPhoneNumber(phoneNumber);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<User> findByEmailOrPhoneNumber(String identifier) {
        return userRepository.findByEmailOrPhoneNumber(identifier, identifier);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<User> findActiveUserByEmailOrPhoneNumber(String identifier) {
        return userRepository.findActiveUserByEmailOrPhoneNumber(identifier);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByPhoneNumber(String phoneNumber) {
        return userRepository.existsByPhoneNumber(phoneNumber);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByNationalId(String nationalId) {
        return userRepository.existsByNationalId(nationalId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<User> getUsersByRole(UserRole role, Pageable pageable) {
        return userRepository.findByRoleAndStatus(role, UserStatus.ACTIVE, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<User> getActiveHealthWorkers() {
        return userRepository.findByRoleAndStatus(UserRole.HEALTH_WORKER, UserStatus.ACTIVE);
    }

    @Override
    @Transactional(readOnly = true)
    public List<User> getActiveAdmins() {
        return userRepository.findActiveAdmins();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<User> getClientsByFacility(UUID facilityId, Pageable pageable) {
        return userRepository.findClientsByFacility(facilityId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<User> getHealthWorkersByFacility(UUID facilityId) {
        return userRepository.findActiveHealthWorkersByFacility(facilityId);
    }

    @Override
    public User updateUserStatus(UUID userId, UserStatus status) {
        log.info("Updating status for user ID: {} to {}", userId, status);
        
        User user = getUserById(userId);
        user.setStatus(status);
        
        User savedUser = userRepository.save(user);
        log.info("Successfully updated user status for ID: {}", userId);
        
        return savedUser;
    }

    @Override
    public User activateUser(UUID userId) {
        return updateUserStatus(userId, UserStatus.ACTIVE);
    }

    @Override
    public User deactivateUser(UUID userId) {
        User user = updateUserStatus(userId, UserStatus.INACTIVE);
        user.setIsActive(false);
        return userRepository.save(user);
    }

    @Override
    public User suspendUser(UUID userId, String reason) {
        log.info("Suspending user ID: {} with reason: {}", userId, reason);
        
        User user = getUserById(userId);
        user.setStatus(UserStatus.SUSPENDED);
        // Note: In a real implementation, you might want to store the suspension reason
        
        User savedUser = userRepository.save(user);
        log.info("Successfully suspended user ID: {}", userId);
        
        return savedUser;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<User> getUsersByStatus(UserStatus status, Pageable pageable) {
        return userRepository.findByStatus(status, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<User> getUsersByDistrict(String district, Pageable pageable) {
        return userRepository.findByDistrict(district, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<User> searchUsers(String searchTerm, Pageable pageable) {
        return userRepository.searchUsers(searchTerm, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<User> searchUsersByRole(String searchTerm, UserRole role, Pageable pageable) {
        return userRepository.searchUsersByRole(searchTerm, role, pageable);
    }

    @Override
    public void updatePassword(UUID userId, String newPassword) {
        log.info("Updating password for user ID: {}", userId);
        
        User user = getUserById(userId);
        user.setPasswordHash(passwordEncoder.encode(newPassword));
        user.resetLoginAttempts();
        
        userRepository.save(user);
        log.info("Successfully updated password for user ID: {}", userId);
    }

    @Override
    public void updateLastLogin(UUID userId) {
        User user = getUserById(userId);
        user.setLastLoginAt(LocalDateTime.now());
        user.resetLoginAttempts();
        userRepository.save(user);
    }

    @Override
    @Transactional(readOnly = true)
    public long getTotalUserCount() {
        return userRepository.count();
    }

    @Override
    @Transactional(readOnly = true)
    public long getActiveUserCount() {
        return userRepository.countActiveUsersByRole(null);
    }

    @Override
    @Transactional(readOnly = true)
    public long getUserCountByRole(UserRole role) {
        return userRepository.countActiveUsersByRole(role);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isValidUser(UUID userId) {
        return userRepository.findById(userId)
                .map(user -> user.isActive() && user.getStatus() == UserStatus.ACTIVE)
                .orElse(false);
    }

    @Override
    public void incrementLoginAttempts(UUID userId) {
        User user = getUserById(userId);
        user.incrementLoginAttempts();
        
        // Lock account after 5 failed attempts
        if (user.getLoginAttempts() >= 5) {
            user.lockAccount(30); // Lock for 30 minutes
        }
        
        userRepository.save(user);
    }

    @Override
    public void resetLoginAttempts(UUID userId) {
        User user = getUserById(userId);
        user.resetLoginAttempts();
        userRepository.save(user);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isAccountLocked(UUID userId) {
        return userRepository.findById(userId)
                .map(User::isAccountLocked)
                .orElse(false);
    }
}
