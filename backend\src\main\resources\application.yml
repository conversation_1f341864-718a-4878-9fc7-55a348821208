server:
  port: 8080
  servlet:
    context-path: /api/v1

spring:
  application:
    name: ubuzima-backend
    
  profiles:
    active: dev
    
  datasource:
    url: *******************************************
    username: ubuzima_user
    password: ubuzima_password
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 25
        order_inserts: true
        order_updates: true
        
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${JWT_ISSUER_URI:http://localhost:8080}
          
  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:}
    password: ${MAIL_PASSWORD:}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      
  cache:
    type: simple
    
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
      
logging:
  level:
    rw.health.ubuzima: INFO
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ubuzima-backend.log
    
# Application specific configuration
ubuzima:
  jwt:
    secret: ${JWT_SECRET:mySecretKey}
    expiration: ${JWT_EXPIRATION:86400000} # 24 hours
    refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800000} # 7 days
    
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:8080}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
    
  file:
    upload-dir: ${FILE_UPLOAD_DIR:./uploads}
    max-size: ${FILE_MAX_SIZE:********} # 10MB
    
  sms:
    provider: ${SMS_PROVIDER:twilio}
    api-key: ${SMS_API_KEY:}
    api-secret: ${SMS_API_SECRET:}
    from-number: ${SMS_FROM_NUMBER:}
    
  notification:
    firebase:
      service-account-key: ${FIREBASE_SERVICE_ACCOUNT_KEY:}
      
  encryption:
    algorithm: AES/GCB/NoPadding
    key: ${ENCRYPTION_KEY:myEncryptionKey123}

---
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: ********************************************
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: create-drop
logging:
  level:
    rw.health.ubuzima: DEBUG

---
spring:
  config:
    activate:
      on-profile: test
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect

---
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: ${DATABASE_URL}
    username: ${DATABASE_USERNAME}
    password: ${DATABASE_PASSWORD}
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate
logging:
  level:
    rw.health.ubuzima: WARN
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
