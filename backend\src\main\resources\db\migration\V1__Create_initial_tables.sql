-- Ubuzima Database Schema - Initial Migration
-- Version 1.0.0
-- Author: Ubuzima Development Team

-- Create health_facilities table
CREATE TABLE health_facilities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    facility_type VARCHAR(50) NOT NULL,
    license_number VARCHAR(50) UNIQUE,
    phone_number VARCHAR(20),
    email VARCHAR(100),
    website VARCHAR(255),
    address VARCHAR(255) NOT NULL,
    district VARCHAR(50) NOT NULL,
    sector VARCHAR(50) NOT NULL,
    cell VARCHAR(50),
    village VARCHAR(50),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    operating_hours VARCHAR(255),
    emergency_contact VARCHAR(20),
    bed_capacity INTEGER,
    staff_count INTEGER,
    services_offered VARCHAR(1000),
    equipment_available VARCHAR(1000),
    is_24_hours BOOLEAN NOT NULL DEFAULT FALSE,
    has_emergency_services BOOLEAN NOT NULL DEFAULT FALSE,
    has_maternity_ward BOOLEAN NOT NULL DEFAULT FALSE,
    has_family_planning BOOLEAN NOT NULL DEFAULT TRUE,
    has_laboratory BOOLEAN NOT NULL DEFAULT FALSE,
    has_pharmacy BOOLEAN NOT NULL DEFAULT FALSE,
    has_ambulance BOOLEAN NOT NULL DEFAULT FALSE,
    accreditation_level VARCHAR(50),
    rating DECIMAL(3,2),
    total_reviews INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    version BIGINT DEFAULT 0
);

-- Create users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone_number VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL,
    status VARCHAR(30) NOT NULL DEFAULT 'ACTIVE',
    gender VARCHAR(20),
    date_of_birth DATE,
    national_id VARCHAR(16) UNIQUE,
    address VARCHAR(255),
    district VARCHAR(50),
    sector VARCHAR(50),
    cell VARCHAR(50),
    village VARCHAR(50),
    profile_picture_url VARCHAR(255),
    preferred_language VARCHAR(10) DEFAULT 'rw',
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    last_login_at TIMESTAMP,
    email_verified BOOLEAN NOT NULL DEFAULT FALSE,
    phone_verified BOOLEAN NOT NULL DEFAULT FALSE,
    two_factor_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    login_attempts INTEGER NOT NULL DEFAULT 0,
    locked_until TIMESTAMP,
    password_reset_token VARCHAR(255),
    password_reset_expires_at TIMESTAMP,
    email_verification_token VARCHAR(255),
    phone_verification_token VARCHAR(255),
    facility_id UUID REFERENCES health_facilities(id),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    version BIGINT DEFAULT 0
);

-- Create user_sessions table
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    refresh_token VARCHAR(255),
    expires_at TIMESTAMP NOT NULL,
    refresh_expires_at TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    device_type VARCHAR(50),
    device_id VARCHAR(100),
    location VARCHAR(100),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    last_activity_at TIMESTAMP,
    logout_at TIMESTAMP,
    logout_reason VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    version BIGINT DEFAULT 0
);

-- Create appointments table
CREATE TABLE appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL REFERENCES users(id),
    health_worker_id UUID REFERENCES users(id),
    facility_id UUID NOT NULL REFERENCES health_facilities(id),
    appointment_date TIMESTAMP NOT NULL,
    duration_minutes INTEGER DEFAULT 30,
    appointment_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'SCHEDULED',
    reason VARCHAR(500),
    notes VARCHAR(1000),
    health_worker_notes VARCHAR(1000),
    is_emergency BOOLEAN NOT NULL DEFAULT FALSE,
    is_follow_up BOOLEAN NOT NULL DEFAULT FALSE,
    reminder_sent BOOLEAN NOT NULL DEFAULT FALSE,
    reminder_sent_at TIMESTAMP,
    checked_in_at TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    cancelled_at TIMESTAMP,
    cancellation_reason VARCHAR(500),
    cancelled_by VARCHAR(255),
    rescheduled_from TIMESTAMP,
    reschedule_reason VARCHAR(500),
    consultation_fee DECIMAL(10,2),
    payment_status VARCHAR(20) DEFAULT 'PENDING',
    payment_method VARCHAR(50),
    payment_reference VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    version BIGINT DEFAULT 0
);

-- Create health_records table
CREATE TABLE health_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID NOT NULL REFERENCES users(id),
    health_worker_id UUID REFERENCES users(id),
    facility_id UUID REFERENCES health_facilities(id),
    appointment_id UUID REFERENCES appointments(id),
    record_type VARCHAR(50) NOT NULL,
    record_date DATE NOT NULL,
    title VARCHAR(100),
    description VARCHAR(2000),
    symptoms VARCHAR(1000),
    diagnosis VARCHAR(1000),
    treatment VARCHAR(1000),
    medications VARCHAR(1000),
    recommendations VARCHAR(1000),
    follow_up_instructions VARCHAR(500),
    follow_up_date DATE,
    -- Vital Signs
    weight_kg DECIMAL(5,2),
    height_cm DECIMAL(5,2),
    bmi DECIMAL(4,2),
    blood_pressure_systolic INTEGER,
    blood_pressure_diastolic INTEGER,
    heart_rate INTEGER,
    temperature_celsius DECIMAL(4,2),
    respiratory_rate INTEGER,
    oxygen_saturation INTEGER,
    -- Family Planning Specific
    contraceptive_method VARCHAR(100),
    contraceptive_start_date DATE,
    last_menstrual_period DATE,
    cycle_length_days INTEGER,
    is_pregnant BOOLEAN,
    pregnancy_weeks INTEGER,
    expected_delivery_date DATE,
    number_of_pregnancies INTEGER,
    number_of_births INTEGER,
    number_of_miscarriages INTEGER,
    -- Laboratory Results
    lab_results TEXT,
    hiv_status VARCHAR(20),
    hiv_test_date DATE,
    sti_screening_results VARCHAR(500),
    sti_test_date DATE,
    -- Additional fields
    is_confidential BOOLEAN NOT NULL DEFAULT FALSE,
    is_emergency BOOLEAN NOT NULL DEFAULT FALSE,
    notes VARCHAR(500),
    next_appointment_recommended DATE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    version BIGINT DEFAULT 0
);

-- Create health_record_data table for additional key-value data
CREATE TABLE health_record_data (
    health_record_id UUID NOT NULL REFERENCES health_records(id) ON DELETE CASCADE,
    data_key VARCHAR(255) NOT NULL,
    data_value VARCHAR(1000),
    PRIMARY KEY (health_record_id, data_key)
);

-- Create facility_reviews table
CREATE TABLE facility_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    facility_id UUID NOT NULL REFERENCES health_facilities(id),
    user_id UUID NOT NULL REFERENCES users(id),
    rating DECIMAL(3,2) NOT NULL CHECK (rating >= 1.0 AND rating <= 5.0),
    title VARCHAR(100),
    comment VARCHAR(1000),
    is_anonymous BOOLEAN NOT NULL DEFAULT FALSE,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    helpful_count INTEGER NOT NULL DEFAULT 0,
    not_helpful_count INTEGER NOT NULL DEFAULT 0,
    is_flagged BOOLEAN NOT NULL DEFAULT FALSE,
    flag_reason VARCHAR(200),
    moderated_by VARCHAR(255),
    -- Service-specific ratings
    staff_rating DECIMAL(3,2) CHECK (staff_rating >= 1.0 AND staff_rating <= 5.0),
    cleanliness_rating DECIMAL(3,2) CHECK (cleanliness_rating >= 1.0 AND cleanliness_rating <= 5.0),
    wait_time_rating DECIMAL(3,2) CHECK (wait_time_rating >= 1.0 AND wait_time_rating <= 5.0),
    communication_rating DECIMAL(3,2) CHECK (communication_rating >= 1.0 AND communication_rating <= 5.0),
    facilities_rating DECIMAL(3,2) CHECK (facilities_rating >= 1.0 AND facilities_rating <= 5.0),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    version BIGINT DEFAULT 0,
    UNIQUE(facility_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX idx_user_email ON users(email);
CREATE INDEX idx_user_phone ON users(phone_number);
CREATE INDEX idx_user_role ON users(role);
CREATE INDEX idx_user_status ON users(status);
CREATE INDEX idx_user_facility ON users(facility_id);

CREATE INDEX idx_facility_name ON health_facilities(name);
CREATE INDEX idx_facility_type ON health_facilities(facility_type);
CREATE INDEX idx_facility_district ON health_facilities(district);
CREATE INDEX idx_facility_location ON health_facilities(latitude, longitude);

CREATE INDEX idx_session_user ON user_sessions(user_id);
CREATE INDEX idx_session_token ON user_sessions(session_token);
CREATE INDEX idx_session_expires ON user_sessions(expires_at);

CREATE INDEX idx_appointment_client ON appointments(client_id);
CREATE INDEX idx_appointment_health_worker ON appointments(health_worker_id);
CREATE INDEX idx_appointment_facility ON appointments(facility_id);
CREATE INDEX idx_appointment_date ON appointments(appointment_date);
CREATE INDEX idx_appointment_status ON appointments(status);

CREATE INDEX idx_health_record_client ON health_records(client_id);
CREATE INDEX idx_health_record_type ON health_records(record_type);
CREATE INDEX idx_health_record_date ON health_records(record_date);
CREATE INDEX idx_health_record_health_worker ON health_records(health_worker_id);

CREATE INDEX idx_review_facility ON facility_reviews(facility_id);
CREATE INDEX idx_review_user ON facility_reviews(user_id);
CREATE INDEX idx_review_rating ON facility_reviews(rating);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_health_facilities_updated_at BEFORE UPDATE ON health_facilities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_sessions_updated_at BEFORE UPDATE ON user_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_appointments_updated_at BEFORE UPDATE ON appointments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_health_records_updated_at BEFORE UPDATE ON health_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_facility_reviews_updated_at BEFORE UPDATE ON facility_reviews
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
