-- Audio Content Management Tables
-- Version 2.0.0
-- Author: Ubuzima Development Team

-- Create audio_content table
CREATE TABLE audio_content (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(200) NOT NULL,
    description VARCHAR(1000),
    category VARCHAR(50) NOT NULL,
    language VARCHAR(10) NOT NULL DEFAULT 'rw',
    file_path VARCHAR(500) NOT NULL,
    file_url VARCHAR(500),
    file_size_bytes BIGINT,
    duration_seconds INTEGER,
    mime_type VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT',
    tags VARCHAR(500),
    transcript TEXT,
    keywords VARCHAR(500),
    target_audience VARCHAR(100),
    age_group VARCHAR(50),
    difficulty_level VARCHAR(20) DEFAULT 'BASIC',
    play_count BIGINT NOT NULL DEFAULT 0,
    download_count BIGINT NOT NULL DEFAULT 0,
    rating DECIMAL(3,2),
    total_ratings INTEGER NOT NULL DEFAULT 0,
    is_featured BOOLEAN NOT NULL DEFAULT FALSE,
    is_offline_available BOOLEAN NOT NULL DEFAULT TRUE,
    published_at TIMESTAMP,
    expires_at TIMESTAMP,
    sort_order INTEGER DEFAULT 0,
    author_id UUID REFERENCES users(id),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    version BIGINT DEFAULT 0
);

-- Create content_ratings table
CREATE TABLE content_ratings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    audio_content_id UUID NOT NULL REFERENCES audio_content(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    rating DECIMAL(3,2) NOT NULL CHECK (rating >= 1.0 AND rating <= 5.0),
    comment VARCHAR(500),
    is_helpful BOOLEAN NOT NULL DEFAULT TRUE,
    helpful_count INTEGER NOT NULL DEFAULT 0,
    not_helpful_count INTEGER NOT NULL DEFAULT 0,
    is_flagged BOOLEAN NOT NULL DEFAULT FALSE,
    flag_reason VARCHAR(200),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    version BIGINT DEFAULT 0,
    UNIQUE(audio_content_id, user_id)
);

-- Create content_play_history table
CREATE TABLE content_play_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    audio_content_id UUID NOT NULL REFERENCES audio_content(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    played_at TIMESTAMP NOT NULL,
    duration_played_seconds INTEGER,
    total_duration_seconds INTEGER,
    completion_percentage DECIMAL(5,2),
    completed BOOLEAN NOT NULL DEFAULT FALSE,
    device_type VARCHAR(50),
    platform VARCHAR(50),
    offline_play BOOLEAN NOT NULL DEFAULT FALSE,
    session_id VARCHAR(100),
    ip_address VARCHAR(45),
    location VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    version BIGINT DEFAULT 0
);

-- Create content_downloads table
CREATE TABLE content_downloads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    audio_content_id UUID NOT NULL REFERENCES audio_content(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    downloaded_at TIMESTAMP NOT NULL,
    file_size_bytes BIGINT,
    download_duration_seconds INTEGER,
    device_type VARCHAR(50),
    platform VARCHAR(50),
    ip_address VARCHAR(45),
    location VARCHAR(100),
    is_successful BOOLEAN NOT NULL DEFAULT TRUE,
    error_message VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    version BIGINT DEFAULT 0
);

-- Create user_content_preferences table
CREATE TABLE user_content_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    preferred_categories VARCHAR(500), -- JSON array of categories
    preferred_language VARCHAR(10) DEFAULT 'rw',
    difficulty_level VARCHAR(20) DEFAULT 'BASIC',
    auto_download BOOLEAN NOT NULL DEFAULT FALSE,
    offline_sync_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    notification_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    playback_speed DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    version BIGINT DEFAULT 0,
    UNIQUE(user_id)
);

-- Create content_playlists table
CREATE TABLE content_playlists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    is_public BOOLEAN NOT NULL DEFAULT FALSE,
    is_system_generated BOOLEAN NOT NULL DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    version BIGINT DEFAULT 0
);

-- Create playlist_items table
CREATE TABLE playlist_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    playlist_id UUID NOT NULL REFERENCES content_playlists(id) ON DELETE CASCADE,
    audio_content_id UUID NOT NULL REFERENCES audio_content(id) ON DELETE CASCADE,
    sort_order INTEGER DEFAULT 0,
    added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    version BIGINT DEFAULT 0,
    UNIQUE(playlist_id, audio_content_id)
);

-- Create indexes for better performance
CREATE INDEX idx_audio_content_category ON audio_content(category);
CREATE INDEX idx_audio_content_language ON audio_content(language);
CREATE INDEX idx_audio_content_status ON audio_content(status);
CREATE INDEX idx_audio_content_featured ON audio_content(is_featured);
CREATE INDEX idx_audio_content_published ON audio_content(published_at);
CREATE INDEX idx_audio_content_author ON audio_content(author_id);
CREATE INDEX idx_audio_content_tags ON audio_content USING gin(to_tsvector('english', tags));

CREATE INDEX idx_content_rating_content ON content_ratings(audio_content_id);
CREATE INDEX idx_content_rating_user ON content_ratings(user_id);
CREATE INDEX idx_content_rating_rating ON content_ratings(rating);

CREATE INDEX idx_play_history_content ON content_play_history(audio_content_id);
CREATE INDEX idx_play_history_user ON content_play_history(user_id);
CREATE INDEX idx_play_history_date ON content_play_history(played_at);
CREATE INDEX idx_play_history_completed ON content_play_history(completed);

CREATE INDEX idx_content_downloads_content ON content_downloads(audio_content_id);
CREATE INDEX idx_content_downloads_user ON content_downloads(user_id);
CREATE INDEX idx_content_downloads_date ON content_downloads(downloaded_at);

CREATE INDEX idx_user_preferences_user ON user_content_preferences(user_id);

CREATE INDEX idx_playlist_user ON content_playlists(user_id);
CREATE INDEX idx_playlist_public ON content_playlists(is_public);

CREATE INDEX idx_playlist_items_playlist ON playlist_items(playlist_id);
CREATE INDEX idx_playlist_items_content ON playlist_items(audio_content_id);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_audio_content_updated_at BEFORE UPDATE ON audio_content
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_ratings_updated_at BEFORE UPDATE ON content_ratings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_play_history_updated_at BEFORE UPDATE ON content_play_history
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_downloads_updated_at BEFORE UPDATE ON content_downloads
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_content_preferences_updated_at BEFORE UPDATE ON user_content_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_playlists_updated_at BEFORE UPDATE ON content_playlists
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_playlist_items_updated_at BEFORE UPDATE ON playlist_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample audio content categories and initial data
INSERT INTO audio_content (title, description, category, language, file_path, status, is_featured, target_audience, difficulty_level, sort_order) VALUES
('Kurinda inda - Amoko y''ubufasha', 'Amoko atandukanye y''ubufasha bwo kurinda inda', 'FAMILY_PLANNING', 'rw', '/audio/family_planning_methods_rw.mp3', 'PUBLISHED', true, 'women', 'BASIC', 1),
('Contraceptive Methods Overview', 'Overview of different contraceptive methods available', 'CONTRACEPTION', 'en', '/audio/contraceptive_methods_en.mp3', 'PUBLISHED', true, 'women', 'BASIC', 2),
('Méthodes de planification familiale', 'Aperçu des méthodes de planification familiale', 'FAMILY_PLANNING', 'fr', '/audio/family_planning_fr.mp3', 'PUBLISHED', false, 'women', 'BASIC', 3),
('Kwita ku nda - Ibikenewe', 'Ibikenewe mu gihe cy''inda', 'PRENATAL_CARE', 'rw', '/audio/prenatal_care_rw.mp3', 'PUBLISHED', true, 'pregnant_women', 'BASIC', 4),
('Ubuzima bw''ingimbi', 'Ubuzima bw''ingimbi n''ubwangavu', 'ADOLESCENT_HEALTH', 'rw', '/audio/adolescent_health_rw.mp3', 'PUBLISHED', false, 'youth', 'BASIC', 5);

-- Insert sample playlists
INSERT INTO content_playlists (user_id, name, description, is_public, is_system_generated) VALUES
((SELECT id FROM users WHERE role = 'CLIENT' LIMIT 1), 'My Favorites', 'My favorite health education content', false, false),
((SELECT id FROM users WHERE role = 'CLIENT' LIMIT 1), 'Family Planning Basics', 'Essential family planning information', true, true);

-- Insert sample playlist items
INSERT INTO playlist_items (playlist_id, audio_content_id, sort_order) VALUES
((SELECT id FROM content_playlists WHERE name = 'Family Planning Basics' LIMIT 1), (SELECT id FROM audio_content WHERE category = 'FAMILY_PLANNING' LIMIT 1), 1),
((SELECT id FROM content_playlists WHERE name = 'Family Planning Basics' LIMIT 1), (SELECT id FROM audio_content WHERE category = 'CONTRACEPTION' LIMIT 1), 2);
