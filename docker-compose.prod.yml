version: '3.8'

services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: ubuzima-db-prod
    environment:
      POSTGRES_DB: ubuzima_prod
      POSTGRES_USER: ${DATABASE_USERNAME:-ubuzima_user}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD:-ubuzima_password}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./backend/src/main/resources/db/migration:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - ubuzima-network-prod
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DATABASE_USERNAME:-ubuzima_user} -d ubuzima_prod"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Spring Boot Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    image: ubuzima-backend:production
    container_name: ubuzima-backend-prod
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DATABASE_URL: ********************************************
      DATABASE_USERNAME: ${DATABASE_USERNAME:-ubuzima_user}
      DATABASE_PASSWORD: ${DATABASE_PASSWORD:-ubuzima_password}
      JWT_SECRET: ${JWT_SECRET}
      CORS_ALLOWED_ORIGINS: ${CORS_ALLOWED_ORIGINS}
      GEMINI_API_KEY: ${GEMINI_API_KEY}
      GOOGLE_MAPS_API_KEY: ${GOOGLE_MAPS_API_KEY}
      SENTRY_DSN: ${SENTRY_DSN}
    ports:
      - "8080:8080"
    depends_on:
      database:
        condition: service_healthy
    networks:
      - ubuzima-network-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v1/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    volumes:
      - backend_logs_prod:/app/logs
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Flutter Frontend
  frontend:
    build:
      context: ./frontend/ubuzima_app
      dockerfile: Dockerfile
      target: production
    image: ubuzima-frontend:production
    container_name: ubuzima-frontend-prod
    environment:
      API_BASE_URL: ${API_BASE_URL:-http://backend:8080/api/v1}
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - ubuzima-network-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: ubuzima-redis-prod
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    ports:
      - "6379:6379"
    networks:
      - ubuzima-network-prod
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD:-redis_password}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    volumes:
      - redis_data_prod:/data
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    container_name: ubuzima-nginx-prod
    ports:
      - "443:443"
      - "80:80"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs_prod:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - ubuzima-network-prod
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: ubuzima-prometheus-prod
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data_prod:/prometheus
    networks:
      - ubuzima-network-prod
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Grafana for dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: ubuzima-grafana-prod
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    volumes:
      - grafana_data_prod:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - ubuzima-network-prod
    restart: unless-stopped
    depends_on:
      - prometheus
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

volumes:
  postgres_data_prod:
    driver: local
  redis_data_prod:
    driver: local
  backend_logs_prod:
    driver: local
  nginx_logs_prod:
    driver: local
  prometheus_data_prod:
    driver: local
  grafana_data_prod:
    driver: local

networks:
  ubuzima-network-prod:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
