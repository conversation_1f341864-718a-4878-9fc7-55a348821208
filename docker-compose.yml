version: '3.8'

services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: ubuzima-db
    environment:
      POSTGRES_DB: ubuzima_db
      POSTGRES_USER: ubuzima_user
      POSTGRES_PASSWORD: ubuzima_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/main/resources/db/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - ubuzima-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ubuzima_user -d ubuzima_db"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Spring Boot Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ubuzima-backend
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DATABASE_URL: ******************************************
      DATABASE_USERNAME: ubuzima_user
      DATABASE_PASSWORD: ubuzima_password
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      CORS_ALLOWED_ORIGINS: http://localhost:3000,http://localhost:80
    ports:
      - "8080:8080"
    depends_on:
      database:
        condition: service_healthy
    networks:
      - ubuzima-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v1/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    volumes:
      - backend_logs:/app/logs

  # Flutter Frontend
  frontend:
    build:
      context: ./frontend/ubuzima_app
      dockerfile: Dockerfile
    container_name: ubuzima-frontend
    ports:
      - "80:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - ubuzima-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: ubuzima-redis
    ports:
      - "6379:6379"
    networks:
      - ubuzima-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    volumes:
      - redis_data:/data

  # Nginx Load Balancer (for production scaling)
  nginx:
    image: nginx:alpine
    container_name: ubuzima-nginx
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - ubuzima-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local

networks:
  ubuzima-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
