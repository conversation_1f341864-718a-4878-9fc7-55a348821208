# Development Environment Configuration for Ubuzima App

# API Configuration
API_BASE_URL=http://localhost:8080/api/v1
WS_BASE_URL=ws://localhost:8080/ws

# Database Configuration (for backend)
DATABASE_URL=postgresql://ubuzima_user:ubuzima_password@localhost:5432/ubuzima_dev
DATABASE_SSL=false

# Authentication
JWT_SECRET=development-jwt-secret-not-for-production
JWT_EXPIRATION=86400000

# External Services
GEMINI_API_KEY=your-development-gemini-api-key
GOOGLE_MAPS_API_KEY=your-development-google-maps-api-key

# Firebase Configuration
FIREBASE_PROJECT_ID=ubuzima-dev
FIREBASE_API_KEY=your-firebase-api-key
FIREBASE_AUTH_DOMAIN=ubuzima-dev.firebaseapp.com
FIREBASE_STORAGE_BUCKET=ubuzima-dev.appspot.com

# Email Service (Development)
SMTP_HOST=smtp.mailtrap.io
SMTP_PORT=2525
SMTP_USER=your-mailtrap-user
SMTP_PASS=your-mailtrap-pass

# SMS Service (Development - use test credentials)
TWILIO_ACCOUNT_SID=test-account-sid
TWILIO_AUTH_TOKEN=test-auth-token
TWILIO_PHONE_NUMBER=+***********

# File Storage (Development)
AWS_ACCESS_KEY_ID=minioadmin
AWS_SECRET_ACCESS_KEY=minioadmin
AWS_REGION=us-east-1
AWS_S3_BUCKET=ubuzima-files-dev
AWS_ENDPOINT=http://localhost:9000

# Monitoring (Development)
SENTRY_DSN=
GOOGLE_ANALYTICS_ID=

# Security (Development)
CORS_ALLOWED_ORIGINS=http://localhost:8080,http://localhost:3000,http://127.0.0.1:8080
RATE_LIMIT_REQUESTS_PER_MINUTE=1000

# Features
ENABLE_AI_FEATURES=true
ENABLE_VOICE_FEATURES=true
ENABLE_OFFLINE_MODE=true
ENABLE_ANALYTICS=false

# Deployment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG
