# Production Environment Configuration for Ubuzima App

# API Configuration
API_BASE_URL=https://api.ubuzima.rw/v1
WS_BASE_URL=wss://api.ubuzima.rw/ws

# Database Configuration (for backend)
DATABASE_URL=****************************************/ubuzima_prod
DATABASE_SSL=true

# Authentication
JWT_SECRET=your-production-jwt-secret-256-bits-minimum
JWT_EXPIRATION=********

# External Services
GEMINI_API_KEY=your-production-gemini-api-key
GOOGLE_MAPS_API_KEY=your-production-google-maps-api-key

# Firebase Configuration
FIREBASE_PROJECT_ID=ubuzima-prod
FIREBASE_API_KEY=your-firebase-api-key
FIREBASE_AUTH_DOMAIN=ubuzima-prod.firebaseapp.com
FIREBASE_STORAGE_BUCKET=ubuzima-prod.appspot.com

# Email Service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# SMS Service (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+************

# File Storage
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=ubuzima-files-prod

# Monitoring
SENTRY_DSN=your-sentry-dsn
GOOGLE_ANALYTICS_ID=your-ga-id

# Security
CORS_ALLOWED_ORIGINS=https://ubuzima.rw,https://www.ubuzima.rw
RATE_LIMIT_REQUESTS_PER_MINUTE=60

# Features
ENABLE_AI_FEATURES=true
ENABLE_VOICE_FEATURES=true
ENABLE_OFFLINE_MODE=true
ENABLE_ANALYTICS=true

# Deployment
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
