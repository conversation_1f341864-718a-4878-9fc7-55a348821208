# 🏗️ Complete Backend Setup Guide for Ubuzima App

## 📋 **Overview**

This guide will help you create a complete Spring Boot backend that integrates with your Flutter frontend and PostgreSQL database.

---

## 🚀 **Step 1: Create Spring Boot Project**

### **Using Spring Initializr:**
1. **Go to**: https://start.spring.io/
2. **Configure project**:
   - **Project**: Maven
   - **Language**: Java
   - **Spring Boot**: 3.2.0 (or latest stable)
   - **Group**: `com.ubuzima`
   - **Artifact**: `ubuzima-backend`
   - **Name**: `Ubuzima Backend`
   - **Package name**: `com.ubuzima.backend`
   - **Packaging**: Jar
   - **Java**: 17 or 21

3. **Add Dependencies**:
   - Spring Web
   - Spring Data JPA
   - PostgreSQL Driver
   - Spring Security
   - Spring Boot DevTools
   - Validation

4. **Generate and Download**

### **Project Structure:**
```
ubuzima-backend/
├── src/main/java/com/ubuzima/backend/
│   ├── UbuzimaBackendApplication.java
│   ├── config/
│   ├── controller/
│   ├── entity/
│   ├── repository/
│   ├── service/
│   └── dto/
├── src/main/resources/
│   ├── application.yml
│   └── data.sql
└── pom.xml
```

---

## 🗄️ **Step 2: Configure Database**

### **application.yml:**
```yaml
server:
  port: 8080

spring:
  application:
    name: ubuzima-backend
  
  datasource:
    url: *******************************************
    username: ubuzima_user
    password: your_password
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  security:
    cors:
      allowed-origins: "http://localhost:8080,http://localhost:3000"
      allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
      allowed-headers: "*"

logging:
  level:
    com.ubuzima.backend: DEBUG
    org.springframework.security: DEBUG
```

---

## 📊 **Step 3: Create Entity Classes**

### **User Entity:**
```java
@Entity
@Table(name = "users")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false)
    private String uuid;
    
    @Column(nullable = false)
    private String name;
    
    @Column(unique = true)
    private String email;
    
    private String phone;
    
    @Enumerated(EnumType.STRING)
    private UserRole role;
    
    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;
    
    private String gender;
    private String location;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors, getters, setters
}

enum UserRole {
    CLIENT, HEALTH_WORKER, ADMIN
}
```

### **HealthRecord Entity:**
```java
@Entity
@Table(name = "health_records")
public class HealthRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;
    
    @Column(name = "record_type", nullable = false)
    private String recordType;
    
    @Column(nullable = false)
    private LocalDateTime date;
    
    private Double weight;
    
    @Column(name = "blood_pressure_systolic")
    private Integer bloodPressureSystolic;
    
    @Column(name = "blood_pressure_diastolic")
    private Integer bloodPressureDiastolic;
    
    private Double temperature;
    private String notes;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    // Constructors, getters, setters
}
```

---

## 🔌 **Step 4: Create REST Controllers**

### **UserController:**
```java
@RestController
@RequestMapping("/api/users")
@CrossOrigin(origins = "*")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @GetMapping
    public ResponseEntity<List<User>> getAllUsers() {
        return ResponseEntity.ok(userService.getAllUsers());
    }
    
    @GetMapping("/{uuid}")
    public ResponseEntity<User> getUserByUuid(@PathVariable String uuid) {
        return userService.getUserByUuid(uuid)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }
    
    @PostMapping
    public ResponseEntity<User> createUser(@Valid @RequestBody User user) {
        User savedUser = userService.createUser(user);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedUser);
    }
    
    @PutMapping("/{uuid}")
    public ResponseEntity<User> updateUser(@PathVariable String uuid, @Valid @RequestBody User user) {
        return userService.updateUser(uuid, user)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }
    
    @DeleteMapping("/{uuid}")
    public ResponseEntity<Void> deleteUser(@PathVariable String uuid) {
        if (userService.deleteUser(uuid)) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }
}
```

### **HealthRecordController:**
```java
@RestController
@RequestMapping("/api/health-records")
@CrossOrigin(origins = "*")
public class HealthRecordController {
    
    @Autowired
    private HealthRecordService healthRecordService;
    
    @GetMapping("/user/{userUuid}")
    public ResponseEntity<List<HealthRecord>> getHealthRecords(
            @PathVariable String userUuid,
            @RequestParam(required = false) String recordType,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        List<HealthRecord> records = healthRecordService.getHealthRecords(userUuid, recordType, startDate, endDate);
        return ResponseEntity.ok(records);
    }
    
    @PostMapping
    public ResponseEntity<HealthRecord> createHealthRecord(@Valid @RequestBody HealthRecord record) {
        HealthRecord savedRecord = healthRecordService.createHealthRecord(record);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedRecord);
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<HealthRecord> updateHealthRecord(@PathVariable Long id, @Valid @RequestBody HealthRecord record) {
        return healthRecordService.updateHealthRecord(id, record)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteHealthRecord(@PathVariable Long id) {
        if (healthRecordService.deleteHealthRecord(id)) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }
}
```

---

## 🔧 **Step 5: Update Frontend Configuration**

### **Update Backend Sync Service:**
In your Flutter app, update the backend URL:

```dart
// In backend_sync_service_simple.dart
class BackendSyncService {
  static const String baseUrl = 'http://localhost:8080/api';
  
  Future<void> syncHealthRecords() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/health-records/user/$userUuid'),
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        // Process health records
      }
    } catch (e) {
      debugPrint('Health records sync failed: $e');
    }
  }
}
```

---

## 🚀 **Step 6: Run Complete Setup**

### **1. Start PostgreSQL Database**
```bash
# If using local PostgreSQL
sudo service postgresql start

# Or start PostgreSQL service on Windows
```

### **2. Start Spring Boot Backend**
```bash
cd ubuzima-backend
./mvnw spring-boot:run
```

### **3. Start Flutter Frontend**
```bash
cd frontend/ubuzima_app
flutter run -d chrome
```

### **4. Test Integration**
1. **Backend API**: http://localhost:8080/api/users
2. **Frontend**: http://localhost:8080 (Flutter web)
3. **Database**: Check pgAdmin or your database tool

---

## 🧪 **Step 7: Test Complete Flow**

### **Test Endpoints:**
```bash
# Get all users
curl -X GET http://localhost:8080/api/users

# Create user
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "uuid": "test-uuid-123",
    "name": "Test User",
    "email": "<EMAIL>",
    "role": "CLIENT"
  }'

# Get health records
curl -X GET http://localhost:8080/api/health-records/user/test-uuid-123
```

---

## 🎯 **Benefits of Full Backend Integration**

### **Security:**
- ✅ **API authentication** and authorization
- ✅ **Data validation** on server side
- ✅ **SQL injection protection**
- ✅ **CORS configuration**

### **Scalability:**
- ✅ **Horizontal scaling** of backend services
- ✅ **Database connection pooling**
- ✅ **Caching strategies**
- ✅ **Load balancing** support

### **Features:**
- ✅ **Real-time data sync**
- ✅ **Advanced querying** and filtering
- ✅ **File upload** handling
- ✅ **Email notifications**
- ✅ **Audit logging**

---

## 🎓 **For University Presentation**

**Highlight These Achievements:**
- ✅ **Full-stack development** (Flutter + Spring Boot + PostgreSQL)
- ✅ **RESTful API design** with proper HTTP methods
- ✅ **Database relationships** and JPA mapping
- ✅ **Cross-origin resource sharing** (CORS) configuration
- ✅ **Production-ready architecture** with proper separation of concerns

**This demonstrates advanced software engineering skills and real-world application development!** 🚀✨
