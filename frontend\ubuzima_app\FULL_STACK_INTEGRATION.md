# 🚀 Complete Full-Stack Integration Guide

## 🎯 **Your Current Setup**
- ✅ **Flutter Frontend** - Complete and functional
- ✅ **Spring Boot Backend** - Ready for integration  
- ✅ **PostgreSQL Database** - Professional database choice

## 🏗️ **Integration Architecture**
```
Flutter Frontend (Port 8080) ↔ Spring Boot API (Port 8080) ↔ PostgreSQL (Port 5432)
```

---

## 📋 **Step 1: Configure Your Spring Boot Backend**

### **A. Update application.yml**
In your Spring Boot project, update `src/main/resources/application.yml`:

```yaml
server:
  port: 8080

spring:
  application:
    name: ubuzima-backend
  
  datasource:
    url: *******************************************
    username: your_postgres_username
    password: your_postgres_password
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  web:
    cors:
      allowed-origins: 
        - "http://localhost:8080"
        - "http://localhost:3000"
        - "http://127.0.0.1:8080"
      allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
      allowed-headers: "*"
      allow-credentials: true

logging:
  level:
    com.ubuzima: DEBUG
    org.springframework.web: DEBUG
```

### **B. Add Essential Dependencies**
In your `pom.xml`, ensure you have:

```xml
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <scope>runtime</scope>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
</dependencies>
```

### **C. Create Health Check Endpoint**
Create `src/main/java/com/ubuzima/backend/controller/HealthController.java`:

```java
@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*")
public class HealthController {
    
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Ubuzima Backend");
        response.put("timestamp", LocalDateTime.now().toString());
        return ResponseEntity.ok(response);
    }
}
```

---

## 🗄️ **Step 2: Create Your Database Entities**

### **A. User Entity**
Create `src/main/java/com/ubuzima/backend/entity/User.java`:

```java
@Entity
@Table(name = "users")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false)
    private String uuid;
    
    @Column(nullable = false)
    private String name;
    
    @Column(unique = true)
    private String email;
    
    private String phone;
    
    @Enumerated(EnumType.STRING)
    private UserRole role;
    
    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;
    
    private String gender;
    private String location;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Constructors, getters, setters
}

enum UserRole {
    CLIENT, HEALTH_WORKER, ADMIN
}
```

### **B. HealthRecord Entity**
Create `src/main/java/com/ubuzima/backend/entity/HealthRecord.java`:

```java
@Entity
@Table(name = "health_records")
public class HealthRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;
    
    @Column(name = "record_type", nullable = false)
    private String recordType;
    
    @Column(nullable = false)
    private LocalDateTime date;
    
    private Double weight;
    
    @Column(name = "blood_pressure_systolic")
    private Integer bloodPressureSystolic;
    
    @Column(name = "blood_pressure_diastolic")
    private Integer bloodPressureDiastolic;
    
    private Double temperature;
    private String notes;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    // Constructors, getters, setters
}
```

---

## 🔌 **Step 3: Create REST Controllers**

### **A. UserController**
Create `src/main/java/com/ubuzima/backend/controller/UserController.java`:

```java
@RestController
@RequestMapping("/api/users")
@CrossOrigin(origins = "*")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @GetMapping
    public ResponseEntity<List<User>> getAllUsers() {
        return ResponseEntity.ok(userService.getAllUsers());
    }
    
    @GetMapping("/{uuid}")
    public ResponseEntity<User> getUserByUuid(@PathVariable String uuid) {
        return userService.getUserByUuid(uuid)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }
    
    @PostMapping
    public ResponseEntity<User> createUser(@Valid @RequestBody User user) {
        User savedUser = userService.createUser(user);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedUser);
    }
}
```

### **B. HealthRecordController**
Create `src/main/java/com/ubuzima/backend/controller/HealthRecordController.java`:

```java
@RestController
@RequestMapping("/api/health-records")
@CrossOrigin(origins = "*")
public class HealthRecordController {
    
    @Autowired
    private HealthRecordService healthRecordService;
    
    @GetMapping
    public ResponseEntity<List<HealthRecord>> getAllHealthRecords() {
        return ResponseEntity.ok(healthRecordService.getAllHealthRecords());
    }
    
    @GetMapping("/user/{userUuid}")
    public ResponseEntity<List<HealthRecord>> getHealthRecordsByUser(@PathVariable String userUuid) {
        return ResponseEntity.ok(healthRecordService.getHealthRecordsByUser(userUuid));
    }
    
    @PostMapping
    public ResponseEntity<HealthRecord> createHealthRecord(@Valid @RequestBody HealthRecord record) {
        HealthRecord savedRecord = healthRecordService.createHealthRecord(record);
        return ResponseEntity.status(HttpStatus.CREATED).body(savedRecord);
    }
}
```

---

## 🚀 **Step 4: Test Your Integration**

### **A. Start Your Services**

1. **Start PostgreSQL Database**:
   ```bash
   # Windows
   net start postgresql-x64-14
   
   # Linux/Mac
   sudo service postgresql start
   ```

2. **Start Spring Boot Backend**:
   ```bash
   cd your-backend-project
   ./mvnw spring-boot:run
   ```

3. **Start Flutter Frontend**:
   ```bash
   cd frontend/ubuzima_app
   flutter run -d chrome
   ```

### **B. Test API Endpoints**

Open your browser or use curl to test:

```bash
# Test health endpoint
curl http://localhost:8080/api/health

# Test users endpoint
curl http://localhost:8080/api/users

# Test health records endpoint
curl http://localhost:8080/api/health-records
```

### **C. Test Frontend Integration**

1. **Open your Ubuzima app** in browser
2. **Go to**: Settings → Advanced Settings → Backend Test
3. **Run tests** - should show successful API connections
4. **Check browser console** for API call logs

---

## 🎯 **Step 5: Verify Complete Integration**

### **✅ What Should Work:**
- **Frontend loads** successfully
- **Backend API** responds to health checks
- **Database tables** are created automatically
- **API calls** from frontend reach backend
- **Data persistence** in PostgreSQL

### **🔍 Troubleshooting:**

**If Frontend Can't Reach Backend:**
- Check CORS configuration
- Verify backend is running on port 8080
- Check browser console for errors

**If Backend Can't Connect to Database:**
- Verify PostgreSQL is running
- Check database credentials in application.yml
- Ensure database `ubuzima_db` exists

**If API Calls Fail:**
- Check network tab in browser dev tools
- Verify API endpoints are correct
- Check backend logs for errors

---

## 🎓 **University Presentation Points**

**Highlight These Achievements:**
- ✅ **Complete Full-Stack Architecture** (Flutter + Spring Boot + PostgreSQL)
- ✅ **RESTful API Design** with proper HTTP methods
- ✅ **Database Integration** with JPA/Hibernate
- ✅ **Cross-Origin Resource Sharing** (CORS) configuration
- ✅ **Real-Time Data Sync** between frontend and backend
- ✅ **Production-Ready Architecture** with proper separation of concerns

**Technical Skills Demonstrated:**
- Modern web development stack
- API design and implementation
- Database modeling and relationships
- Frontend-backend communication
- Error handling and validation
- Professional development practices

---

## 🚀 **Next Steps**

1. **Complete the backend controllers** for all entities
2. **Add authentication** and security
3. **Implement data validation** on both ends
4. **Add comprehensive error handling**
5. **Create API documentation** with Swagger
6. **Add unit and integration tests**

**Your full-stack Ubuzima application is now ready to impress your professors and demonstrate real-world software engineering skills!** 🎉✨
