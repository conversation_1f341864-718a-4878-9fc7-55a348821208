<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="io.agora.infra:aosl:1.2.13.1" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eeeb962f535d32e0ec3f7f635a5827fc\transformed\jetified-aosl-1.2.13.1\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eeeb962f535d32e0ec3f7f635a5827fc\transformed\jetified-aosl-1.2.13.1\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-av1-codec-dec:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60bdd0c524da9ef4f53812860ec1bbd2\transformed\jetified-full-video-av1-codec-dec-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60bdd0c524da9ef4f53812860ec1bbd2\transformed\jetified-full-video-av1-codec-dec-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-av1-codec-enc:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e57e6bbc9f964cd945349386164e36cd\transformed\jetified-full-video-av1-codec-enc-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e57e6bbc9f964cd945349386164e36cd\transformed\jetified-full-video-av1-codec-enc-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-codec-dec:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83a31f91f536d48aeb965cff537d299e\transformed\jetified-full-video-codec-dec-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83a31f91f536d48aeb965cff537d299e\transformed\jetified-full-video-codec-dec-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-video-codec-enc:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21730f7af1e36316ece9dd940c740afa\transformed\jetified-full-video-codec-enc-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21730f7af1e36316ece9dd940c740afa\transformed\jetified-full-video-codec-enc-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-voice-drive:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7baeeea09a2e73662c3231511cacfc66\transformed\jetified-full-voice-drive-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7baeeea09a2e73662c3231511cacfc66\transformed\jetified-full-voice-drive-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-face-capture:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f72001cf10ee074e171931a733be920c\transformed\jetified-full-face-capture-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f72001cf10ee074e171931a733be920c\transformed\jetified-full-face-capture-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-face-detect:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de94cc1474313ee38e7a6a14ee64b42d\transformed\jetified-full-face-detect-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de94cc1474313ee38e7a6a14ee64b42d\transformed\jetified-full-face-detect-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-vqa:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d978ee2e5c6f5f84aa300827a90f7b2\transformed\jetified-full-vqa-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d978ee2e5c6f5f84aa300827a90f7b2\transformed\jetified-full-vqa-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:aiaec-ll:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af2b953e65d4d5b6f78997f87ade4780\transformed\jetified-aiaec-ll-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af2b953e65d4d5b6f78997f87ade4780\transformed\jetified-aiaec-ll-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:aiaec:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\664675af4ddff7261540588a1e631046\transformed\jetified-aiaec-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\664675af4ddff7261540588a1e631046\transformed\jetified-aiaec-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:spatial-audio:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb54ae6afdda5bfeb497f2460c4f3450\transformed\jetified-spatial-audio-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb54ae6afdda5bfeb497f2460c4f3450\transformed\jetified-spatial-audio-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-virtual-background:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3129c0aa65d072a5e9a8b89b905c4753\transformed\jetified-full-virtual-background-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3129c0aa65d072a5e9a8b89b905c4753\transformed\jetified-full-virtual-background-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:screen-capture:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\921c96f2a54cc2a9e4fa6d2f0e139fb0\transformed\jetified-screen-capture-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\921c96f2a54cc2a9e4fa6d2f0e139fb0\transformed\jetified-screen-capture-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-content-inspect:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\430b5068a09980e3328146acfc7d213a\transformed\jetified-full-content-inspect-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\430b5068a09980e3328146acfc7d213a\transformed\jetified-full-content-inspect-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:clear-vision:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4de41ffaf0f1fac276576a3383ae26e\transformed\jetified-clear-vision-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4de41ffaf0f1fac276576a3383ae26e\transformed\jetified-clear-vision-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:audio-beauty:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40920ab0d4f64d7ad8486f25ea2c4936\transformed\jetified-audio-beauty-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40920ab0d4f64d7ad8486f25ea2c4936\transformed\jetified-audio-beauty-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:ains-ll:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2311eb0ffb201204940a220fa821ff22\transformed\jetified-ains-ll-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2311eb0ffb201204940a220fa821ff22\transformed\jetified-ains-ll-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:ains:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf53be273b760b5a4d925173ff0f1143\transformed\jetified-ains-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf53be273b760b5a4d925173ff0f1143\transformed\jetified-ains-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-rtc-basic:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aceae36db7ceb700b56febe67a88552b\transformed\jetified-full-rtc-basic-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aceae36db7ceb700b56febe67a88552b\transformed\jetified-full-rtc-basic-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:full-sdk:4.5.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b566b144f24aaecc40f84aae65d82b2e\transformed\jetified-full-sdk-4.5.2\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b566b144f24aaecc40f84aae65d82b2e\transformed\jetified-full-sdk-4.5.2\assets\PLACEHOLDER"/></source></dataSet><dataSet config="io.agora.rtc:iris-rtc:4.5.2-build.1" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8025b73987cc94b15b57f0db06b908e\transformed\jetified-iris-rtc-4.5.2-build.1\assets"><file name="PLACEHOLDER" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8025b73987cc94b15b57f0db06b908e\transformed\jetified-iris-rtc-4.5.2-build.1\assets\PLACEHOLDER"/></source></dataSet><dataSet config=":video_player_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\video_player_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":url_launcher_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\url_launcher_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":sqflite_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\sqflite_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":share_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":printing" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":permission_handler_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\permission_handler_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\path_provider_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":package_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\package_info_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":iris_method_channel" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\iris_method_channel\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":integration_test" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\integration_test\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":geocoding_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\geocoding_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\flutter_plugin_android_lifecycle\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":local_auth_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\local_auth_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":image_picker_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":google_maps_flutter_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\google_maps_flutter_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\firebase_core\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_messaging" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":device_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\device_info_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":connectivity_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\connectivity_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":camera_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\camera_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":agora_rtc_engine" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":geolocator_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":audioplayers_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\audioplayers_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\shared_preferences_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":location" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_tts" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\flutter_tts\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\WEB\develop\frontend\ubuzima_app\build\app\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>