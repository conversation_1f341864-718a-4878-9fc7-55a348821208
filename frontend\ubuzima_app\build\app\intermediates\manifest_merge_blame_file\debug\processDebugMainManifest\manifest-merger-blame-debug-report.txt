1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="rw.health.ubuzima.ubuzima_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:6:5-67
15-->C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:6:22-64
16    <!-- Location permissions for Google Maps -->
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:4:5-79
17-->C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:4:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
18-->C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:5:5-81
18-->C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:5:22-78
19    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
19-->C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:7:5-79
19-->C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:7:22-76
20    <!--
21 Required to query activities that can process text, see:
22         https://developer.android.com/training/package-visibility and
23         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
24
25         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
26    -->
27    <queries>
27-->C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:52:5-57:15
28        <intent>
28-->C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:53:9-56:18
29            <action android:name="android.intent.action.PROCESS_TEXT" />
29-->C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:54:13-72
29-->C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:54:21-70
30
31            <data android:mimeType="text/plain" />
31-->C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:55:13-50
31-->C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:55:19-48
32        </intent>
33        <!-- Needs to be explicitly declared on Android R+ -->
34        <package android:name="com.google.android.apps.maps" />
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
35    </queries>
36
37    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
37-->[:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-75
37-->[:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-72
38    <uses-permission android:name="android.permission.RECORD_AUDIO" />
38-->[:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-71
38-->[:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:22-68
39    <uses-permission android:name="android.permission.CAMERA" />
39-->[:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-65
39-->[:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-62
40    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
40-->[:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-80
40-->[:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:22-77
41    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- The Agora SDK requires Bluetooth permissions in case users are using Bluetooth devices. -->
41-->[:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-76
41-->[:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:22-73
42    <uses-permission android:name="android.permission.BLUETOOTH" /> <!-- For Android 12 and above devices, the following permission is also required. -->
42-->[:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-68
42-->[:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:22-65
43    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
43-->[:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:5-76
43-->[:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:22-73
44    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Permissions options for the `notification` group -->
44-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
44-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
45    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
45-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
45-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-74
46    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- Required by older versions of Google Play services to create IID tokens -->
46-->[:local_auth_android] C:\WEB\develop\frontend\ubuzima_app\build\local_auth_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-72
46-->[:local_auth_android] C:\WEB\develop\frontend\ubuzima_app\build\local_auth_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-69
47    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- suppress DeprecatedClassUsageInspection -->
47-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:5-82
47-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:22-79
48    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
48-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd1be39a4121b41df3cecdb1f1acb24d\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
48-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd1be39a4121b41df3cecdb1f1acb24d\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
49
50    <uses-feature
50-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
51        android:glEsVersion="0x00020000"
51-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
52        android:required="true" />
52-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
53
54    <permission
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
55        android:name="rw.health.ubuzima.ubuzima_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
56        android:protectionLevel="signature" />
56-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
57
58    <uses-permission android:name="rw.health.ubuzima.ubuzima_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
58-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
58-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
59    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
59-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:7:5-77
59-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:7:22-74
60    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
60-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:8:5-94
60-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:8:22-91
61
62    <application
63        android:name="android.app.Application"
64        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
64-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
65        android:debuggable="true"
66        android:extractNativeLibs="true"
67        android:icon="@mipmap/ic_launcher"
68        android:label="Ubuzima" >
69
70        <!-- Google Maps API Key -->
71        <meta-data
72            android:name="com.google.android.geo.API_KEY"
73            android:value="AIzaSyB41DRUbKWJHPxaFjMAwdrzWzbVKartNGg" />
74
75        <activity
76            android:name="rw.health.ubuzima.ubuzima_app.MainActivity"
77            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
78            android:exported="true"
79            android:hardwareAccelerated="true"
80            android:launchMode="singleTop"
81            android:taskAffinity=""
82            android:theme="@style/LaunchTheme"
83            android:windowSoftInputMode="adjustResize" >
84
85            <!--
86                 Specifies an Android theme to apply to this Activity as soon as
87                 the Android process has started. This theme is visible to the user
88                 while the Flutter UI initializes. After that, this theme continues
89                 to determine the Window background behind the Flutter UI.
90            -->
91            <meta-data
92                android:name="io.flutter.embedding.android.NormalTheme"
93                android:resource="@style/NormalTheme" />
94
95            <intent-filter>
96                <action android:name="android.intent.action.MAIN" />
97
98                <category android:name="android.intent.category.LAUNCHER" />
99            </intent-filter>
100        </activity>
101        <!--
102             Don't delete the meta-data below.
103             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
104        -->
105        <meta-data
106            android:name="flutterEmbedding"
107            android:value="2" />
108
109        <service
109-->[:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-15:56
110            android:name="com.lyokone.location.FlutterLocationService"
110-->[:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-71
111            android:enabled="true"
111-->[:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-35
112            android:exported="false"
112-->[:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
113            android:foregroundServiceType="location" />
113-->[:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-53
114        <service
114-->[:geolocator_android] C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
115            android:name="com.baseflow.geolocator.GeolocatorLocationService"
115-->[:geolocator_android] C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
116            android:enabled="true"
116-->[:geolocator_android] C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
117            android:exported="false"
117-->[:geolocator_android] C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
118            android:foregroundServiceType="location" />
118-->[:geolocator_android] C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
119        <service
119-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
120            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
120-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
121            android:exported="false"
121-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
122            android:permission="android.permission.BIND_JOB_SERVICE" />
122-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
123        <service
123-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
124            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
124-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
125            android:exported="false" >
125-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
126            <intent-filter>
126-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
127                <action android:name="com.google.firebase.MESSAGING_EVENT" />
127-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
127-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
128            </intent-filter>
129        </service>
130
131        <receiver
131-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
132            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
132-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
133            android:exported="true"
133-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
134            android:permission="com.google.android.c2dm.permission.SEND" >
134-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
135            <intent-filter>
135-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
136                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
136-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
136-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
137            </intent-filter>
138        </receiver>
139
140        <service
140-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
141            android:name="com.google.firebase.components.ComponentDiscoveryService"
141-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
142            android:directBootAware="true"
142-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:32:13-43
143            android:exported="false" >
143-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:31:13-37
144            <meta-data
144-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
145                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
145-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
146                android:value="com.google.firebase.components.ComponentRegistrar" />
146-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
147            <meta-data
147-->[:firebase_core] C:\WEB\develop\frontend\ubuzima_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
148                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
148-->[:firebase_core] C:\WEB\develop\frontend\ubuzima_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
149                android:value="com.google.firebase.components.ComponentRegistrar" />
149-->[:firebase_core] C:\WEB\develop\frontend\ubuzima_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
150            <meta-data
150-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
151                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
151-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
152                android:value="com.google.firebase.components.ComponentRegistrar" />
152-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
153            <meta-data
153-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
154                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
154-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
155                android:value="com.google.firebase.components.ComponentRegistrar" />
155-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
156            <meta-data
156-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
157                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
157-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
158                android:value="com.google.firebase.components.ComponentRegistrar" />
158-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
159            <meta-data
159-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
160                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
160-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
161                android:value="com.google.firebase.components.ComponentRegistrar" />
161-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
162            <meta-data
162-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
163                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
163-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
164                android:value="com.google.firebase.components.ComponentRegistrar" />
164-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
165            <meta-data
165-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\627f757bcd95f222224618b867985469\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
166                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
166-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\627f757bcd95f222224618b867985469\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
167                android:value="com.google.firebase.components.ComponentRegistrar" />
167-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\627f757bcd95f222224618b867985469\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
168            <meta-data
168-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
169                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
169-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:36:17-109
170                android:value="com.google.firebase.components.ComponentRegistrar" />
170-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:37:17-82
171            <meta-data
171-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
172                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
172-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
173                android:value="com.google.firebase.components.ComponentRegistrar" />
173-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
174        </service>
175
176        <provider
176-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
177            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
177-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
178            android:authorities="rw.health.ubuzima.ubuzima_app.flutterfirebasemessaginginitprovider"
178-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
179            android:exported="false"
179-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
180            android:initOrder="99" />
180-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
181        <provider
181-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
182            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
182-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
183            android:authorities="rw.health.ubuzima.ubuzima_app.flutter.image_provider"
183-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
184            android:exported="false"
184-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
185            android:grantUriPermissions="true" >
185-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
186            <meta-data
186-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
187                android:name="android.support.FILE_PROVIDER_PATHS"
187-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
188                android:resource="@xml/flutter_image_picker_file_paths" />
188-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
189        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
190        <service
190-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
191            android:name="com.google.android.gms.metadata.ModuleDependencies"
191-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
192            android:enabled="false"
192-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
193            android:exported="false" >
193-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
194            <intent-filter>
194-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
195                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
195-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
195-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
196            </intent-filter>
197
198            <meta-data
198-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
199                android:name="photopicker_activity:0:required"
199-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
200                android:value="" />
200-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
201        </service>
202
203        <provider
203-->[:printing] C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
204            android:name="net.nfet.flutter.printing.PrintFileProvider"
204-->[:printing] C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-71
205            android:authorities="rw.health.ubuzima.ubuzima_app.flutter.printing"
205-->[:printing] C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-68
206            android:exported="false"
206-->[:printing] C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
207            android:grantUriPermissions="true" >
207-->[:printing] C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
208            <meta-data
208-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
209                android:name="android.support.FILE_PROVIDER_PATHS"
209-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
210                android:resource="@xml/flutter_printing_file_paths" />
210-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
211        </provider>
212        <!--
213           Declares a provider which allows us to store files to share in
214           '.../caches/share_plus' and grant the receiving action access
215        -->
216        <provider
216-->[:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
217            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
217-->[:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
218            android:authorities="rw.health.ubuzima.ubuzima_app.flutter.share_provider"
218-->[:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
219            android:exported="false"
219-->[:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
220            android:grantUriPermissions="true" >
220-->[:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
221            <meta-data
221-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
222                android:name="android.support.FILE_PROVIDER_PATHS"
222-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
223                android:resource="@xml/flutter_share_file_paths" />
223-->[:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
224        </provider>
225        <!--
226           This manifest declared broadcast receiver allows us to use an explicit
227           Intent when creating a PendingItent to be informed of the user's choice
228        -->
229        <receiver
229-->[:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
230            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
230-->[:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
231            android:exported="false" >
231-->[:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
232            <intent-filter>
232-->[:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
233                <action android:name="EXTRA_CHOSEN_COMPONENT" />
233-->[:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
233-->[:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
234            </intent-filter>
235        </receiver>
236
237        <activity
237-->[:url_launcher_android] C:\WEB\develop\frontend\ubuzima_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
238            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
238-->[:url_launcher_android] C:\WEB\develop\frontend\ubuzima_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
239            android:exported="false"
239-->[:url_launcher_android] C:\WEB\develop\frontend\ubuzima_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
240            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- Needs to be explicitly declared on P+ -->
240-->[:url_launcher_android] C:\WEB\develop\frontend\ubuzima_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
241        <uses-library
241-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
242            android:name="org.apache.http.legacy"
242-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
243            android:required="false" />
243-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
244
245        <receiver
245-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
246            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
246-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
247            android:exported="true"
247-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
248            android:permission="com.google.android.c2dm.permission.SEND" >
248-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
249            <intent-filter>
249-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
250                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
250-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
250-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
251            </intent-filter>
252
253            <meta-data
253-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
254                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
254-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
255                android:value="true" />
255-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
256        </receiver>
257        <!--
258             FirebaseMessagingService performs security checks at runtime,
259             but set to not exported to explicitly avoid allowing another app to call it.
260        -->
261        <service
261-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
262            android:name="com.google.firebase.messaging.FirebaseMessagingService"
262-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
263            android:directBootAware="true"
263-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
264            android:exported="false" >
264-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
265            <intent-filter android:priority="-500" >
265-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
266                <action android:name="com.google.firebase.MESSAGING_EVENT" />
266-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
266-->[:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
267            </intent-filter>
268        </service>
269
270        <provider
270-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
271            android:name="com.google.firebase.provider.FirebaseInitProvider"
271-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:24:13-77
272            android:authorities="rw.health.ubuzima.ubuzima_app.firebaseinitprovider"
272-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:25:13-72
273            android:directBootAware="true"
273-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:26:13-43
274            android:exported="false"
274-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:27:13-37
275            android:initOrder="100" />
275-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:28:13-36
276
277        <activity
277-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
278            android:name="com.google.android.gms.common.api.GoogleApiActivity"
278-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
279            android:exported="false"
279-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
280            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
280-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
281
282        <uses-library
282-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
283            android:name="androidx.window.extensions"
283-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
284            android:required="false" />
284-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
285        <uses-library
285-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
286            android:name="androidx.window.sidecar"
286-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
287            android:required="false" />
287-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
288
289        <meta-data
289-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abeed4f47a72eff8f5b4e9bf7f2f3c91\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
290            android:name="com.google.android.gms.version"
290-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abeed4f47a72eff8f5b4e9bf7f2f3c91\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
291            android:value="@integer/google_play_services_version" />
291-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abeed4f47a72eff8f5b4e9bf7f2f3c91\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
292
293        <provider
293-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
294            android:name="androidx.startup.InitializationProvider"
294-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
295            android:authorities="rw.health.ubuzima.ubuzima_app.androidx-startup"
295-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
296            android:exported="false" >
296-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
297            <meta-data
297-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
298                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
298-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
299                android:value="androidx.startup" />
299-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
300            <meta-data
300-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
301                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
301-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
302                android:value="androidx.startup" />
302-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
303        </provider>
304
305        <receiver
305-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
306            android:name="androidx.profileinstaller.ProfileInstallReceiver"
306-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
307            android:directBootAware="false"
307-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
308            android:enabled="true"
308-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
309            android:exported="true"
309-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
310            android:permission="android.permission.DUMP" >
310-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
311            <intent-filter>
311-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
312                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
312-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
312-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
313            </intent-filter>
314            <intent-filter>
314-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
315                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
315-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
315-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
316            </intent-filter>
317            <intent-filter>
317-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
318                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
318-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
318-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
319            </intent-filter>
320            <intent-filter>
320-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
321                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
321-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
321-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
322            </intent-filter>
323        </receiver>
324
325        <service
325-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
326            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
326-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
327            android:exported="false" >
327-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
328            <meta-data
328-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
329                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
329-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
330                android:value="cct" />
330-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
331        </service>
332        <service
332-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
333            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
333-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
334            android:exported="false"
334-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
335            android:permission="android.permission.BIND_JOB_SERVICE" >
335-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
336        </service>
337
338        <receiver
338-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
339            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
339-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
340            android:exported="false" />
340-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
341
342        <activity
342-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:11:9-14:63
343            android:name="io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenCaptureAssistantActivity"
343-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:11:19-89
344            android:configChanges="screenSize|orientation"
344-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:12:13-59
345            android:screenOrientation="unspecified"
345-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:13:13-52
346            android:theme="@android:style/Theme.Translucent" />
346-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:14:13-61
347
348        <service
348-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:16:9-19:19
349            android:name="io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenSharingService"
349-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:17:13-73
350            android:foregroundServiceType="mediaProjection" >
350-->[io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:18:13-60
351        </service>
352    </application>
353
354</manifest>
