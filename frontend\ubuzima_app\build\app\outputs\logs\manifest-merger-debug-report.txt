-- Merging decision tree log ---
application
INJECTED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:9:5-46:19
INJECTED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-16:19
MERGED from [:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-16:19
MERGED from [:geolocator_android] C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:geolocator_android] C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3336a24e8ef5d3d66fff7f5ca80854\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3336a24e8ef5d3d66fff7f5ca80854\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_core] C:\WEB\develop\frontend\ubuzima_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\WEB\develop\frontend\ubuzima_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:printing] C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:printing] C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-33:19
MERGED from [:url_launcher_android] C:\WEB\develop\frontend\ubuzima_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\WEB\develop\frontend\ubuzima_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\627f757bcd95f222224618b867985469\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\627f757bcd95f222224618b867985469\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13d0c2544ad01fcdb01c4e62d7089728\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13d0c2544ad01fcdb01c4e62d7089728\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b426ae45ba34699527b81c12c814bb78\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b426ae45ba34699527b81c12c814bb78\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b180219ad72ca48e76b8a5127003ab7\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b180219ad72ca48e76b8a5127003ab7\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dbfa2b099ab663030b8159eebacd12\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dbfa2b099ab663030b8159eebacd12\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abeed4f47a72eff8f5b4e9bf7f2f3c91\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abeed4f47a72eff8f5b4e9bf7f2f3c91\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d9d3675465ff69d847e2f781f20c61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d9d3675465ff69d847e2f781f20c61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92a2169b5b2674c8bbe10e1b1d80da0f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92a2169b5b2674c8bbe10e1b1d80da0f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:10:5-20:19
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:10:5-20:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a63b21759398e83092b56262a073cdf\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a63b21759398e83092b56262a073cdf\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
	android:extractNativeLibs
		INJECTED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:1:1-58:12
MERGED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:1:1-58:12
INJECTED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:flutter_tts] C:\WEB\develop\frontend\ubuzima_app\build\flutter_tts\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-18:12
MERGED from [:shared_preferences_android] C:\WEB\develop\frontend\ubuzima_app\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:audioplayers_android] C:\WEB\develop\frontend\ubuzima_app\build\audioplayers_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:geolocator_android] C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3336a24e8ef5d3d66fff7f5ca80854\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-21:12
MERGED from [:camera_android] C:\WEB\develop\frontend\ubuzima_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:connectivity_plus] C:\WEB\develop\frontend\ubuzima_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:device_info_plus] C:\WEB\develop\frontend\ubuzima_app\build\device_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-48:12
MERGED from [:firebase_core] C:\WEB\develop\frontend\ubuzima_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:google_maps_flutter_android] C:\WEB\develop\frontend\ubuzima_app\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:local_auth_android] C:\WEB\develop\frontend\ubuzima_app\build\local_auth_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_plugin_android_lifecycle] C:\WEB\develop\frontend\ubuzima_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:geocoding_android] C:\WEB\develop\frontend\ubuzima_app\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:integration_test] C:\WEB\develop\frontend\ubuzima_app\build\integration_test\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:1-12:12
MERGED from [:iris_method_channel] C:\WEB\develop\frontend\ubuzima_app\build\iris_method_channel\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:package_info_plus] C:\WEB\develop\frontend\ubuzima_app\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] C:\WEB\develop\frontend\ubuzima_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] C:\WEB\develop\frontend\ubuzima_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:printing] C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-35:12
MERGED from [:sqflite_android] C:\WEB\develop\frontend\ubuzima_app\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] C:\WEB\develop\frontend\ubuzima_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:video_player_android] C:\WEB\develop\frontend\ubuzima_app\build\video_player_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:17:1-38:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd1be39a4121b41df3cecdb1f1acb24d\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\627f757bcd95f222224618b867985469\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13d0c2544ad01fcdb01c4e62d7089728\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb6a7e0963abf3e29d747a5c9e4e78fe\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\277aa05e63c0582e9a6c0fb9811696d7\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0098a6e93522fecc805d8900172003dc\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75e1bcd7a8b61b1e132d50e7766bfd37\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb98276ae8bdd946a2259a69bef27bdf\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74ed8ceaa55f9ae57c7b49acd30346c2\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1f85211ae6978d0218d51595a7f7e62\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba8599b6014e514325b6a37b5077642\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02cddce53ae6e54afa639a8fcdb8b322\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7cbd00988e28f46cac66dd959ae6532\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b426ae45ba34699527b81c12c814bb78\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d6ee565ab495ccfff8140c2f79bcdec\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6334635279cb43af07c2144a95c2be9\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e4c2e4e231b6a1dd462fb9cdc4ff977\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5cf1d9716b9650d5a0a992711d623bf8\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1da3d52bf544e2149dd46ce2061a20cf\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1682fe25d19efcf6ed57ec9bfdc01f5\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebeb29d7113d60e59d5e0acf92f847f3\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7156a54e8f0502a1f8f57797d8b3e6e\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b91be3af319ede480d7185430690ee1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2e21678784b3eef6b33f576b1691a56\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0eede8414d1ee0e5f236daeb1a2e59a\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b180219ad72ca48e76b8a5127003ab7\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dbfa2b099ab663030b8159eebacd12\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abeed4f47a72eff8f5b4e9bf7f2f3c91\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21f508358b7ef6793f647ddb068091fd\transformed\appcompat-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55520e4df2220e27f13f0bbb7467d11a\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a70ddd560199940b45ffc1a1c4db7f79\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\308a2e77faa557d0bd706972416bde6a\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e7454f640b41d9fb1f89a6b912b741e\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\458a1a3589d1e39abd86072805af9a32\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcc302a7b606f68719f42912b7ef31a0\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a93664941e2b88c03441606ac08a6fc2\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8df7439c67c18e2b650336c112408c6\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f287878c588d535c6238ddf78c37b2ed\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e45546448fd971172ffd9d216a54d834\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09fc8eeb7b5bae391cca11b3fcf42aca\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d53732e8ebd4dda2c45fb0959ea7441\transformed\jetified-media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d54be51abe8aaa49b41f4a3e99db2aa4\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab1dc0cd23ebe3c890248eaabfbb4ea4\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec70d4eb0351866340efe37c2014fef\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddd5e7b38041965968ff0c456e5ef322\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\294fa292f5b2d15ea9a4a7a32547c712\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd3810ce99ffe1cc26d54f0b91968d35\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58a0920e123e93dd6aa702d27ab7530e\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ba2c056d15ac2bb85e5cc14a5abc112\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45af1ebc35cbf9d2d2886a132166b73a\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4220b5ee723fba7acc664a2b5c85228\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a955e1aaa27844724e860bd6269b5afd\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c5a81a775ceb5f896270c240e3627f0\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d3f16dd28e73e73ebdd49d75e0396da\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c092edbccc16347970ed4f22e8da111a\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebca0abd79b4e189ce4efe6f41f957a4\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d51a44ab6b56289d4858158a1ad6dd\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a0d5794843604364eca179d4eb7b8a1\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b98555939aa5eea06aa9fccf2cf9525\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70a41aa59d26a7808f11dc71c456d768\transformed\rules-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffe8b6ec9d46186c39471e3e37cb2701\transformed\espresso-core-3.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76c4d580a7e756fbf27e29c6ee14b546\transformed\runner-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d9d3675465ff69d847e2f781f20c61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\285b6d6abe2dd4ba9515886e610a7341\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ccaaf8ba0ff17d4bbcf5fc9064ae7a64\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92a2169b5b2674c8bbe10e1b1d80da0f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b783e88f81ef41387b62e1f4fef7408\transformed\monitor-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff849810dfe34a0ab790c402787c149e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8c994221057b3c1840994d9e7871976\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\881c4abfce54f2ad198c7660c5ae4450\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d0d33ecaece8d17a3b79a6bcd1ce82f\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76a0dd850d9f18b6d5dce2588e83d61b\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\768c044a1b4db4aa31548ac9b4db55c8\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4e9d33e8be7718e1e9997a4b8a9b898\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47506b70b93177473c53f4d5d7e77c91\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f09a22cdd213028a38958888c7d16e20\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.agora.rtc:iris-rtc:4.5.2-build.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8025b73987cc94b15b57f0db06b908e\transformed\jetified-iris-rtc-4.5.2-build.1\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-sdk:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b566b144f24aaecc40f84aae65d82b2e\transformed\jetified-full-sdk-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:2:1-21:12
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a63b21759398e83092b56262a073cdf\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:2:1-13:12
MERGED from [io.agora.rtc:full-rtc-basic:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aceae36db7ceb700b56febe67a88552b\transformed\jetified-full-rtc-basic-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:ains:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf53be273b760b5a4d925173ff0f1143\transformed\jetified-ains-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:ains-ll:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2311eb0ffb201204940a220fa821ff22\transformed\jetified-ains-ll-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:audio-beauty:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40920ab0d4f64d7ad8486f25ea2c4936\transformed\jetified-audio-beauty-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:clear-vision:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4de41ffaf0f1fac276576a3383ae26e\transformed\jetified-clear-vision-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-content-inspect:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\430b5068a09980e3328146acfc7d213a\transformed\jetified-full-content-inspect-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:screen-capture:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\921c96f2a54cc2a9e4fa6d2f0e139fb0\transformed\jetified-screen-capture-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-virtual-background:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3129c0aa65d072a5e9a8b89b905c4753\transformed\jetified-full-virtual-background-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:spatial-audio:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb54ae6afdda5bfeb497f2460c4f3450\transformed\jetified-spatial-audio-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:aiaec:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\664675af4ddff7261540588a1e631046\transformed\jetified-aiaec-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:aiaec-ll:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af2b953e65d4d5b6f78997f87ade4780\transformed\jetified-aiaec-ll-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-vqa:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d978ee2e5c6f5f84aa300827a90f7b2\transformed\jetified-full-vqa-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-face-detect:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de94cc1474313ee38e7a6a14ee64b42d\transformed\jetified-full-face-detect-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-face-capture:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f72001cf10ee074e171931a733be920c\transformed\jetified-full-face-capture-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-voice-drive:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7baeeea09a2e73662c3231511cacfc66\transformed\jetified-full-voice-drive-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-video-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21730f7af1e36316ece9dd940c740afa\transformed\jetified-full-video-codec-enc-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-video-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83a31f91f536d48aeb965cff537d299e\transformed\jetified-full-video-codec-dec-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-video-av1-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e57e6bbc9f964cd945349386164e36cd\transformed\jetified-full-video-av1-codec-enc-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [io.agora.rtc:full-video-av1-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60bdd0c524da9ef4f53812860ec1bbd2\transformed\jetified-full-video-av1-codec-dec-4.5.2\AndroidManifest.xml:2:1-11:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e1412f82216e9a99f9f845f4567c1f2\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d4bf435606b4fb3525b04e78978ca8d\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [io.agora.infra:aosl:1.2.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eeeb962f535d32e0ec3f7f635a5827fc\transformed\jetified-aosl-1.2.13.1\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:4:5-79
MERGED from [:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
MERGED from [:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
	android:name
		ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:4:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:5:5-81
MERGED from [:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
	android:name
		ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:5:22-78
uses-permission#android.permission.INTERNET
ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:6:5-67
MERGED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:6:5-67
MERGED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb6a7e0963abf3e29d747a5c9e4e78fe\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb6a7e0963abf3e29d747a5c9e4e78fe\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-79
MERGED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-79
MERGED from [:connectivity_plus] C:\WEB\develop\frontend\ubuzima_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] C:\WEB\develop\frontend\ubuzima_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb6a7e0963abf3e29d747a5c9e4e78fe\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb6a7e0963abf3e29d747a5c9e4e78fe\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f287878c588d535c6238ddf78c37b2ed\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f287878c588d535c6238ddf78c37b2ed\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab1dc0cd23ebe3c890248eaabfbb4ea4\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab1dc0cd23ebe3c890248eaabfbb4ea4\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:7:22-76
queries
ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:52:5-57:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:53:9-56:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:54:13-72
	android:name
		ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:54:21-70
data
ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:55:13-50
	android:mimeType
		ADDED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\main\AndroidManifest.xml:55:19-48
uses-sdk
INJECTED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\debug\AndroidManifest.xml
MERGED from [:flutter_tts] C:\WEB\develop\frontend\ubuzima_app\build\flutter_tts\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_tts] C:\WEB\develop\frontend\ubuzima_app\build\flutter_tts\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\WEB\develop\frontend\ubuzima_app\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\WEB\develop\frontend\ubuzima_app\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:audioplayers_android] C:\WEB\develop\frontend\ubuzima_app\build\audioplayers_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:audioplayers_android] C:\WEB\develop\frontend\ubuzima_app\build\audioplayers_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3336a24e8ef5d3d66fff7f5ca80854\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc3336a24e8ef5d3d66fff7f5ca80854\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:camera_android] C:\WEB\develop\frontend\ubuzima_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:camera_android] C:\WEB\develop\frontend\ubuzima_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\WEB\develop\frontend\ubuzima_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\WEB\develop\frontend\ubuzima_app\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] C:\WEB\develop\frontend\ubuzima_app\build\device_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] C:\WEB\develop\frontend\ubuzima_app\build\device_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\WEB\develop\frontend\ubuzima_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\WEB\develop\frontend\ubuzima_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_maps_flutter_android] C:\WEB\develop\frontend\ubuzima_app\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_maps_flutter_android] C:\WEB\develop\frontend\ubuzima_app\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:local_auth_android] C:\WEB\develop\frontend\ubuzima_app\build\local_auth_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:local_auth_android] C:\WEB\develop\frontend\ubuzima_app\build\local_auth_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\WEB\develop\frontend\ubuzima_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\WEB\develop\frontend\ubuzima_app\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geocoding_android] C:\WEB\develop\frontend\ubuzima_app\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geocoding_android] C:\WEB\develop\frontend\ubuzima_app\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:integration_test] C:\WEB\develop\frontend\ubuzima_app\build\integration_test\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-44
MERGED from [:integration_test] C:\WEB\develop\frontend\ubuzima_app\build\integration_test\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-44
MERGED from [:iris_method_channel] C:\WEB\develop\frontend\ubuzima_app\build\iris_method_channel\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:iris_method_channel] C:\WEB\develop\frontend\ubuzima_app\build\iris_method_channel\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\WEB\develop\frontend\ubuzima_app\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\WEB\develop\frontend\ubuzima_app\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\WEB\develop\frontend\ubuzima_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\WEB\develop\frontend\ubuzima_app\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\WEB\develop\frontend\ubuzima_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\WEB\develop\frontend\ubuzima_app\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:printing] C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:printing] C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\WEB\develop\frontend\ubuzima_app\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\WEB\develop\frontend\ubuzima_app\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\WEB\develop\frontend\ubuzima_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\WEB\develop\frontend\ubuzima_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] C:\WEB\develop\frontend\ubuzima_app\build\video_player_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] C:\WEB\develop\frontend\ubuzima_app\build\video_player_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd1be39a4121b41df3cecdb1f1acb24d\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd1be39a4121b41df3cecdb1f1acb24d\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\627f757bcd95f222224618b867985469\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\627f757bcd95f222224618b867985469\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13d0c2544ad01fcdb01c4e62d7089728\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13d0c2544ad01fcdb01c4e62d7089728\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb6a7e0963abf3e29d747a5c9e4e78fe\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb6a7e0963abf3e29d747a5c9e4e78fe\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\277aa05e63c0582e9a6c0fb9811696d7\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\277aa05e63c0582e9a6c0fb9811696d7\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0098a6e93522fecc805d8900172003dc\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0098a6e93522fecc805d8900172003dc\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75e1bcd7a8b61b1e132d50e7766bfd37\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75e1bcd7a8b61b1e132d50e7766bfd37\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb98276ae8bdd946a2259a69bef27bdf\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb98276ae8bdd946a2259a69bef27bdf\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74ed8ceaa55f9ae57c7b49acd30346c2\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74ed8ceaa55f9ae57c7b49acd30346c2\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1f85211ae6978d0218d51595a7f7e62\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1f85211ae6978d0218d51595a7f7e62\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba8599b6014e514325b6a37b5077642\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba8599b6014e514325b6a37b5077642\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02cddce53ae6e54afa639a8fcdb8b322\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02cddce53ae6e54afa639a8fcdb8b322\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7cbd00988e28f46cac66dd959ae6532\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7cbd00988e28f46cac66dd959ae6532\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b426ae45ba34699527b81c12c814bb78\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b426ae45ba34699527b81c12c814bb78\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d6ee565ab495ccfff8140c2f79bcdec\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d6ee565ab495ccfff8140c2f79bcdec\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6334635279cb43af07c2144a95c2be9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6334635279cb43af07c2144a95c2be9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e4c2e4e231b6a1dd462fb9cdc4ff977\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e4c2e4e231b6a1dd462fb9cdc4ff977\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5cf1d9716b9650d5a0a992711d623bf8\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5cf1d9716b9650d5a0a992711d623bf8\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1da3d52bf544e2149dd46ce2061a20cf\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1da3d52bf544e2149dd46ce2061a20cf\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1682fe25d19efcf6ed57ec9bfdc01f5\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1682fe25d19efcf6ed57ec9bfdc01f5\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebeb29d7113d60e59d5e0acf92f847f3\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebeb29d7113d60e59d5e0acf92f847f3\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7156a54e8f0502a1f8f57797d8b3e6e\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7156a54e8f0502a1f8f57797d8b3e6e\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b91be3af319ede480d7185430690ee1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b91be3af319ede480d7185430690ee1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2e21678784b3eef6b33f576b1691a56\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2e21678784b3eef6b33f576b1691a56\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0eede8414d1ee0e5f236daeb1a2e59a\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0eede8414d1ee0e5f236daeb1a2e59a\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b180219ad72ca48e76b8a5127003ab7\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b180219ad72ca48e76b8a5127003ab7\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dbfa2b099ab663030b8159eebacd12\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dbfa2b099ab663030b8159eebacd12\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abeed4f47a72eff8f5b4e9bf7f2f3c91\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abeed4f47a72eff8f5b4e9bf7f2f3c91\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21f508358b7ef6793f647ddb068091fd\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21f508358b7ef6793f647ddb068091fd\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55520e4df2220e27f13f0bbb7467d11a\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55520e4df2220e27f13f0bbb7467d11a\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a70ddd560199940b45ffc1a1c4db7f79\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a70ddd560199940b45ffc1a1c4db7f79\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\308a2e77faa557d0bd706972416bde6a\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\308a2e77faa557d0bd706972416bde6a\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e7454f640b41d9fb1f89a6b912b741e\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e7454f640b41d9fb1f89a6b912b741e\transformed\jetified-media3-extractor-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\458a1a3589d1e39abd86072805af9a32\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\458a1a3589d1e39abd86072805af9a32\transformed\jetified-media3-container-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcc302a7b606f68719f42912b7ef31a0\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dcc302a7b606f68719f42912b7ef31a0\transformed\jetified-media3-datasource-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a93664941e2b88c03441606ac08a6fc2\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a93664941e2b88c03441606ac08a6fc2\transformed\jetified-media3-decoder-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8df7439c67c18e2b650336c112408c6\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8df7439c67c18e2b650336c112408c6\transformed\jetified-media3-database-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f287878c588d535c6238ddf78c37b2ed\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f287878c588d535c6238ddf78c37b2ed\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e45546448fd971172ffd9d216a54d834\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e45546448fd971172ffd9d216a54d834\transformed\jetified-media3-exoplayer-hls-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09fc8eeb7b5bae391cca11b3fcf42aca\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09fc8eeb7b5bae391cca11b3fcf42aca\transformed\jetified-media3-exoplayer-dash-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d53732e8ebd4dda2c45fb0959ea7441\transformed\jetified-media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d53732e8ebd4dda2c45fb0959ea7441\transformed\jetified-media3-exoplayer-rtsp-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d54be51abe8aaa49b41f4a3e99db2aa4\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d54be51abe8aaa49b41f4a3e99db2aa4\transformed\jetified-media3-exoplayer-smoothstreaming-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab1dc0cd23ebe3c890248eaabfbb4ea4\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab1dc0cd23ebe3c890248eaabfbb4ea4\transformed\jetified-media3-exoplayer-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec70d4eb0351866340efe37c2014fef\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec70d4eb0351866340efe37c2014fef\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddd5e7b38041965968ff0c456e5ef322\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddd5e7b38041965968ff0c456e5ef322\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\294fa292f5b2d15ea9a4a7a32547c712\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\294fa292f5b2d15ea9a4a7a32547c712\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd3810ce99ffe1cc26d54f0b91968d35\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd3810ce99ffe1cc26d54f0b91968d35\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58a0920e123e93dd6aa702d27ab7530e\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58a0920e123e93dd6aa702d27ab7530e\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ba2c056d15ac2bb85e5cc14a5abc112\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ba2c056d15ac2bb85e5cc14a5abc112\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45af1ebc35cbf9d2d2886a132166b73a\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45af1ebc35cbf9d2d2886a132166b73a\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4220b5ee723fba7acc664a2b5c85228\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4220b5ee723fba7acc664a2b5c85228\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a955e1aaa27844724e860bd6269b5afd\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a955e1aaa27844724e860bd6269b5afd\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c5a81a775ceb5f896270c240e3627f0\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c5a81a775ceb5f896270c240e3627f0\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d3f16dd28e73e73ebdd49d75e0396da\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d3f16dd28e73e73ebdd49d75e0396da\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c092edbccc16347970ed4f22e8da111a\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c092edbccc16347970ed4f22e8da111a\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebca0abd79b4e189ce4efe6f41f957a4\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebca0abd79b4e189ce4efe6f41f957a4\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d51a44ab6b56289d4858158a1ad6dd\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d51a44ab6b56289d4858158a1ad6dd\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a0d5794843604364eca179d4eb7b8a1\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a0d5794843604364eca179d4eb7b8a1\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b98555939aa5eea06aa9fccf2cf9525\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b98555939aa5eea06aa9fccf2cf9525\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70a41aa59d26a7808f11dc71c456d768\transformed\rules-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\70a41aa59d26a7808f11dc71c456d768\transformed\rules-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffe8b6ec9d46186c39471e3e37cb2701\transformed\espresso-core-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffe8b6ec9d46186c39471e3e37cb2701\transformed\espresso-core-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76c4d580a7e756fbf27e29c6ee14b546\transformed\runner-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76c4d580a7e756fbf27e29c6ee14b546\transformed\runner-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d9d3675465ff69d847e2f781f20c61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d9d3675465ff69d847e2f781f20c61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\285b6d6abe2dd4ba9515886e610a7341\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\285b6d6abe2dd4ba9515886e610a7341\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ccaaf8ba0ff17d4bbcf5fc9064ae7a64\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ccaaf8ba0ff17d4bbcf5fc9064ae7a64\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92a2169b5b2674c8bbe10e1b1d80da0f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92a2169b5b2674c8bbe10e1b1d80da0f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b783e88f81ef41387b62e1f4fef7408\transformed\monitor-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b783e88f81ef41387b62e1f4fef7408\transformed\monitor-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff849810dfe34a0ab790c402787c149e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff849810dfe34a0ab790c402787c149e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8c994221057b3c1840994d9e7871976\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e8c994221057b3c1840994d9e7871976\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\881c4abfce54f2ad198c7660c5ae4450\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\881c4abfce54f2ad198c7660c5ae4450\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d0d33ecaece8d17a3b79a6bcd1ce82f\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d0d33ecaece8d17a3b79a6bcd1ce82f\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76a0dd850d9f18b6d5dce2588e83d61b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76a0dd850d9f18b6d5dce2588e83d61b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\768c044a1b4db4aa31548ac9b4db55c8\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\768c044a1b4db4aa31548ac9b4db55c8\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4e9d33e8be7718e1e9997a4b8a9b898\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4e9d33e8be7718e1e9997a4b8a9b898\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47506b70b93177473c53f4d5d7e77c91\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47506b70b93177473c53f4d5d7e77c91\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f09a22cdd213028a38958888c7d16e20\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f09a22cdd213028a38958888c7d16e20\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.agora.rtc:iris-rtc:4.5.2-build.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8025b73987cc94b15b57f0db06b908e\transformed\jetified-iris-rtc-4.5.2-build.1\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:iris-rtc:4.5.2-build.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8025b73987cc94b15b57f0db06b908e\transformed\jetified-iris-rtc-4.5.2-build.1\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-sdk:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b566b144f24aaecc40f84aae65d82b2e\transformed\jetified-full-sdk-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-sdk:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b566b144f24aaecc40f84aae65d82b2e\transformed\jetified-full-sdk-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:5:5-73
MERGED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:5:5-73
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a63b21759398e83092b56262a073cdf\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a63b21759398e83092b56262a073cdf\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [io.agora.rtc:full-rtc-basic:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aceae36db7ceb700b56febe67a88552b\transformed\jetified-full-rtc-basic-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-rtc-basic:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aceae36db7ceb700b56febe67a88552b\transformed\jetified-full-rtc-basic-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:ains:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf53be273b760b5a4d925173ff0f1143\transformed\jetified-ains-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:ains:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf53be273b760b5a4d925173ff0f1143\transformed\jetified-ains-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:ains-ll:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2311eb0ffb201204940a220fa821ff22\transformed\jetified-ains-ll-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:ains-ll:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2311eb0ffb201204940a220fa821ff22\transformed\jetified-ains-ll-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:audio-beauty:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40920ab0d4f64d7ad8486f25ea2c4936\transformed\jetified-audio-beauty-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:audio-beauty:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40920ab0d4f64d7ad8486f25ea2c4936\transformed\jetified-audio-beauty-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:clear-vision:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4de41ffaf0f1fac276576a3383ae26e\transformed\jetified-clear-vision-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:clear-vision:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4de41ffaf0f1fac276576a3383ae26e\transformed\jetified-clear-vision-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-content-inspect:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\430b5068a09980e3328146acfc7d213a\transformed\jetified-full-content-inspect-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-content-inspect:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\430b5068a09980e3328146acfc7d213a\transformed\jetified-full-content-inspect-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:screen-capture:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\921c96f2a54cc2a9e4fa6d2f0e139fb0\transformed\jetified-screen-capture-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:screen-capture:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\921c96f2a54cc2a9e4fa6d2f0e139fb0\transformed\jetified-screen-capture-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-virtual-background:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3129c0aa65d072a5e9a8b89b905c4753\transformed\jetified-full-virtual-background-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-virtual-background:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3129c0aa65d072a5e9a8b89b905c4753\transformed\jetified-full-virtual-background-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:spatial-audio:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb54ae6afdda5bfeb497f2460c4f3450\transformed\jetified-spatial-audio-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:spatial-audio:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb54ae6afdda5bfeb497f2460c4f3450\transformed\jetified-spatial-audio-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:aiaec:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\664675af4ddff7261540588a1e631046\transformed\jetified-aiaec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:aiaec:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\664675af4ddff7261540588a1e631046\transformed\jetified-aiaec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:aiaec-ll:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af2b953e65d4d5b6f78997f87ade4780\transformed\jetified-aiaec-ll-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:aiaec-ll:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\af2b953e65d4d5b6f78997f87ade4780\transformed\jetified-aiaec-ll-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-vqa:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d978ee2e5c6f5f84aa300827a90f7b2\transformed\jetified-full-vqa-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-vqa:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4d978ee2e5c6f5f84aa300827a90f7b2\transformed\jetified-full-vqa-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-face-detect:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de94cc1474313ee38e7a6a14ee64b42d\transformed\jetified-full-face-detect-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-face-detect:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de94cc1474313ee38e7a6a14ee64b42d\transformed\jetified-full-face-detect-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-face-capture:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f72001cf10ee074e171931a733be920c\transformed\jetified-full-face-capture-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-face-capture:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f72001cf10ee074e171931a733be920c\transformed\jetified-full-face-capture-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-voice-drive:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7baeeea09a2e73662c3231511cacfc66\transformed\jetified-full-voice-drive-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-voice-drive:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7baeeea09a2e73662c3231511cacfc66\transformed\jetified-full-voice-drive-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21730f7af1e36316ece9dd940c740afa\transformed\jetified-full-video-codec-enc-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21730f7af1e36316ece9dd940c740afa\transformed\jetified-full-video-codec-enc-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83a31f91f536d48aeb965cff537d299e\transformed\jetified-full-video-codec-dec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\83a31f91f536d48aeb965cff537d299e\transformed\jetified-full-video-codec-dec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-av1-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e57e6bbc9f964cd945349386164e36cd\transformed\jetified-full-video-av1-codec-enc-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-av1-codec-enc:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e57e6bbc9f964cd945349386164e36cd\transformed\jetified-full-video-av1-codec-enc-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-av1-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60bdd0c524da9ef4f53812860ec1bbd2\transformed\jetified-full-video-av1-codec-dec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.rtc:full-video-av1-codec-dec:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\60bdd0c524da9ef4f53812860ec1bbd2\transformed\jetified-full-video-av1-codec-dec-4.5.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e1412f82216e9a99f9f845f4567c1f2\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e1412f82216e9a99f9f845f4567c1f2\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d4bf435606b4fb3525b04e78978ca8d\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d4bf435606b4fb3525b04e78978ca8d\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [io.agora.infra:aosl:1.2.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eeeb962f535d32e0ec3f7f635a5827fc\transformed\jetified-aosl-1.2.13.1\AndroidManifest.xml:7:5-9:41
MERGED from [io.agora.infra:aosl:1.2.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eeeb962f535d32e0ec3f7f635a5827fc\transformed\jetified-aosl-1.2.13.1\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\WEB\develop\frontend\ubuzima_app\android\app\src\debug\AndroidManifest.xml
service#com.lyokone.location.FlutterLocationService
ADDED from [:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-15:56
	android:enabled
		ADDED from [:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-35
	android:exported
		ADDED from [:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
	android:foregroundServiceType
		ADDED from [:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-53
	android:name
		ADDED from [:location] C:\WEB\develop\frontend\ubuzima_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-71
service#com.baseflow.geolocator.GeolocatorLocationService
ADDED from [:geolocator_android] C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
	android:enabled
		ADDED from [:geolocator_android] C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
	android:exported
		ADDED from [:geolocator_android] C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:foregroundServiceType
		ADDED from [:geolocator_android] C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
	android:name
		ADDED from [:geolocator_android] C:\WEB\develop\frontend\ubuzima_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
uses-permission#android.permission.READ_PHONE_STATE
ADDED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-75
	android:name
		ADDED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-72
uses-permission#android.permission.RECORD_AUDIO
ADDED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-71
MERGED from [:camera_android] C:\WEB\develop\frontend\ubuzima_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
MERGED from [:camera_android] C:\WEB\develop\frontend\ubuzima_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
	android:name
		ADDED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:22-68
uses-permission#android.permission.CAMERA
ADDED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-65
MERGED from [:camera_android] C:\WEB\develop\frontend\ubuzima_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
MERGED from [:camera_android] C:\WEB\develop\frontend\ubuzima_app\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
	android:name
		ADDED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-62
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-80
	android:name
		ADDED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:22-77
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-76
	android:name
		ADDED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:22-73
uses-permission#android.permission.BLUETOOTH
ADDED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-68
	android:name
		ADDED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:22-65
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:5-76
	android:name
		ADDED from [:agora_rtc_engine] C:\WEB\develop\frontend\ubuzima_app\build\agora_rtc_engine\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:22-73
uses-permission#android.permission.WAKE_LOCK
ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb6a7e0963abf3e29d747a5c9e4e78fe\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb6a7e0963abf3e29d747a5c9e4e78fe\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-74
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
	android:exported
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	android:permission
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
	android:name
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
	android:exported
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
	android:name
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
	android:name
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
receiver#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
	android:exported
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
	android:name
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
	android:name
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_core] C:\WEB\develop\frontend\ubuzima_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\WEB\develop\frontend\ubuzima_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\627f757bcd95f222224618b867985469\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\627f757bcd95f222224618b867985469\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
	android:exported
		ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:31:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
	android:value
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
	android:name
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
provider#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
	android:authorities
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
	android:exported
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
	android:initOrder
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
	android:name
		ADDED from [:firebase_messaging] C:\WEB\develop\frontend\ubuzima_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\WEB\develop\frontend\ubuzima_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\WEB\develop\frontend\ubuzima_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\WEB\develop\frontend\ubuzima_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\WEB\develop\frontend\ubuzima_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [:local_auth_android] C:\WEB\develop\frontend\ubuzima_app\build\local_auth_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd1be39a4121b41df3cecdb1f1acb24d\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd1be39a4121b41df3cecdb1f1acb24d\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [:local_auth_android] C:\WEB\develop\frontend\ubuzima_app\build\local_auth_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-69
provider#net.nfet.flutter.printing.PrintFileProvider
ADDED from [:printing] C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:printing] C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:printing] C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-68
	android:exported
		ADDED from [:printing] C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:printing] C:\WEB\develop\frontend\ubuzima_app\build\printing\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-71
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] C:\WEB\develop\frontend\ubuzima_app\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\WEB\develop\frontend\ubuzima_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\WEB\develop\frontend\ubuzima_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\WEB\develop\frontend\ubuzima_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\WEB\develop\frontend\ubuzima_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb6a7e0963abf3e29d747a5c9e4e78fe\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fb6a7e0963abf3e29d747a5c9e4e78fe\transformed\jetified-play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:22-79
meta-data#com.google.firebase.components:com.google.firebase.iid.Registrar
ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3175beb38bb575263e73dd26f917fcdc\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd1be39a4121b41df3cecdb1f1acb24d\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bd1be39a4121b41df3cecdb1f1acb24d\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\223e4c2782dceb65140338abe6972c59\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\185b052657445071c55336e362c7ed18\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90732639fa4f56076e94b2645278604e\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\627f757bcd95f222224618b867985469\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\627f757bcd95f222224618b867985469\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\627f757bcd95f222224618b867985469\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31812384e4d3cdb05bc40b4fb426c266\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:36:17-109
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9db9b81a577112600e8d2ee7b5450923\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abeed4f47a72eff8f5b4e9bf7f2f3c91\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a63b21759398e83092b56262a073cdf\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a63b21759398e83092b56262a073cdf\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abeed4f47a72eff8f5b4e9bf7f2f3c91\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abeed4f47a72eff8f5b4e9bf7f2f3c91\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#rw.health.ubuzima.ubuzima_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#rw.health.ubuzima.ubuzima_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d9d3675465ff69d847e2f781f20c61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d9d3675465ff69d847e2f781f20c61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7db1b95df90f3baf78601bd1f13dbf08\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\06f79797ad77f08b9bcaf5ac75dacce7\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\28a5037b5ebc48ab32c50633c524288e\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:7:5-77
	android:name
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:7:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:8:5-94
	android:name
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:8:22-91
activity#io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenCaptureAssistantActivity
ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:11:9-14:63
	android:screenOrientation
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:13:13-52
	android:configChanges
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:12:13-59
	android:theme
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:14:13-61
	android:name
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:11:19-89
service#io.agora.rtc2.extensions.MediaProjectionMgr$LocalScreenSharingService
ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:16:9-19:19
	android:foregroundServiceType
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:18:13-60
	android:name
		ADDED from [io.agora.rtc:full-screen-sharing:4.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\727fdeca1d52d174b7b04e666385707e\transformed\jetified-full-screen-sharing-4.5.2\AndroidManifest.xml:17:13-73
