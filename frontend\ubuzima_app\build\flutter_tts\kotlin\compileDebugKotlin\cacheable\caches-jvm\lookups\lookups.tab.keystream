  ContentResolver android.content  
ContentValues android.content  Context android.content  insert android.content.ContentResolver  openFileDescriptor android.content.ContentResolver  Environment android.content.ContentValues  
MediaStore android.content.ContentValues  apply android.content.ContentValues  getAPPLY android.content.ContentValues  getApply android.content.ContentValues  put android.content.ContentValues  
AUDIO_SERVICE android.content.Context  contentResolver android.content.Context  getCONTENTResolver android.content.Context  getContentResolver android.content.Context  getSystemService android.content.Context  setContentResolver android.content.Context  AudioAttributes 
android.media  AudioFocusRequest 
android.media  AudioManager 
android.media  Builder android.media.AudioAttributes  CONTENT_TYPE_SPEECH android.media.AudioAttributes  $USAGE_ASSISTANCE_NAVIGATION_GUIDANCE android.media.AudioAttributes  build %android.media.AudioAttributes.Builder  setContentType %android.media.AudioAttributes.Builder  setUsage %android.media.AudioAttributes.Builder  Builder android.media.AudioFocusRequest  getLET android.media.AudioFocusRequest  getLet android.media.AudioFocusRequest  let android.media.AudioFocusRequest  build 'android.media.AudioFocusRequest.Builder  setOnAudioFocusChangeListener 'android.media.AudioFocusRequest.Builder  "AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK android.media.AudioManager  STREAM_MUSIC android.media.AudioManager  abandonAudioFocus android.media.AudioManager  abandonAudioFocusRequest android.media.AudioManager  requestAudioFocus android.media.AudioManager  <SAM-CONSTRUCTOR> 5android.media.AudioManager.OnAudioFocusChangeListener  Uri android.net  getPATH android.net.Uri  getPath android.net.Uri  path android.net.Uri  setPath android.net.Uri  Build 
android.os  Bundle 
android.os  Environment 
android.os  Handler 
android.os  Looper 
android.os  ParcelFileDescriptor 
android.os  putFloat android.os.BaseBundle  	putString android.os.BaseBundle  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  putFloat android.os.Bundle  	putString android.os.Bundle  DIRECTORY_MUSIC android.os.Environment  !getExternalStoragePublicDirectory android.os.Environment  post android.os.Handler  
getMainLooper android.os.Looper  close android.os.ParcelFileDescriptor  closeWithError android.os.ParcelFileDescriptor  equals android.os.ParcelFileDescriptor  
MediaStore android.provider  OpenableColumns android.provider  Audio android.provider.MediaStore  MediaColumns android.provider.MediaStore  Media !android.provider.MediaStore.Audio  EXTERNAL_CONTENT_URI 'android.provider.MediaStore.Audio.Media  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  	MIME_TYPE (android.provider.MediaStore.MediaColumns  
RELATIVE_PATH (android.provider.MediaStore.MediaColumns  TextToSpeech android.speech.tts  UtteranceProgressListener android.speech.tts  Voice android.speech.tts  Engine android.speech.tts.TextToSpeech  
EngineInfo android.speech.tts.TextToSpeech  LANG_AVAILABLE android.speech.tts.TextToSpeech  OnInitListener android.speech.tts.TextToSpeech  	QUEUE_ADD android.speech.tts.TextToSpeech  QUEUE_FLUSH android.speech.tts.TextToSpeech  SUCCESS android.speech.tts.TextToSpeech  availableLanguages android.speech.tts.TextToSpeech  
defaultEngine android.speech.tts.TextToSpeech  defaultVoice android.speech.tts.TextToSpeech  engines android.speech.tts.TextToSpeech  equals android.speech.tts.TextToSpeech  getAVAILABLELanguages android.speech.tts.TextToSpeech  getAvailableLanguages android.speech.tts.TextToSpeech  getDEFAULTEngine android.speech.tts.TextToSpeech  getDEFAULTVoice android.speech.tts.TextToSpeech  getDefaultEngine android.speech.tts.TextToSpeech  getDefaultVoice android.speech.tts.TextToSpeech  
getENGINES android.speech.tts.TextToSpeech  
getEngines android.speech.tts.TextToSpeech  getJAVAClass android.speech.tts.TextToSpeech  getJavaClass android.speech.tts.TextToSpeech  getLANGUAGE android.speech.tts.TextToSpeech  getLanguage android.speech.tts.TextToSpeech  getMaxSpeechInputLength android.speech.tts.TextToSpeech  getVOICE android.speech.tts.TextToSpeech  	getVOICES android.speech.tts.TextToSpeech  getVoice android.speech.tts.TextToSpeech  	getVoices android.speech.tts.TextToSpeech  isLanguageAvailable android.speech.tts.TextToSpeech  	javaClass android.speech.tts.TextToSpeech  language android.speech.tts.TextToSpeech  playSilentUtterance android.speech.tts.TextToSpeech  setAudioAttributes android.speech.tts.TextToSpeech  setAvailableLanguages android.speech.tts.TextToSpeech  setDefaultEngine android.speech.tts.TextToSpeech  setDefaultVoice android.speech.tts.TextToSpeech  
setEngines android.speech.tts.TextToSpeech  setLanguage android.speech.tts.TextToSpeech  setOnUtteranceProgressListener android.speech.tts.TextToSpeech  setPitch android.speech.tts.TextToSpeech  
setSpeechRate android.speech.tts.TextToSpeech  setVoice android.speech.tts.TextToSpeech  	setVoices android.speech.tts.TextToSpeech  shutdown android.speech.tts.TextToSpeech  speak android.speech.tts.TextToSpeech  stop android.speech.tts.TextToSpeech  synthesizeToFile android.speech.tts.TextToSpeech  voice android.speech.tts.TextToSpeech  voices android.speech.tts.TextToSpeech  KEY_FEATURE_NOT_INSTALLED &android.speech.tts.TextToSpeech.Engine  KEY_PARAM_UTTERANCE_ID &android.speech.tts.TextToSpeech.Engine  KEY_PARAM_VOLUME &android.speech.tts.TextToSpeech.Engine  name *android.speech.tts.TextToSpeech.EngineInfo  <SAM-CONSTRUCTOR> .android.speech.tts.TextToSpeech.OnInitListener  Boolean ,android.speech.tts.UtteranceProgressListener  Build ,android.speech.tts.UtteranceProgressListener  
Deprecated ,android.speech.tts.UtteranceProgressListener  HashMap ,android.speech.tts.UtteranceProgressListener  Int ,android.speech.tts.UtteranceProgressListener  Log ,android.speech.tts.UtteranceProgressListener  SILENCE_PREFIX ,android.speech.tts.UtteranceProgressListener  SYNTHESIZE_TO_FILE_PREFIX ,android.speech.tts.UtteranceProgressListener  String ,android.speech.tts.UtteranceProgressListener  TextToSpeech ,android.speech.tts.UtteranceProgressListener  awaitSpeakCompletion ,android.speech.tts.UtteranceProgressListener  awaitSynthCompletion ,android.speech.tts.UtteranceProgressListener  closeParcelFileDescriptor ,android.speech.tts.UtteranceProgressListener  invokeMethod ,android.speech.tts.UtteranceProgressListener  isPaused ,android.speech.tts.UtteranceProgressListener  lastProgress ,android.speech.tts.UtteranceProgressListener  
onProgress ,android.speech.tts.UtteranceProgressListener  onRangeStart ,android.speech.tts.UtteranceProgressListener  	pauseText ,android.speech.tts.UtteranceProgressListener  	queueMode ,android.speech.tts.UtteranceProgressListener  releaseAudioFocus ,android.speech.tts.UtteranceProgressListener  set ,android.speech.tts.UtteranceProgressListener  speakCompletion ,android.speech.tts.UtteranceProgressListener  speaking ,android.speech.tts.UtteranceProgressListener  
startsWith ,android.speech.tts.UtteranceProgressListener  	substring ,android.speech.tts.UtteranceProgressListener  synth ,android.speech.tts.UtteranceProgressListener  synthCompletion ,android.speech.tts.UtteranceProgressListener  tag ,android.speech.tts.UtteranceProgressListener  
utterances ,android.speech.tts.UtteranceProgressListener  LATENCY_HIGH android.speech.tts.Voice  LATENCY_LOW android.speech.tts.Voice  LATENCY_NORMAL android.speech.tts.Voice  LATENCY_VERY_HIGH android.speech.tts.Voice  LATENCY_VERY_LOW android.speech.tts.Voice  QUALITY_HIGH android.speech.tts.Voice  QUALITY_LOW android.speech.tts.Voice  QUALITY_NORMAL android.speech.tts.Voice  QUALITY_VERY_HIGH android.speech.tts.Voice  QUALITY_VERY_LOW android.speech.tts.Voice  equals android.speech.tts.Voice  features android.speech.tts.Voice  getFEATURES android.speech.tts.Voice  getFeatures android.speech.tts.Voice  getISNetworkConnectionRequired android.speech.tts.Voice  getIsNetworkConnectionRequired android.speech.tts.Voice  
getLATENCY android.speech.tts.Voice  	getLOCALE android.speech.tts.Voice  
getLatency android.speech.tts.Voice  	getLocale android.speech.tts.Voice  getNAME android.speech.tts.Voice  getName android.speech.tts.Voice  
getQUALITY android.speech.tts.Voice  
getQuality android.speech.tts.Voice  isNetworkConnectionRequired android.speech.tts.Voice  latency android.speech.tts.Voice  locale android.speech.tts.Voice  name android.speech.tts.Voice  quality android.speech.tts.Voice  setFeatures android.speech.tts.Voice  
setLatency android.speech.tts.Voice  	setLocale android.speech.tts.Voice  setName android.speech.tts.Voice  setNetworkConnectionRequired android.speech.tts.Voice  
setQuality android.speech.tts.Voice  Any com.tundralabs.fluttertts  Array com.tundralabs.fluttertts  	ArrayList com.tundralabs.fluttertts  AudioAttributes com.tundralabs.fluttertts  AudioFocusRequest com.tundralabs.fluttertts  AudioManager com.tundralabs.fluttertts  Boolean com.tundralabs.fluttertts  Build com.tundralabs.fluttertts  Bundle com.tundralabs.fluttertts  
ContentValues com.tundralabs.fluttertts  Context com.tundralabs.fluttertts  
Deprecated com.tundralabs.fluttertts  Environment com.tundralabs.fluttertts  	Exception com.tundralabs.fluttertts  File com.tundralabs.fluttertts  Float com.tundralabs.fluttertts  FlutterTtsPlugin com.tundralabs.fluttertts  Handler com.tundralabs.fluttertts  HashMap com.tundralabs.fluttertts  IllegalAccessException com.tundralabs.fluttertts  IllegalArgumentException com.tundralabs.fluttertts  Int com.tundralabs.fluttertts  List com.tundralabs.fluttertts  Locale com.tundralabs.fluttertts  Log com.tundralabs.fluttertts  Looper com.tundralabs.fluttertts  Map com.tundralabs.fluttertts  
MediaStore com.tundralabs.fluttertts  
MethodChannel com.tundralabs.fluttertts  
MutableMap com.tundralabs.fluttertts  NullPointerException com.tundralabs.fluttertts  Runnable com.tundralabs.fluttertts  SILENCE_PREFIX com.tundralabs.fluttertts  SYNTHESIZE_TO_FILE_PREFIX com.tundralabs.fluttertts  Set com.tundralabs.fluttertts  String com.tundralabs.fluttertts  TextToSpeech com.tundralabs.fluttertts  UUID com.tundralabs.fluttertts  Voice com.tundralabs.fluttertts  apply com.tundralabs.fluttertts  awaitSpeakCompletion com.tundralabs.fluttertts  awaitSynthCompletion com.tundralabs.fluttertts  closeParcelFileDescriptor com.tundralabs.fluttertts  indices com.tundralabs.fluttertts  invokeMethod com.tundralabs.fluttertts  isEmpty com.tundralabs.fluttertts  isPaused com.tundralabs.fluttertts  java com.tundralabs.fluttertts  	javaClass com.tundralabs.fluttertts  joinToString com.tundralabs.fluttertts  lastProgress com.tundralabs.fluttertts  let com.tundralabs.fluttertts  	pauseText com.tundralabs.fluttertts  plus com.tundralabs.fluttertts  	queueMode com.tundralabs.fluttertts  rangeTo com.tundralabs.fluttertts  releaseAudioFocus com.tundralabs.fluttertts  set com.tundralabs.fluttertts  speakCompletion com.tundralabs.fluttertts  speaking com.tundralabs.fluttertts  
startsWith com.tundralabs.fluttertts  	substring com.tundralabs.fluttertts  synchronized com.tundralabs.fluttertts  synth com.tundralabs.fluttertts  synthCompletion com.tundralabs.fluttertts  tag com.tundralabs.fluttertts  toFloat com.tundralabs.fluttertts  toInt com.tundralabs.fluttertts  
utterances com.tundralabs.fluttertts  Any *com.tundralabs.fluttertts.FlutterTtsPlugin  Array *com.tundralabs.fluttertts.FlutterTtsPlugin  	ArrayList *com.tundralabs.fluttertts.FlutterTtsPlugin  AudioAttributes *com.tundralabs.fluttertts.FlutterTtsPlugin  AudioFocusRequest *com.tundralabs.fluttertts.FlutterTtsPlugin  AudioManager *com.tundralabs.fluttertts.FlutterTtsPlugin  BinaryMessenger *com.tundralabs.fluttertts.FlutterTtsPlugin  Boolean *com.tundralabs.fluttertts.FlutterTtsPlugin  Build *com.tundralabs.fluttertts.FlutterTtsPlugin  Bundle *com.tundralabs.fluttertts.FlutterTtsPlugin  
ContentValues *com.tundralabs.fluttertts.FlutterTtsPlugin  Context *com.tundralabs.fluttertts.FlutterTtsPlugin  
Deprecated *com.tundralabs.fluttertts.FlutterTtsPlugin  Environment *com.tundralabs.fluttertts.FlutterTtsPlugin  	Exception *com.tundralabs.fluttertts.FlutterTtsPlugin  Field *com.tundralabs.fluttertts.FlutterTtsPlugin  File *com.tundralabs.fluttertts.FlutterTtsPlugin  Float *com.tundralabs.fluttertts.FlutterTtsPlugin  
FlutterPlugin *com.tundralabs.fluttertts.FlutterTtsPlugin  Handler *com.tundralabs.fluttertts.FlutterTtsPlugin  HashMap *com.tundralabs.fluttertts.FlutterTtsPlugin  IllegalAccessException *com.tundralabs.fluttertts.FlutterTtsPlugin  IllegalArgumentException *com.tundralabs.fluttertts.FlutterTtsPlugin  Int *com.tundralabs.fluttertts.FlutterTtsPlugin  List *com.tundralabs.fluttertts.FlutterTtsPlugin  Locale *com.tundralabs.fluttertts.FlutterTtsPlugin  Log *com.tundralabs.fluttertts.FlutterTtsPlugin  Looper *com.tundralabs.fluttertts.FlutterTtsPlugin  Map *com.tundralabs.fluttertts.FlutterTtsPlugin  
MediaStore *com.tundralabs.fluttertts.FlutterTtsPlugin  
MethodCall *com.tundralabs.fluttertts.FlutterTtsPlugin  
MethodChannel *com.tundralabs.fluttertts.FlutterTtsPlugin  MissingResourceException *com.tundralabs.fluttertts.FlutterTtsPlugin  
MutableMap *com.tundralabs.fluttertts.FlutterTtsPlugin  NullPointerException *com.tundralabs.fluttertts.FlutterTtsPlugin  ParcelFileDescriptor *com.tundralabs.fluttertts.FlutterTtsPlugin  Result *com.tundralabs.fluttertts.FlutterTtsPlugin  Runnable *com.tundralabs.fluttertts.FlutterTtsPlugin  SILENCE_PREFIX *com.tundralabs.fluttertts.FlutterTtsPlugin  SYNTHESIZE_TO_FILE_PREFIX *com.tundralabs.fluttertts.FlutterTtsPlugin  Set *com.tundralabs.fluttertts.FlutterTtsPlugin  String *com.tundralabs.fluttertts.FlutterTtsPlugin  TextToSpeech *com.tundralabs.fluttertts.FlutterTtsPlugin  UUID *com.tundralabs.fluttertts.FlutterTtsPlugin  UtteranceProgressListener *com.tundralabs.fluttertts.FlutterTtsPlugin  Voice *com.tundralabs.fluttertts.FlutterTtsPlugin  apply *com.tundralabs.fluttertts.FlutterTtsPlugin  areLanguagesInstalled *com.tundralabs.fluttertts.FlutterTtsPlugin  audioFocusRequest *com.tundralabs.fluttertts.FlutterTtsPlugin  audioManager *com.tundralabs.fluttertts.FlutterTtsPlugin  awaitSpeakCompletion *com.tundralabs.fluttertts.FlutterTtsPlugin  awaitSynthCompletion *com.tundralabs.fluttertts.FlutterTtsPlugin  bundle *com.tundralabs.fluttertts.FlutterTtsPlugin  
clearVoice *com.tundralabs.fluttertts.FlutterTtsPlugin  closeParcelFileDescriptor *com.tundralabs.fluttertts.FlutterTtsPlugin  context *com.tundralabs.fluttertts.FlutterTtsPlugin  currentText *com.tundralabs.fluttertts.FlutterTtsPlugin  engineResult *com.tundralabs.fluttertts.FlutterTtsPlugin  getAPPLY *com.tundralabs.fluttertts.FlutterTtsPlugin  getApply *com.tundralabs.fluttertts.FlutterTtsPlugin  getDefaultEngine *com.tundralabs.fluttertts.FlutterTtsPlugin  getDefaultVoice *com.tundralabs.fluttertts.FlutterTtsPlugin  
getEngines *com.tundralabs.fluttertts.FlutterTtsPlugin  
getISEmpty *com.tundralabs.fluttertts.FlutterTtsPlugin  
getIsEmpty *com.tundralabs.fluttertts.FlutterTtsPlugin  getJAVA *com.tundralabs.fluttertts.FlutterTtsPlugin  getJOINToString *com.tundralabs.fluttertts.FlutterTtsPlugin  getJava *com.tundralabs.fluttertts.FlutterTtsPlugin  getJoinToString *com.tundralabs.fluttertts.FlutterTtsPlugin  getLET *com.tundralabs.fluttertts.FlutterTtsPlugin  getLanguages *com.tundralabs.fluttertts.FlutterTtsPlugin  getLet *com.tundralabs.fluttertts.FlutterTtsPlugin  getPLUS *com.tundralabs.fluttertts.FlutterTtsPlugin  getPlus *com.tundralabs.fluttertts.FlutterTtsPlugin  
getRANGETo *com.tundralabs.fluttertts.FlutterTtsPlugin  
getRangeTo *com.tundralabs.fluttertts.FlutterTtsPlugin  getSET *com.tundralabs.fluttertts.FlutterTtsPlugin  
getSTARTSWith *com.tundralabs.fluttertts.FlutterTtsPlugin  getSUBSTRING *com.tundralabs.fluttertts.FlutterTtsPlugin  getSYNCHRONIZED *com.tundralabs.fluttertts.FlutterTtsPlugin  getSet *com.tundralabs.fluttertts.FlutterTtsPlugin  getSpeechRateValidRange *com.tundralabs.fluttertts.FlutterTtsPlugin  
getStartsWith *com.tundralabs.fluttertts.FlutterTtsPlugin  getSubstring *com.tundralabs.fluttertts.FlutterTtsPlugin  getSynchronized *com.tundralabs.fluttertts.FlutterTtsPlugin  
getTOFloat *com.tundralabs.fluttertts.FlutterTtsPlugin  getTOInt *com.tundralabs.fluttertts.FlutterTtsPlugin  
getToFloat *com.tundralabs.fluttertts.FlutterTtsPlugin  getToInt *com.tundralabs.fluttertts.FlutterTtsPlugin  	getVoices *com.tundralabs.fluttertts.FlutterTtsPlugin  handler *com.tundralabs.fluttertts.FlutterTtsPlugin  indices *com.tundralabs.fluttertts.FlutterTtsPlugin  initInstance *com.tundralabs.fluttertts.FlutterTtsPlugin  invokeMethod *com.tundralabs.fluttertts.FlutterTtsPlugin  isEmpty *com.tundralabs.fluttertts.FlutterTtsPlugin  isLanguageAvailable *com.tundralabs.fluttertts.FlutterTtsPlugin  isLanguageInstalled *com.tundralabs.fluttertts.FlutterTtsPlugin  isPaused *com.tundralabs.fluttertts.FlutterTtsPlugin  ismServiceConnectionUsable *com.tundralabs.fluttertts.FlutterTtsPlugin  java *com.tundralabs.fluttertts.FlutterTtsPlugin  	javaClass *com.tundralabs.fluttertts.FlutterTtsPlugin  joinToString *com.tundralabs.fluttertts.FlutterTtsPlugin  lastProgress *com.tundralabs.fluttertts.FlutterTtsPlugin  latencyToString *com.tundralabs.fluttertts.FlutterTtsPlugin  let *com.tundralabs.fluttertts.FlutterTtsPlugin  maxSpeechInputLength *com.tundralabs.fluttertts.FlutterTtsPlugin  
methodChannel *com.tundralabs.fluttertts.FlutterTtsPlugin  onInitListenerWithCallback *com.tundralabs.fluttertts.FlutterTtsPlugin  onInitListenerWithoutCallback *com.tundralabs.fluttertts.FlutterTtsPlugin  onMethodCall *com.tundralabs.fluttertts.FlutterTtsPlugin  parcelFileDescriptor *com.tundralabs.fluttertts.FlutterTtsPlugin  	pauseText *com.tundralabs.fluttertts.FlutterTtsPlugin  pendingMethodCalls *com.tundralabs.fluttertts.FlutterTtsPlugin  plus *com.tundralabs.fluttertts.FlutterTtsPlugin  qualityToString *com.tundralabs.fluttertts.FlutterTtsPlugin  	queueMode *com.tundralabs.fluttertts.FlutterTtsPlugin  rangeTo *com.tundralabs.fluttertts.FlutterTtsPlugin  readVoiceProperties *com.tundralabs.fluttertts.FlutterTtsPlugin  releaseAudioFocus *com.tundralabs.fluttertts.FlutterTtsPlugin  requestAudioFocus *com.tundralabs.fluttertts.FlutterTtsPlugin  selectedEngine *com.tundralabs.fluttertts.FlutterTtsPlugin  set *com.tundralabs.fluttertts.FlutterTtsPlugin  setAudioAttributesForNavigation *com.tundralabs.fluttertts.FlutterTtsPlugin  	setEngine *com.tundralabs.fluttertts.FlutterTtsPlugin  setLanguage *com.tundralabs.fluttertts.FlutterTtsPlugin  setPitch *com.tundralabs.fluttertts.FlutterTtsPlugin  
setSpeechRate *com.tundralabs.fluttertts.FlutterTtsPlugin  setVoice *com.tundralabs.fluttertts.FlutterTtsPlugin  	setVolume *com.tundralabs.fluttertts.FlutterTtsPlugin  	silencems *com.tundralabs.fluttertts.FlutterTtsPlugin  speak *com.tundralabs.fluttertts.FlutterTtsPlugin  speakCompletion *com.tundralabs.fluttertts.FlutterTtsPlugin  speakResult *com.tundralabs.fluttertts.FlutterTtsPlugin  speaking *com.tundralabs.fluttertts.FlutterTtsPlugin  
startsWith *com.tundralabs.fluttertts.FlutterTtsPlugin  stop *com.tundralabs.fluttertts.FlutterTtsPlugin  	substring *com.tundralabs.fluttertts.FlutterTtsPlugin  synchronized *com.tundralabs.fluttertts.FlutterTtsPlugin  synth *com.tundralabs.fluttertts.FlutterTtsPlugin  synthCompletion *com.tundralabs.fluttertts.FlutterTtsPlugin  synthResult *com.tundralabs.fluttertts.FlutterTtsPlugin  synthesizeToFile *com.tundralabs.fluttertts.FlutterTtsPlugin  tag *com.tundralabs.fluttertts.FlutterTtsPlugin  toFloat *com.tundralabs.fluttertts.FlutterTtsPlugin  toInt *com.tundralabs.fluttertts.FlutterTtsPlugin  tts *com.tundralabs.fluttertts.FlutterTtsPlugin  	ttsStatus *com.tundralabs.fluttertts.FlutterTtsPlugin  utteranceProgressListener *com.tundralabs.fluttertts.FlutterTtsPlugin  
utterances *com.tundralabs.fluttertts.FlutterTtsPlugin  Any 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Array 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  	ArrayList 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  AudioAttributes 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  AudioFocusRequest 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  AudioManager 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  BinaryMessenger 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Boolean 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Build 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Bundle 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
ContentValues 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Context 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
Deprecated 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Environment 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  	Exception 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Field 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  File 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Float 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
FlutterPlugin 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Handler 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  HashMap 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  IllegalAccessException 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  IllegalArgumentException 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Int 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  List 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Locale 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Log 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Looper 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Map 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
MediaStore 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
MethodCall 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
MethodChannel 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  MissingResourceException 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
MutableMap 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  NullPointerException 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  ParcelFileDescriptor 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Result 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Runnable 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  SILENCE_PREFIX 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  SYNTHESIZE_TO_FILE_PREFIX 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Set 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  String 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  TextToSpeech 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  UUID 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  UtteranceProgressListener 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  Voice 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  apply 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  awaitSpeakCompletion 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  awaitSynthCompletion 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  closeParcelFileDescriptor 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getAPPLY 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getApply 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
getISEmpty 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
getIsEmpty 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getJAVA 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getJOINToString 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getJava 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getJoinToString 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getLET 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getLet 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getPLUS 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getPlus 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
getRANGETo 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
getRangeTo 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getSET 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
getSTARTSWith 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getSUBSTRING 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getSYNCHRONIZED 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getSet 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
getStartsWith 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getSubstring 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getSynchronized 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
getTOFloat 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getTOInt 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
getToFloat 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getToInt 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  indices 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  invokeMethod 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  isEmpty 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  isPaused 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  java 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  	javaClass 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  joinToString 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  lastProgress 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  let 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  	pauseText 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  plus 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  	queueMode 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  rangeTo 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  releaseAudioFocus 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  set 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  speakCompletion 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  speaking 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
startsWith 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  	substring 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  synchronized 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  synth 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  synthCompletion 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  tag 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  toFloat 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  toInt 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  
utterances 4com.tundralabs.fluttertts.FlutterTtsPlugin.Companion  getAWAITSpeakCompletion Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getAWAITSynthCompletion Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getAwaitSpeakCompletion Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getAwaitSynthCompletion Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getCLOSEParcelFileDescriptor Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getCloseParcelFileDescriptor Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getINVOKEMethod Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getISPaused Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getInvokeMethod Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getIsPaused Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getLASTProgress Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getLastProgress Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getPAUSEText Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getPauseText Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getQUEUEMode Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getQueueMode Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getRELEASEAudioFocus Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getReleaseAudioFocus Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getSET Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getSPEAKCompletion Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getSPEAKING Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  
getSTARTSWith Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getSUBSTRING Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getSYNTH Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getSYNTHCompletion Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getSet Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getSpeakCompletion Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getSpeaking Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  
getStartsWith Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getSubstring Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getSynth Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getSynthCompletion Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getTAG Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  getTag Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  
getUTTERANCES Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  
getUtterances Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  isPaused Wcom.tundralabs.fluttertts.FlutterTtsPlugin.utteranceProgressListener.<no name provided>  Log 
io.flutter  d io.flutter.Log  e io.flutter.Log  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getAPPLICATIONContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  equals &io.flutter.plugin.common.MethodChannel  invokeMethod &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  equals -io.flutter.plugin.common.MethodChannel.Result  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  File java.io  getPATH java.io.File  getPath java.io.File  path java.io.File  
separatorChar java.io.File  setPath java.io.File  	ArrayList 	java.lang  AudioAttributes 	java.lang  AudioFocusRequest 	java.lang  AudioManager 	java.lang  Boolean 	java.lang  Build 	java.lang  Bundle 	java.lang  
ContentValues 	java.lang  Context 	java.lang  Environment 	java.lang  	Exception 	java.lang  File 	java.lang  Handler 	java.lang  HashMap 	java.lang  IllegalAccessException 	java.lang  IllegalArgumentException 	java.lang  Locale 	java.lang  Log 	java.lang  Looper 	java.lang  
MediaStore 	java.lang  
MethodChannel 	java.lang  NullPointerException 	java.lang  Runnable 	java.lang  SILENCE_PREFIX 	java.lang  SYNTHESIZE_TO_FILE_PREFIX 	java.lang  TextToSpeech 	java.lang  UUID 	java.lang  Voice 	java.lang  apply 	java.lang  awaitSpeakCompletion 	java.lang  awaitSynthCompletion 	java.lang  closeParcelFileDescriptor 	java.lang  indices 	java.lang  invokeMethod 	java.lang  isEmpty 	java.lang  isPaused 	java.lang  java 	java.lang  	javaClass 	java.lang  joinToString 	java.lang  lastProgress 	java.lang  let 	java.lang  	pauseText 	java.lang  plus 	java.lang  	queueMode 	java.lang  rangeTo 	java.lang  releaseAudioFocus 	java.lang  set 	java.lang  speakCompletion 	java.lang  speaking 	java.lang  
startsWith 	java.lang  	substring 	java.lang  synchronized 	java.lang  synth 	java.lang  synthCompletion 	java.lang  tag 	java.lang  toFloat 	java.lang  toInt 	java.lang  
utterances 	java.lang  parseBoolean java.lang.Boolean  declaredFields java.lang.Class  getDECLAREDFields java.lang.Class  getDeclaredFields java.lang.Class  getNAME java.lang.Class  getName java.lang.Class  name java.lang.Class  setDeclaredFields java.lang.Class  setName java.lang.Class  message java.lang.Exception  printStackTrace java.lang.Exception  printStackTrace  java.lang.IllegalAccessException  message "java.lang.IllegalArgumentException  printStackTrace "java.lang.IllegalArgumentException  message java.lang.NullPointerException  printStackTrace &java.lang.ReflectiveOperationException  <SAM-CONSTRUCTOR> java.lang.Runnable  run java.lang.Runnable  printStackTrace java.lang.RuntimeException  Field java.lang.reflect  get "java.lang.reflect.AccessibleObject  get java.lang.reflect.Field  getISAccessible java.lang.reflect.Field  getIsAccessible java.lang.reflect.Field  getNAME java.lang.reflect.Field  getName java.lang.reflect.Field  getTYPE java.lang.reflect.Field  getType java.lang.reflect.Field  isAccessible java.lang.reflect.Field  name java.lang.reflect.Field  
setAccessible java.lang.reflect.Field  setName java.lang.reflect.Field  setType java.lang.reflect.Field  type java.lang.reflect.Field  	ArrayList 	java.util  HashMap 	java.util  Locale 	java.util  MissingResourceException 	java.util  UUID 	java.util  add java.util.AbstractCollection  clear java.util.AbstractCollection  iterator java.util.AbstractCollection  add java.util.AbstractList  clear java.util.AbstractList  iterator java.util.AbstractList  get java.util.AbstractMap  remove java.util.AbstractMap  set java.util.AbstractMap  add java.util.ArrayList  clear java.util.ArrayList  iterator java.util.ArrayList  get java.util.HashMap  getSET java.util.HashMap  getSet java.util.HashMap  remove java.util.HashMap  set java.util.HashMap  equals java.util.Locale  forLanguageTag java.util.Locale  getAvailableLocales java.util.Locale  
getVARIANT java.util.Locale  
getVariant java.util.Locale  
setVariant java.util.Locale  
toLanguageTag java.util.Locale  variant java.util.Locale  message "java.util.MissingResourceException  
randomUUID java.util.UUID  toString java.util.UUID  Any kotlin  Array kotlin  	ArrayList kotlin  AudioAttributes kotlin  AudioFocusRequest kotlin  AudioManager kotlin  Boolean kotlin  Build kotlin  Bundle kotlin  Char kotlin  
ContentValues kotlin  Context kotlin  
Deprecated kotlin  Environment kotlin  	Exception kotlin  File kotlin  Float kotlin  	Function0 kotlin  	Function1 kotlin  Handler kotlin  HashMap kotlin  IllegalAccessException kotlin  IllegalArgumentException kotlin  Int kotlin  Locale kotlin  Log kotlin  Long kotlin  Looper kotlin  
MediaStore kotlin  
MethodChannel kotlin  Nothing kotlin  NullPointerException kotlin  Runnable kotlin  SILENCE_PREFIX kotlin  SYNTHESIZE_TO_FILE_PREFIX kotlin  String kotlin  TextToSpeech kotlin  UUID kotlin  Unit kotlin  Voice kotlin  apply kotlin  awaitSpeakCompletion kotlin  awaitSynthCompletion kotlin  closeParcelFileDescriptor kotlin  indices kotlin  invokeMethod kotlin  isEmpty kotlin  isPaused kotlin  java kotlin  	javaClass kotlin  joinToString kotlin  lastProgress kotlin  let kotlin  	pauseText kotlin  plus kotlin  	queueMode kotlin  rangeTo kotlin  releaseAudioFocus kotlin  set kotlin  speakCompletion kotlin  speaking kotlin  
startsWith kotlin  	substring kotlin  synchronized kotlin  synth kotlin  synthCompletion kotlin  tag kotlin  toFloat kotlin  toInt kotlin  
utterances kotlin  
getINDICES kotlin.Array  
getIndices kotlin.Array  
getRANGETo kotlin.Float  
getRangeTo kotlin.Float  
getISEmpty 
kotlin.String  
getIsEmpty 
kotlin.String  getPLUS 
kotlin.String  getPlus 
kotlin.String  
getSTARTSWith 
kotlin.String  getSUBSTRING 
kotlin.String  
getStartsWith 
kotlin.String  getSubstring 
kotlin.String  
getTOFloat 
kotlin.String  getTOInt 
kotlin.String  
getToFloat 
kotlin.String  getToInt 
kotlin.String  isEmpty 
kotlin.String  	ArrayList kotlin.annotation  AudioAttributes kotlin.annotation  AudioFocusRequest kotlin.annotation  AudioManager kotlin.annotation  Build kotlin.annotation  Bundle kotlin.annotation  
ContentValues kotlin.annotation  Context kotlin.annotation  Environment kotlin.annotation  	Exception kotlin.annotation  File kotlin.annotation  Handler kotlin.annotation  HashMap kotlin.annotation  IllegalAccessException kotlin.annotation  IllegalArgumentException kotlin.annotation  Locale kotlin.annotation  Log kotlin.annotation  Looper kotlin.annotation  
MediaStore kotlin.annotation  
MethodChannel kotlin.annotation  NullPointerException kotlin.annotation  Runnable kotlin.annotation  SILENCE_PREFIX kotlin.annotation  SYNTHESIZE_TO_FILE_PREFIX kotlin.annotation  TextToSpeech kotlin.annotation  UUID kotlin.annotation  Voice kotlin.annotation  apply kotlin.annotation  awaitSpeakCompletion kotlin.annotation  awaitSynthCompletion kotlin.annotation  closeParcelFileDescriptor kotlin.annotation  indices kotlin.annotation  invokeMethod kotlin.annotation  isEmpty kotlin.annotation  isPaused kotlin.annotation  java kotlin.annotation  	javaClass kotlin.annotation  joinToString kotlin.annotation  lastProgress kotlin.annotation  let kotlin.annotation  	pauseText kotlin.annotation  plus kotlin.annotation  	queueMode kotlin.annotation  rangeTo kotlin.annotation  releaseAudioFocus kotlin.annotation  set kotlin.annotation  speakCompletion kotlin.annotation  speaking kotlin.annotation  
startsWith kotlin.annotation  	substring kotlin.annotation  synchronized kotlin.annotation  synth kotlin.annotation  synthCompletion kotlin.annotation  tag kotlin.annotation  toFloat kotlin.annotation  toInt kotlin.annotation  
utterances kotlin.annotation  	ArrayList kotlin.collections  AudioAttributes kotlin.collections  AudioFocusRequest kotlin.collections  AudioManager kotlin.collections  Build kotlin.collections  Bundle kotlin.collections  
ContentValues kotlin.collections  Context kotlin.collections  Environment kotlin.collections  	Exception kotlin.collections  File kotlin.collections  Handler kotlin.collections  HashMap kotlin.collections  IllegalAccessException kotlin.collections  IllegalArgumentException kotlin.collections  List kotlin.collections  Locale kotlin.collections  Log kotlin.collections  Looper kotlin.collections  Map kotlin.collections  
MediaStore kotlin.collections  
MethodChannel kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  NullPointerException kotlin.collections  Runnable kotlin.collections  SILENCE_PREFIX kotlin.collections  SYNTHESIZE_TO_FILE_PREFIX kotlin.collections  Set kotlin.collections  TextToSpeech kotlin.collections  UUID kotlin.collections  Voice kotlin.collections  apply kotlin.collections  awaitSpeakCompletion kotlin.collections  awaitSynthCompletion kotlin.collections  closeParcelFileDescriptor kotlin.collections  indices kotlin.collections  invokeMethod kotlin.collections  isEmpty kotlin.collections  isPaused kotlin.collections  java kotlin.collections  	javaClass kotlin.collections  joinToString kotlin.collections  lastProgress kotlin.collections  let kotlin.collections  	pauseText kotlin.collections  plus kotlin.collections  	queueMode kotlin.collections  rangeTo kotlin.collections  releaseAudioFocus kotlin.collections  set kotlin.collections  speakCompletion kotlin.collections  speaking kotlin.collections  
startsWith kotlin.collections  	substring kotlin.collections  synchronized kotlin.collections  synth kotlin.collections  synthCompletion kotlin.collections  tag kotlin.collections  toFloat kotlin.collections  toInt kotlin.collections  
utterances kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  getJOINToString kotlin.collections.MutableSet  getJoinToString kotlin.collections.MutableSet  	ArrayList kotlin.comparisons  AudioAttributes kotlin.comparisons  AudioFocusRequest kotlin.comparisons  AudioManager kotlin.comparisons  Build kotlin.comparisons  Bundle kotlin.comparisons  
ContentValues kotlin.comparisons  Context kotlin.comparisons  Environment kotlin.comparisons  	Exception kotlin.comparisons  File kotlin.comparisons  Handler kotlin.comparisons  HashMap kotlin.comparisons  IllegalAccessException kotlin.comparisons  IllegalArgumentException kotlin.comparisons  Locale kotlin.comparisons  Log kotlin.comparisons  Looper kotlin.comparisons  
MediaStore kotlin.comparisons  
MethodChannel kotlin.comparisons  NullPointerException kotlin.comparisons  Runnable kotlin.comparisons  SILENCE_PREFIX kotlin.comparisons  SYNTHESIZE_TO_FILE_PREFIX kotlin.comparisons  TextToSpeech kotlin.comparisons  UUID kotlin.comparisons  Voice kotlin.comparisons  apply kotlin.comparisons  awaitSpeakCompletion kotlin.comparisons  awaitSynthCompletion kotlin.comparisons  closeParcelFileDescriptor kotlin.comparisons  indices kotlin.comparisons  invokeMethod kotlin.comparisons  isEmpty kotlin.comparisons  isPaused kotlin.comparisons  java kotlin.comparisons  	javaClass kotlin.comparisons  joinToString kotlin.comparisons  lastProgress kotlin.comparisons  let kotlin.comparisons  	pauseText kotlin.comparisons  plus kotlin.comparisons  	queueMode kotlin.comparisons  rangeTo kotlin.comparisons  releaseAudioFocus kotlin.comparisons  set kotlin.comparisons  speakCompletion kotlin.comparisons  speaking kotlin.comparisons  
startsWith kotlin.comparisons  	substring kotlin.comparisons  synchronized kotlin.comparisons  synth kotlin.comparisons  synthCompletion kotlin.comparisons  tag kotlin.comparisons  toFloat kotlin.comparisons  toInt kotlin.comparisons  
utterances kotlin.comparisons  	ArrayList 	kotlin.io  AudioAttributes 	kotlin.io  AudioFocusRequest 	kotlin.io  AudioManager 	kotlin.io  Build 	kotlin.io  Bundle 	kotlin.io  
ContentValues 	kotlin.io  Context 	kotlin.io  Environment 	kotlin.io  	Exception 	kotlin.io  File 	kotlin.io  Handler 	kotlin.io  HashMap 	kotlin.io  IllegalAccessException 	kotlin.io  IllegalArgumentException 	kotlin.io  Locale 	kotlin.io  Log 	kotlin.io  Looper 	kotlin.io  
MediaStore 	kotlin.io  
MethodChannel 	kotlin.io  NullPointerException 	kotlin.io  Runnable 	kotlin.io  SILENCE_PREFIX 	kotlin.io  SYNTHESIZE_TO_FILE_PREFIX 	kotlin.io  TextToSpeech 	kotlin.io  UUID 	kotlin.io  Voice 	kotlin.io  apply 	kotlin.io  awaitSpeakCompletion 	kotlin.io  awaitSynthCompletion 	kotlin.io  closeParcelFileDescriptor 	kotlin.io  indices 	kotlin.io  invokeMethod 	kotlin.io  isEmpty 	kotlin.io  isPaused 	kotlin.io  java 	kotlin.io  	javaClass 	kotlin.io  joinToString 	kotlin.io  lastProgress 	kotlin.io  let 	kotlin.io  	pauseText 	kotlin.io  plus 	kotlin.io  	queueMode 	kotlin.io  rangeTo 	kotlin.io  releaseAudioFocus 	kotlin.io  set 	kotlin.io  speakCompletion 	kotlin.io  speaking 	kotlin.io  
startsWith 	kotlin.io  	substring 	kotlin.io  synchronized 	kotlin.io  synth 	kotlin.io  synthCompletion 	kotlin.io  tag 	kotlin.io  toFloat 	kotlin.io  toInt 	kotlin.io  
utterances 	kotlin.io  	ArrayList 
kotlin.jvm  AudioAttributes 
kotlin.jvm  AudioFocusRequest 
kotlin.jvm  AudioManager 
kotlin.jvm  Build 
kotlin.jvm  Bundle 
kotlin.jvm  
ContentValues 
kotlin.jvm  Context 
kotlin.jvm  Environment 
kotlin.jvm  	Exception 
kotlin.jvm  File 
kotlin.jvm  Handler 
kotlin.jvm  HashMap 
kotlin.jvm  IllegalAccessException 
kotlin.jvm  IllegalArgumentException 
kotlin.jvm  Locale 
kotlin.jvm  Log 
kotlin.jvm  Looper 
kotlin.jvm  
MediaStore 
kotlin.jvm  
MethodChannel 
kotlin.jvm  NullPointerException 
kotlin.jvm  Runnable 
kotlin.jvm  SILENCE_PREFIX 
kotlin.jvm  SYNTHESIZE_TO_FILE_PREFIX 
kotlin.jvm  TextToSpeech 
kotlin.jvm  UUID 
kotlin.jvm  Voice 
kotlin.jvm  apply 
kotlin.jvm  awaitSpeakCompletion 
kotlin.jvm  awaitSynthCompletion 
kotlin.jvm  closeParcelFileDescriptor 
kotlin.jvm  indices 
kotlin.jvm  invokeMethod 
kotlin.jvm  isEmpty 
kotlin.jvm  isPaused 
kotlin.jvm  java 
kotlin.jvm  	javaClass 
kotlin.jvm  joinToString 
kotlin.jvm  lastProgress 
kotlin.jvm  let 
kotlin.jvm  	pauseText 
kotlin.jvm  plus 
kotlin.jvm  	queueMode 
kotlin.jvm  rangeTo 
kotlin.jvm  releaseAudioFocus 
kotlin.jvm  set 
kotlin.jvm  speakCompletion 
kotlin.jvm  speaking 
kotlin.jvm  
startsWith 
kotlin.jvm  	substring 
kotlin.jvm  synchronized 
kotlin.jvm  synth 
kotlin.jvm  synthCompletion 
kotlin.jvm  tag 
kotlin.jvm  toFloat 
kotlin.jvm  toInt 
kotlin.jvm  
utterances 
kotlin.jvm  	ArrayList 
kotlin.ranges  AudioAttributes 
kotlin.ranges  AudioFocusRequest 
kotlin.ranges  AudioManager 
kotlin.ranges  Build 
kotlin.ranges  Bundle 
kotlin.ranges  ClosedFloatingPointRange 
kotlin.ranges  
ContentValues 
kotlin.ranges  Context 
kotlin.ranges  Environment 
kotlin.ranges  	Exception 
kotlin.ranges  File 
kotlin.ranges  Handler 
kotlin.ranges  HashMap 
kotlin.ranges  IllegalAccessException 
kotlin.ranges  IllegalArgumentException 
kotlin.ranges  IntRange 
kotlin.ranges  Locale 
kotlin.ranges  Log 
kotlin.ranges  Looper 
kotlin.ranges  
MediaStore 
kotlin.ranges  
MethodChannel 
kotlin.ranges  NullPointerException 
kotlin.ranges  Runnable 
kotlin.ranges  SILENCE_PREFIX 
kotlin.ranges  SYNTHESIZE_TO_FILE_PREFIX 
kotlin.ranges  TextToSpeech 
kotlin.ranges  UUID 
kotlin.ranges  Voice 
kotlin.ranges  apply 
kotlin.ranges  awaitSpeakCompletion 
kotlin.ranges  awaitSynthCompletion 
kotlin.ranges  closeParcelFileDescriptor 
kotlin.ranges  indices 
kotlin.ranges  invokeMethod 
kotlin.ranges  isEmpty 
kotlin.ranges  isPaused 
kotlin.ranges  java 
kotlin.ranges  	javaClass 
kotlin.ranges  joinToString 
kotlin.ranges  lastProgress 
kotlin.ranges  let 
kotlin.ranges  	pauseText 
kotlin.ranges  plus 
kotlin.ranges  	queueMode 
kotlin.ranges  rangeTo 
kotlin.ranges  releaseAudioFocus 
kotlin.ranges  set 
kotlin.ranges  speakCompletion 
kotlin.ranges  speaking 
kotlin.ranges  
startsWith 
kotlin.ranges  	substring 
kotlin.ranges  synchronized 
kotlin.ranges  synth 
kotlin.ranges  synthCompletion 
kotlin.ranges  tag 
kotlin.ranges  toFloat 
kotlin.ranges  toInt 
kotlin.ranges  
utterances 
kotlin.ranges  contains &kotlin.ranges.ClosedFloatingPointRange  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  	ArrayList kotlin.sequences  AudioAttributes kotlin.sequences  AudioFocusRequest kotlin.sequences  AudioManager kotlin.sequences  Build kotlin.sequences  Bundle kotlin.sequences  
ContentValues kotlin.sequences  Context kotlin.sequences  Environment kotlin.sequences  	Exception kotlin.sequences  File kotlin.sequences  Handler kotlin.sequences  HashMap kotlin.sequences  IllegalAccessException kotlin.sequences  IllegalArgumentException kotlin.sequences  Locale kotlin.sequences  Log kotlin.sequences  Looper kotlin.sequences  
MediaStore kotlin.sequences  
MethodChannel kotlin.sequences  NullPointerException kotlin.sequences  Runnable kotlin.sequences  SILENCE_PREFIX kotlin.sequences  SYNTHESIZE_TO_FILE_PREFIX kotlin.sequences  TextToSpeech kotlin.sequences  UUID kotlin.sequences  Voice kotlin.sequences  apply kotlin.sequences  awaitSpeakCompletion kotlin.sequences  awaitSynthCompletion kotlin.sequences  closeParcelFileDescriptor kotlin.sequences  indices kotlin.sequences  invokeMethod kotlin.sequences  isEmpty kotlin.sequences  isPaused kotlin.sequences  java kotlin.sequences  	javaClass kotlin.sequences  joinToString kotlin.sequences  lastProgress kotlin.sequences  let kotlin.sequences  	pauseText kotlin.sequences  plus kotlin.sequences  	queueMode kotlin.sequences  rangeTo kotlin.sequences  releaseAudioFocus kotlin.sequences  set kotlin.sequences  speakCompletion kotlin.sequences  speaking kotlin.sequences  
startsWith kotlin.sequences  	substring kotlin.sequences  synchronized kotlin.sequences  synth kotlin.sequences  synthCompletion kotlin.sequences  tag kotlin.sequences  toFloat kotlin.sequences  toInt kotlin.sequences  
utterances kotlin.sequences  	ArrayList kotlin.text  AudioAttributes kotlin.text  AudioFocusRequest kotlin.text  AudioManager kotlin.text  Build kotlin.text  Bundle kotlin.text  
ContentValues kotlin.text  Context kotlin.text  Environment kotlin.text  	Exception kotlin.text  File kotlin.text  Handler kotlin.text  HashMap kotlin.text  IllegalAccessException kotlin.text  IllegalArgumentException kotlin.text  Locale kotlin.text  Log kotlin.text  Looper kotlin.text  
MediaStore kotlin.text  
MethodChannel kotlin.text  NullPointerException kotlin.text  Runnable kotlin.text  SILENCE_PREFIX kotlin.text  SYNTHESIZE_TO_FILE_PREFIX kotlin.text  TextToSpeech kotlin.text  UUID kotlin.text  Voice kotlin.text  apply kotlin.text  awaitSpeakCompletion kotlin.text  awaitSynthCompletion kotlin.text  closeParcelFileDescriptor kotlin.text  indices kotlin.text  invokeMethod kotlin.text  isEmpty kotlin.text  isPaused kotlin.text  java kotlin.text  	javaClass kotlin.text  joinToString kotlin.text  lastProgress kotlin.text  let kotlin.text  	pauseText kotlin.text  plus kotlin.text  	queueMode kotlin.text  rangeTo kotlin.text  releaseAudioFocus kotlin.text  set kotlin.text  speakCompletion kotlin.text  speaking kotlin.text  
startsWith kotlin.text  	substring kotlin.text  synchronized kotlin.text  synth kotlin.text  synthCompletion kotlin.text  tag kotlin.text  toFloat kotlin.text  toInt kotlin.text  
utterances kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 