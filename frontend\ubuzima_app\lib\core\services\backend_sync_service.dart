import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/health_record_model.dart';
import '../models/appointment_model.dart';
import 'http_client.dart';
import 'database_service.dart';

class BackendSyncService {
  static final BackendSyncService _instance = BackendSyncService._internal();
  factory BackendSyncService() => _instance;
  BackendSyncService._internal();

  final HttpClient _httpClient = HttpClient();
  final DatabaseService _databaseService = DatabaseService();
  bool _isSyncing = false;

  // Check if device is online
  Future<bool> isOnline() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  // Sync all data with backend
  Future<SyncResult> syncAll() async {
    if (_isSyncing) {
      return SyncResult.failure('Sync already in progress');
    }

    if (!await isOnline()) {
      return SyncResult.failure('No internet connection');
    }

    _isSyncing = true;
    
    try {
      // Check backend health
      final isBackendHealthy = await _httpClient.checkBackendHealth();
      if (!isBackendHealthy) {
        return SyncResult.failure('Backend server is not available');
      }

      int syncedItems = 0;
      
      // Sync health records
      syncedItems += await _syncHealthRecords();
      
      // Sync appointments
      syncedItems += await _syncAppointments();
      
      // Sync messages
      syncedItems += await _syncMessages();
      
      // Sync user profile
      await _syncUserProfile();
      
      // Update last sync time
      await _updateLastSyncTime();
      
      return SyncResult.success(syncedItems);
      
    } catch (e) {
      debugPrint('Sync error: $e');
      return SyncResult.failure('Sync failed: ${e.toString()}');
    } finally {
      _isSyncing = false;
    }
  }

  // Sync health records
  Future<int> _syncHealthRecords() async {
    int syncedCount = 0;
    
    try {
      // Get local health records that need syncing
      final localRecords = await _databaseService.getUnsyncedHealthRecords();
      
      // Upload local records to backend
      for (final record in localRecords) {
        try {
          final response = await _httpClient.post('/health-records', 
            data: record.toJson());
          
          if (response.statusCode == 201) {
            // Update local record with server ID
            final serverRecord = HealthRecord.fromJson(response.data);
            await _databaseService.updateHealthRecordSyncStatus(
              record.id, serverRecord.id, true);
            syncedCount++;
          }
        } catch (e) {
          debugPrint('Failed to sync health record ${record.id}: $e');
        }
      }
      
      // Download new records from backend
      final response = await _httpClient.get('/health-records');
      if (response.statusCode == 200) {
        final serverRecords = (response.data as List)
            .map((json) => HealthRecord.fromJson(json))
            .toList();
        
        for (final record in serverRecords) {
          await _databaseService.insertOrUpdateHealthRecord(record);
          syncedCount++;
        }
      }
      
    } catch (e) {
      debugPrint('Health records sync error: $e');
    }
    
    return syncedCount;
  }

  // Sync appointments
  Future<int> _syncAppointments() async {
    int syncedCount = 0;
    
    try {
      // Get local appointments that need syncing
      final localAppointments = await _databaseService.getUnsyncedAppointments();
      
      // Upload local appointments to backend
      for (final appointment in localAppointments) {
        try {
          final response = await _httpClient.post('/appointments', 
            data: appointment.toJson());
          
          if (response.statusCode == 201) {
            // Update local appointment with server ID
            final serverAppointment = Appointment.fromJson(response.data);
            await _databaseService.updateAppointmentSyncStatus(
              appointment.id, serverAppointment.id, true);
            syncedCount++;
          }
        } catch (e) {
          debugPrint('Failed to sync appointment ${appointment.id}: $e');
        }
      }
      
      // Download new appointments from backend
      final response = await _httpClient.get('/appointments');
      if (response.statusCode == 200) {
        final serverAppointments = (response.data as List)
            .map((json) => Appointment.fromJson(json))
            .toList();
        
        for (final appointment in serverAppointments) {
          await _databaseService.insertOrUpdateAppointment(appointment);
          syncedCount++;
        }
      }
      
    } catch (e) {
      debugPrint('Appointments sync error: $e');
    }
    
    return syncedCount;
  }

  // Sync messages
  Future<int> _syncMessages() async {
    int syncedCount = 0;
    
    try {
      // Get local messages that need syncing
      final localMessages = await _databaseService.getUnsyncedMessages();
      
      // Upload local messages to backend
      for (final message in localMessages) {
        try {
          final response = await _httpClient.post('/messages', 
            data: message);
          
          if (response.statusCode == 201) {
            // Update local message sync status
            await _databaseService.updateMessageSyncStatus(
              message['id'], true);
            syncedCount++;
          }
        } catch (e) {
          debugPrint('Failed to sync message ${message['id']}: $e');
        }
      }
      
      // Download new messages from backend
      final response = await _httpClient.get('/messages');
      if (response.statusCode == 200) {
        final serverMessages = response.data as List;
        
        for (final message in serverMessages) {
          await _databaseService.insertOrUpdateMessage(message);
          syncedCount++;
        }
      }
      
    } catch (e) {
      debugPrint('Messages sync error: $e');
    }
    
    return syncedCount;
  }

  // Sync user profile
  Future<void> _syncUserProfile() async {
    try {
      final response = await _httpClient.get('/users/profile');
      if (response.statusCode == 200) {
        final userProfile = response.data;
        await _databaseService.updateUserProfile(userProfile);
      }
    } catch (e) {
      debugPrint('User profile sync error: $e');
    }
  }

  // Update last sync time
  Future<void> _updateLastSyncTime() async {
    await _databaseService.updateLastSyncTime(DateTime.now());
  }

  // Get last sync time
  Future<DateTime?> getLastSyncTime() async {
    return await _databaseService.getLastSyncTime();
  }

  // Force sync specific data type
  Future<bool> syncHealthRecords() async {
    if (!await isOnline()) return false;
    
    try {
      final count = await _syncHealthRecords();
      return count > 0;
    } catch (e) {
      debugPrint('Health records sync error: $e');
      return false;
    }
  }

  Future<bool> syncAppointments() async {
    if (!await isOnline()) return false;
    
    try {
      final count = await _syncAppointments();
      return count > 0;
    } catch (e) {
      debugPrint('Appointments sync error: $e');
      return false;
    }
  }

  // Queue data for sync when offline
  Future<void> queueForSync(String dataType, Map<String, dynamic> data) async {
    await _databaseService.addToSyncQueue(dataType, data);
  }

  // Process sync queue when back online
  Future<void> processSyncQueue() async {
    if (!await isOnline()) return;
    
    final queueItems = await _databaseService.getSyncQueue();
    
    for (final item in queueItems) {
      try {
        final endpoint = _getEndpointForDataType(item['data_type']);
        final response = await _httpClient.post(endpoint, data: item['data']);
        
        if (response.statusCode == 201) {
          await _databaseService.removeSyncQueueItem(item['id']);
        }
      } catch (e) {
        debugPrint('Failed to process sync queue item ${item['id']}: $e');
      }
    }
  }

  String _getEndpointForDataType(String dataType) {
    switch (dataType) {
      case 'health_record':
        return '/health-records';
      case 'appointment':
        return '/appointments';
      case 'message':
        return '/messages';
      default:
        throw Exception('Unknown data type: $dataType');
    }
  }
}

// Sync result class
class SyncResult {
  final bool isSuccess;
  final int? syncedItems;
  final String? error;

  SyncResult.success(this.syncedItems) : isSuccess = true, error = null;
  SyncResult.failure(this.error) : isSuccess = false, syncedItems = null;
}
