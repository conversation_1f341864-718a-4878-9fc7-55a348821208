import 'dart:async';
import 'package:flutter/foundation.dart';
import 'real_data_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Backend Sync Service for Ubuzima App
/// Provides sync functionality for offline-first architecture
class BackendSyncService {
  static final BackendSyncService _instance = BackendSyncService._internal();
  factory BackendSyncService() => _instance;
  BackendSyncService._internal();

  bool _isSyncing = false;
  DateTime? _lastSyncTime;
  final StreamController<bool> _syncStatusController =
      StreamController<bool>.broadcast();

  final RealDataService _realDataService = RealDataService();

  // Getters
  bool get isSyncing => _isSyncing;
  DateTime? get lastSyncTime => _lastSyncTime;
  Stream<bool> get syncStatusStream => _syncStatusController.stream;

  /// Check if device is online
  Future<bool> isOnline() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      debugPrint('Connectivity check failed: $e');
      return false;
    }
  }

  /// Initialize real data service
  Future<bool> initializeRealData() async {
    try {
      final success = await _realDataService.initialize();
      if (success) {
        debugPrint('✅ Real data service connected to PostgreSQL');
      } else {
        debugPrint('❌ Real data service connection failed');
      }
      return success;
    } catch (e) {
      debugPrint('❌ Real data service initialization error: $e');
      return false;
    }
  }

  /// Start full sync process
  Future<void> startSync() async {
    if (_isSyncing) {
      debugPrint('Sync already in progress');
      return;
    }

    if (!await isOnline()) {
      debugPrint('Device is offline, skipping sync');
      return;
    }

    _isSyncing = true;
    _syncStatusController.add(true);

    try {
      debugPrint('🔄 Starting backend sync...');

      // Simulate sync operations
      await _syncHealthRecords();
      await _syncAppointments();
      await _syncMessages();
      await _syncUserProfile();

      _lastSyncTime = DateTime.now();
      debugPrint('✅ Backend sync completed successfully');
    } catch (e) {
      debugPrint('❌ Backend sync failed: $e');
    } finally {
      _isSyncing = false;
      _syncStatusController.add(false);
    }
  }

  /// Sync health records (placeholder)
  Future<void> _syncHealthRecords() async {
    debugPrint('📊 Syncing health records...');
    await Future.delayed(const Duration(milliseconds: 500));
    debugPrint('✅ Health records synced');
  }

  /// Sync appointments (placeholder)
  Future<void> _syncAppointments() async {
    debugPrint('📅 Syncing appointments...');
    await Future.delayed(const Duration(milliseconds: 300));
    debugPrint('✅ Appointments synced');
  }

  /// Sync messages (placeholder)
  Future<void> _syncMessages() async {
    debugPrint('💬 Syncing messages...');
    await Future.delayed(const Duration(milliseconds: 400));
    debugPrint('✅ Messages synced');
  }

  /// Sync user profile (placeholder)
  Future<void> _syncUserProfile() async {
    debugPrint('👤 Syncing user profile...');
    await Future.delayed(const Duration(milliseconds: 200));
    debugPrint('✅ User profile synced');
  }

  /// Queue data for sync when offline
  Future<void> queueForSync(String dataType, Map<String, dynamic> data) async {
    debugPrint('📝 Queued $dataType for sync: ${data.keys.join(', ')}');
    // In a real implementation, this would store data locally for later sync
  }

  /// Process sync queue when back online
  Future<void> processSyncQueue() async {
    if (!await isOnline()) return;

    debugPrint('🔄 Processing sync queue...');
    // In a real implementation, this would process queued items
    await Future.delayed(const Duration(milliseconds: 100));
    debugPrint('✅ Sync queue processed');
  }

  /// Get sync status information
  Map<String, dynamic> getSyncStatus() {
    return {
      'is_syncing': _isSyncing,
      'last_sync_time': _lastSyncTime?.toIso8601String(),
      'is_online': false, // Will be updated by connectivity check
    };
  }

  /// Force sync for specific data type
  Future<void> forceSyncDataType(String dataType) async {
    if (!await isOnline()) {
      debugPrint('Cannot force sync $dataType - device offline');
      return;
    }

    debugPrint('🔄 Force syncing $dataType...');

    switch (dataType) {
      case 'health_records':
        await _syncHealthRecords();
        break;
      case 'appointments':
        await _syncAppointments();
        break;
      case 'messages':
        await _syncMessages();
        break;
      case 'user_profile':
        await _syncUserProfile();
        break;
      default:
        debugPrint('Unknown data type: $dataType');
    }
  }

  /// Schedule periodic sync
  Timer? _syncTimer;

  void startPeriodicSync({Duration interval = const Duration(minutes: 15)}) {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(interval, (_) {
      if (!_isSyncing) {
        startSync();
      }
    });
    debugPrint(
      '📅 Periodic sync scheduled every ${interval.inMinutes} minutes',
    );
  }

  void stopPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = null;
    debugPrint('⏹️ Periodic sync stopped');
  }

  /// Cleanup resources
  void dispose() {
    _syncTimer?.cancel();
    _syncStatusController.close();
  }
}
