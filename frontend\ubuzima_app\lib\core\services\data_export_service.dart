import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:csv/csv.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/health_record_model.dart';
import '../models/appointment_model.dart';
import '../models/user_model.dart';
import 'database_service.dart';

/// Comprehensive data export service for Ubuzima app
/// Handles PDF reports, CSV exports, and data backup
class DataExportService extends ChangeNotifier {
  static final DataExportService _instance = DataExportService._internal();
  factory DataExportService() => _instance;
  DataExportService._internal();

  final DatabaseService _databaseService = DatabaseService();
  
  // Export state
  bool _isExporting = false;
  double _exportProgress = 0.0;
  String? _lastError;

  // Getters
  bool get isExporting => _isExporting;
  double get exportProgress => _exportProgress;
  String? get lastError => _lastError;

  /// Export health records to PDF
  Future<Uint8List?> exportHealthRecordsToPDF({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
    List<HealthRecordType>? recordTypes,
  }) async {
    try {
      _setExportState(true, 0.0, null);

      // Get user data
      final user = await _getUserData(userId);
      if (user == null) {
        throw Exception('User not found');
      }

      _setExportState(true, 0.2, null);

      // Get health records
      final records = await _getHealthRecords(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
        recordTypes: recordTypes,
      );

      _setExportState(true, 0.5, null);

      // Create PDF document
      final pdf = pw.Document();

      // Add cover page
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return _buildCoverPage(user, startDate, endDate);
          },
        ),
      );

      _setExportState(true, 0.7, null);

      // Add health records pages
      if (records.isNotEmpty) {
        pdf.addPage(
          pw.MultiPage(
            pageFormat: PdfPageFormat.a4,
            build: (pw.Context context) {
              return _buildHealthRecordsPages(records);
            },
          ),
        );
      }

      _setExportState(true, 0.9, null);

      // Generate PDF bytes
      final pdfBytes = await pdf.save();

      _setExportState(false, 1.0, null);
      debugPrint('✅ Health records PDF exported successfully');
      return pdfBytes;

    } catch (e) {
      _setExportState(false, 0.0, e.toString());
      debugPrint('❌ Health records PDF export failed: $e');
      return null;
    }
  }

  /// Export health records to CSV
  Future<String?> exportHealthRecordsToCSV({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
    List<HealthRecordType>? recordTypes,
  }) async {
    try {
      _setExportState(true, 0.0, null);

      // Get health records
      final records = await _getHealthRecords(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
        recordTypes: recordTypes,
      );

      _setExportState(true, 0.5, null);

      // Create CSV data
      final csvData = <List<String>>[];

      // Add headers
      csvData.add([
        'Date',
        'Type',
        'Value',
        'Unit',
        'Notes',
        'Created At',
      ]);

      // Add records
      for (final record in records) {
        csvData.add([
          record.recordedAt.toString().split(' ')[0], // Date only
          record.type.name,
          record.value?.toString() ?? '',
          record.unit ?? '',
          record.notes ?? '',
          record.createdAt?.toString() ?? '',
        ]);
      }

      _setExportState(true, 0.9, null);

      // Convert to CSV string
      final csvString = const ListToCsvConverter().convert(csvData);

      _setExportState(false, 1.0, null);
      debugPrint('✅ Health records CSV exported successfully');
      return csvString;

    } catch (e) {
      _setExportState(false, 0.0, e.toString());
      debugPrint('❌ Health records CSV export failed: $e');
      return null;
    }
  }

  /// Export appointments to PDF
  Future<Uint8List?> exportAppointmentsToPDF({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      _setExportState(true, 0.0, null);

      // Get user data
      final user = await _getUserData(userId);
      if (user == null) {
        throw Exception('User not found');
      }

      _setExportState(true, 0.2, null);

      // Get appointments
      final appointments = await _getAppointments(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );

      _setExportState(true, 0.5, null);

      // Create PDF document
      final pdf = pw.Document();

      // Add appointments page
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return _buildAppointmentsPages(user, appointments);
          },
        ),
      );

      _setExportState(true, 0.9, null);

      // Generate PDF bytes
      final pdfBytes = await pdf.save();

      _setExportState(false, 1.0, null);
      debugPrint('✅ Appointments PDF exported successfully');
      return pdfBytes;

    } catch (e) {
      _setExportState(false, 0.0, e.toString());
      debugPrint('❌ Appointments PDF export failed: $e');
      return null;
    }
  }

  /// Export complete user data backup
  Future<String?> exportCompleteDataBackup({
    required String userId,
  }) async {
    try {
      _setExportState(true, 0.0, null);

      // Get all user data
      final user = await _getUserData(userId);
      final healthRecords = await _getHealthRecords(userId: userId);
      final appointments = await _getAppointments(userId: userId);

      _setExportState(true, 0.5, null);

      // Create backup data structure
      final backupData = {
        'exportDate': DateTime.now().toIso8601String(),
        'version': '1.0',
        'user': user?.toMap(),
        'healthRecords': healthRecords.map((r) => r.toMap()).toList(),
        'appointments': appointments.map((a) => a.toMap()).toList(),
        'metadata': {
          'totalHealthRecords': healthRecords.length,
          'totalAppointments': appointments.length,
          'dateRange': {
            'earliest': healthRecords.isNotEmpty 
                ? healthRecords.map((r) => r.recordedAt).reduce((a, b) => a.isBefore(b) ? a : b).toIso8601String()
                : null,
            'latest': healthRecords.isNotEmpty 
                ? healthRecords.map((r) => r.recordedAt).reduce((a, b) => a.isAfter(b) ? a : b).toIso8601String()
                : null,
          },
        },
      };

      _setExportState(true, 0.9, null);

      // Convert to JSON string
      final jsonString = const JsonEncoder.withIndent('  ').convert(backupData);

      _setExportState(false, 1.0, null);
      debugPrint('✅ Complete data backup exported successfully');
      return jsonString;

    } catch (e) {
      _setExportState(false, 0.0, e.toString());
      debugPrint('❌ Complete data backup export failed: $e');
      return null;
    }
  }

  /// Generate health summary report
  Future<Uint8List?> generateHealthSummaryReport({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      _setExportState(true, 0.0, null);

      // Get user data
      final user = await _getUserData(userId);
      if (user == null) {
        throw Exception('User not found');
      }

      _setExportState(true, 0.2, null);

      // Get health records
      final records = await _getHealthRecords(
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );

      _setExportState(true, 0.5, null);

      // Calculate statistics
      final stats = _calculateHealthStatistics(records);

      _setExportState(true, 0.7, null);

      // Create PDF document
      final pdf = pw.Document();

      // Add summary page
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return _buildHealthSummaryPage(user, stats, startDate, endDate);
          },
        ),
      );

      _setExportState(true, 0.9, null);

      // Generate PDF bytes
      final pdfBytes = await pdf.save();

      _setExportState(false, 1.0, null);
      debugPrint('✅ Health summary report generated successfully');
      return pdfBytes;

    } catch (e) {
      _setExportState(false, 0.0, e.toString());
      debugPrint('❌ Health summary report generation failed: $e');
      return null;
    }
  }

  /// Build PDF cover page
  pw.Widget _buildCoverPage(User user, DateTime? startDate, DateTime? endDate) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Header(
          level: 0,
          child: pw.Text(
            'Ubuzima Health Records',
            style: pw.TextStyle(
              fontSize: 24,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ),
        pw.SizedBox(height: 20),
        pw.Text('Patient: ${user.name}'),
        pw.Text('Email: ${user.email}'),
        if (user.phone != null) pw.Text('Phone: ${user.phone}'),
        pw.SizedBox(height: 20),
        if (startDate != null || endDate != null) ...[
          pw.Text('Report Period:'),
          if (startDate != null) pw.Text('From: ${startDate.toString().split(' ')[0]}'),
          if (endDate != null) pw.Text('To: ${endDate.toString().split(' ')[0]}'),
          pw.SizedBox(height: 20),
        ],
        pw.Text('Generated: ${DateTime.now().toString().split(' ')[0]}'),
        pw.SizedBox(height: 40),
        pw.Text(
          'This report contains confidential health information. Please handle with care.',
          style: pw.TextStyle(
            fontSize: 10,
            fontStyle: pw.FontStyle.italic,
          ),
        ),
      ],
    );
  }

  /// Build health records pages
  List<pw.Widget> _buildHealthRecordsPages(List<HealthRecord> records) {
    final widgets = <pw.Widget>[];

    widgets.add(
      pw.Header(
        level: 1,
        child: pw.Text('Health Records'),
      ),
    );

    // Group records by type
    final groupedRecords = <HealthRecordType, List<HealthRecord>>{};
    for (final record in records) {
      groupedRecords.putIfAbsent(record.type, () => []).add(record);
    }

    // Add each group
    for (final entry in groupedRecords.entries) {
      widgets.add(pw.SizedBox(height: 20));
      widgets.add(
        pw.Header(
          level: 2,
          child: pw.Text(entry.key.name.toUpperCase()),
        ),
      );

      // Create table for this record type
      final tableData = <List<String>>[];
      tableData.add(['Date', 'Value', 'Unit', 'Notes']);

      for (final record in entry.value) {
        tableData.add([
          record.recordedAt.toString().split(' ')[0],
          record.value?.toString() ?? '',
          record.unit ?? '',
          record.notes ?? '',
        ]);
      }

      widgets.add(
        pw.Table.fromTextArray(
          data: tableData,
          headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
          cellAlignment: pw.Alignment.centerLeft,
        ),
      );
    }

    return widgets;
  }

  /// Build appointments pages
  List<pw.Widget> _buildAppointmentsPages(User user, List<Appointment> appointments) {
    final widgets = <pw.Widget>[];

    widgets.add(
      pw.Header(
        level: 1,
        child: pw.Text('Appointments for ${user.name}'),
      ),
    );

    if (appointments.isEmpty) {
      widgets.add(pw.Text('No appointments found.'));
      return widgets;
    }

    // Create appointments table
    final tableData = <List<String>>[];
    tableData.add(['Date', 'Time', 'Type', 'Status', 'Notes']);

    for (final appointment in appointments) {
      final dateTime = appointment.appointmentDate;
      tableData.add([
        dateTime.toString().split(' ')[0],
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}',
        appointment.type,
        appointment.status,
        appointment.notes ?? '',
      ]);
    }

    widgets.add(
      pw.Table.fromTextArray(
        data: tableData,
        headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
        cellAlignment: pw.Alignment.centerLeft,
      ),
    );

    return widgets;
  }

  /// Build health summary page
  pw.Widget _buildHealthSummaryPage(
    User user,
    Map<String, dynamic> stats,
    DateTime? startDate,
    DateTime? endDate,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Header(
          level: 0,
          child: pw.Text('Health Summary Report'),
        ),
        pw.SizedBox(height: 20),
        pw.Text('Patient: ${user.name}'),
        pw.SizedBox(height: 20),
        pw.Text('Summary Statistics:', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
        pw.SizedBox(height: 10),
        ...stats.entries.map((entry) => pw.Text('${entry.key}: ${entry.value}')),
      ],
    );
  }

  /// Calculate health statistics
  Map<String, dynamic> _calculateHealthStatistics(List<HealthRecord> records) {
    final stats = <String, dynamic>{};

    // Group by type
    final groupedRecords = <HealthRecordType, List<HealthRecord>>{};
    for (final record in records) {
      groupedRecords.putIfAbsent(record.type, () => []).add(record);
    }

    // Calculate stats for each type
    for (final entry in groupedRecords.entries) {
      final values = entry.value
          .where((r) => r.value != null)
          .map((r) => r.value!)
          .toList();

      if (values.isNotEmpty) {
        final avg = values.reduce((a, b) => a + b) / values.length;
        final min = values.reduce((a, b) => a < b ? a : b);
        final max = values.reduce((a, b) => a > b ? a : b);

        stats['${entry.key.name}_average'] = avg.toStringAsFixed(2);
        stats['${entry.key.name}_minimum'] = min.toStringAsFixed(2);
        stats['${entry.key.name}_maximum'] = max.toStringAsFixed(2);
        stats['${entry.key.name}_count'] = values.length;
      }
    }

    stats['total_records'] = records.length;
    stats['date_range'] = records.isNotEmpty
        ? '${records.map((r) => r.recordedAt).reduce((a, b) => a.isBefore(b) ? a : b).toString().split(' ')[0]} - ${records.map((r) => r.recordedAt).reduce((a, b) => a.isAfter(b) ? a : b).toString().split(' ')[0]}'
        : 'No records';

    return stats;
  }

  /// Get user data
  Future<User?> _getUserData(String userId) async {
    try {
      final userData = await _databaseService.query(
        'users',
        where: 'id = ?',
        whereArgs: [userId],
      );

      if (userData.isNotEmpty) {
        return User.fromMap(userData.first);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Failed to get user data: $e');
      return null;
    }
  }

  /// Get health records
  Future<List<HealthRecord>> _getHealthRecords({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
    List<HealthRecordType>? recordTypes,
  }) async {
    try {
      String where = 'user_id = ?';
      List<dynamic> whereArgs = [userId];

      if (startDate != null) {
        where += ' AND recorded_at >= ?';
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        where += ' AND recorded_at <= ?';
        whereArgs.add(endDate.toIso8601String());
      }

      if (recordTypes != null && recordTypes.isNotEmpty) {
        where += ' AND type IN (${recordTypes.map((_) => '?').join(',')})';
        whereArgs.addAll(recordTypes.map((t) => t.name));
      }

      final recordsData = await _databaseService.query(
        'health_records',
        where: where,
        whereArgs: whereArgs,
        orderBy: 'recorded_at DESC',
      );

      return recordsData.map((data) => HealthRecord.fromMap(data)).toList();
    } catch (e) {
      debugPrint('❌ Failed to get health records: $e');
      return [];
    }
  }

  /// Get appointments
  Future<List<Appointment>> _getAppointments({
    required String userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      String where = 'client_id = ?';
      List<dynamic> whereArgs = [userId];

      if (startDate != null) {
        where += ' AND appointment_date >= ?';
        whereArgs.add(startDate.toIso8601String());
      }

      if (endDate != null) {
        where += ' AND appointment_date <= ?';
        whereArgs.add(endDate.toIso8601String());
      }

      final appointmentsData = await _databaseService.query(
        'appointments',
        where: where,
        whereArgs: whereArgs,
        orderBy: 'appointment_date DESC',
      );

      return appointmentsData.map((data) => Appointment.fromMap(data)).toList();
    } catch (e) {
      debugPrint('❌ Failed to get appointments: $e');
      return [];
    }
  }

  /// Set export state
  void _setExportState(bool isExporting, double progress, String? error) {
    _isExporting = isExporting;
    _exportProgress = progress;
    _lastError = error;
    notifyListeners();
  }
}
