import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../config/ai_config.dart';

class GeminiAIService {
  final Dio _dio = Dio();

  GeminiAIService() {
    _dio.options.connectTimeout = AIConfig.connectTimeout;
    _dio.options.receiveTimeout = AIConfig.receiveTimeout;
  }

  /// Get health advice from Gemini AI
  Future<String> getHealthAdvice(
    String question, {
    String language = 'kinyarwanda',
  }) async {
    try {
      final prompt = _buildHealthPrompt(question, language);
      final response = await _makeRequest(prompt);
      return _extractResponse(response);
    } catch (e) {
      debugPrint('Gemini AI Error: $e');
      return _getErrorMessage(language);
    }
  }

  /// Analyze symptoms and provide recommendations
  Future<Map<String, dynamic>> analyzeSymptoms(
    List<String> symptoms, {
    String language = 'kinyarwanda',
  }) async {
    try {
      final prompt = _buildSymptomsPrompt(symptoms, language);
      final response = await _makeRequest(prompt);
      final aiResponse = _extractResponse(response);

      return {
        'analysis': aiResponse,
        'recommendations': _parseRecommendations(aiResponse),
        'urgency': _assessUrgency(symptoms),
      };
    } catch (e) {
      debugPrint('Symptoms Analysis Error: $e');
      return {
        'analysis': _getErrorMessage(language),
        'recommendations': <String>[],
        'urgency': 'low',
      };
    }
  }

  /// Get contraceptive advice based on user profile
  Future<String> getContraceptiveAdvice(
    String method,
    Map<String, dynamic> userProfile, {
    String language = 'kinyarwanda',
  }) async {
    try {
      final prompt = _buildContraceptivePrompt(method, userProfile, language);
      final response = await _makeRequest(prompt);
      return _extractResponse(response);
    } catch (e) {
      debugPrint('Contraceptive Advice Error: $e');
      return _getErrorMessage(language);
    }
  }

  /// Get family planning guidance
  Future<String> getFamilyPlanningGuidance(
    Map<String, dynamic> context, {
    String language = 'kinyarwanda',
  }) async {
    try {
      final prompt = _buildFamilyPlanningPrompt(context, language);
      final response = await _makeRequest(prompt);
      return _extractResponse(response);
    } catch (e) {
      debugPrint('Family Planning Guidance Error: $e');
      return _getErrorMessage(language);
    }
  }

  /// Get pregnancy planning advice
  Future<String> getPregnancyPlanningAdvice(
    Map<String, dynamic> planningData, {
    String language = 'kinyarwanda',
  }) async {
    try {
      final prompt = _buildPregnancyPlanningPrompt(planningData, language);
      final response = await _makeRequest(prompt);
      return _extractResponse(response);
    } catch (e) {
      debugPrint('Pregnancy Planning Error: $e');
      return _getErrorMessage(language);
    }
  }

  /// Make HTTP request to Gemini API
  Future<Response> _makeRequest(String prompt) async {
    final response = await _dio.post(
      '${AIConfig.geminiBaseUrl}?key=${AIConfig.apiKey}',
      options: Options(headers: {'Content-Type': 'application/json'}),
      data: {
        'contents': [
          {
            'parts': [
              {'text': prompt},
            ],
          },
        ],
        'generationConfig': {
          'temperature': AIConfig.temperature,
          'topK': AIConfig.topK,
          'topP': AIConfig.topP,
          'maxOutputTokens': AIConfig.maxTokens,
        },
        'safetySettings': AIConfig.safetySettings,
      },
    );
    return response;
  }

  /// Extract response text from Gemini API response
  String _extractResponse(Response response) {
    try {
      final data = response.data;
      if (data['candidates'] != null && data['candidates'].isNotEmpty) {
        final candidate = data['candidates'][0];
        if (candidate['content'] != null &&
            candidate['content']['parts'] != null) {
          final parts = candidate['content']['parts'];
          if (parts.isNotEmpty && parts[0]['text'] != null) {
            return parts[0]['text'].toString().trim();
          }
        }
      }
      return 'Ntabwo nashoboye gusubiza. Ongera ugerageze.';
    } catch (e) {
      debugPrint('Response extraction error: $e');
      return 'Ikosa ryabaye mu gusoma igisubizo.';
    }
  }

  /// Build health advice prompt
  String _buildHealthPrompt(String question, String language) {
    final languageInstruction =
        language == 'kinyarwanda'
            ? 'Subiza mu Kinyarwanda cyangwa mu Cyongereza bitewe n\'ikibazo.'
            : language == 'french'
            ? 'Répondez en français ou en anglais selon la question.'
            : 'Respond in English or the appropriate language.';

    return '''
Wowe uri umujyanama w'ubuzima bw'ababyeyi mu cyaro cya Rwanda. Ufite ubumenyi bw'ibanze ku buzima bw'abagore n'abagabo, gukingira inda, no kubana neza mu muryango.

$languageInstruction

Ikibazo: $question

Tanga inama:
1. Z'ukuri kandi z'ubwoba
2. Z'ubushobozi bw'abantu bo mu cyaro
3. Z'ubwubahane bw'umuco w'u Rwanda
4. Z'ingenzi ku buzima

Ntukavuge ko uri muganga. Saba abantu bajye ku bitaro iyo bikenewe.
''';
  }

  /// Build symptoms analysis prompt
  String _buildSymptomsPrompt(List<String> symptoms, String language) {
    final symptomsText = symptoms.join(', ');
    final languageInstruction =
        language == 'kinyarwanda'
            ? 'Subiza mu Kinyarwanda.'
            : language == 'french'
            ? 'Répondez en français.'
            : 'Respond in English.';

    return '''
Nk'umujyanama w'ubuzima mu cyaro cya Rwanda, suzuma ibi bimenyetso:

Ibimenyetso: $symptomsText

$languageInstruction

Tanga:
1. Icyo bishobora kuba (ntukavuge ko ari indwara runaka)
2. Inama z'ubwoba zo kwikingira
3. Igihe cyo kujya ku bitaro
4. Ibintu by'ingenzi byo kwirinda

Wibuke ko utari muganga. Saba umuntu ajye ku bitaro iyo bibaye ngombwa.
''';
  }

  /// Build contraceptive advice prompt
  String _buildContraceptivePrompt(
    String method,
    Map<String, dynamic> userProfile,
    String language,
  ) {
    final age = userProfile['age'] ?? 'unknown';
    final languageInstruction =
        language == 'kinyarwanda'
            ? 'Subiza mu Kinyarwanda.'
            : language == 'french'
            ? 'Répondez en français.'
            : 'Respond in English.';

    return '''
Umukobwa cyangwa umugabo w'imyaka $age abaza ku buryo bwo kurinda inda: $method

$languageInstruction

Mumuhe amakuru:
1. Uburyo bukora bute
2. Ingaruka nziza
3. Ingaruka zishobora kubaho
4. Inama z'ingenzi zo gukoresha
5. Aho bashobora kubona ubufasha

Ntukavuge ko uri muganga. Saba ajye ku bitaro kugira ngo abone inama z'umuganga.
''';
  }

  /// Build family planning prompt
  String _buildFamilyPlanningPrompt(
    Map<String, dynamic> context,
    String language,
  ) {
    final languageInstruction =
        language == 'kinyarwanda'
            ? 'Subiza mu Kinyarwanda.'
            : language == 'french'
            ? 'Répondez en français.'
            : 'Respond in English.';

    return '''
Nk'umujyanama w'ababyeyi mu cyaro cya Rwanda, fasha aba bantu mu gutegura umuryango wabo.

Amakuru: ${context.toString()}

$languageInstruction

Tanga inama ku:
1. Gutegura umuryango
2. Igihe cyo kubyara
3. Ubuzima bw'umubyeyi
4. Gufasha abana
5. Ubushobozi bw'umuryango

Wibuke umuco w'u Rwanda n'agaciro k'umuryango.
''';
  }

  /// Build pregnancy planning prompt
  String _buildPregnancyPlanningPrompt(
    Map<String, dynamic> planningData,
    String language,
  ) {
    final languageInstruction =
        language == 'kinyarwanda'
            ? 'Subiza mu Kinyarwanda.'
            : language == 'french'
            ? 'Répondez en français.'
            : 'Respond in English.';

    return '''
Umugore arimo gutegura inda. Amakuru ye: ${planningData.toString()}

$languageInstruction

Mumuhe inama ku:
1. Gutegura umubiri
2. Kurya neza
3. Gufata vitamini
4. Kwirinda ibintu bibi
5. Gusura muganga

Mwirinde kutanga inama z'ubuvuzi. Musabe ajye ku bitaro.
''';
  }

  /// Parse recommendations from AI response
  List<String> _parseRecommendations(String response) {
    final lines = response.split('\n');
    final recommendations = <String>[];

    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.startsWith('•') ||
          trimmed.startsWith('-') ||
          trimmed.startsWith('*') ||
          RegExp(r'^\d+\.').hasMatch(trimmed)) {
        recommendations.add(trimmed);
      }
    }

    return recommendations.isNotEmpty ? recommendations : [response];
  }

  /// Assess urgency based on symptoms
  String _assessUrgency(List<String> symptoms) {
    final urgentSymptoms = [
      'kubabara cyane',
      'amaraso',
      'umuriro',
      'guhema bigoye',
      'severe pain',
      'bleeding',
      'fever',
      'difficulty breathing',
    ];

    for (final symptom in symptoms) {
      for (final urgent in urgentSymptoms) {
        if (symptom.toLowerCase().contains(urgent.toLowerCase())) {
          return 'high';
        }
      }
    }

    return 'low';
  }

  /// Get error message in appropriate language
  String _getErrorMessage(String language) {
    switch (language) {
      case 'kinyarwanda':
        return 'Ntabwo nashoboye gusubiza. Reba ko ufite internet hanyuma ongera ugerageze.';
      case 'french':
        return 'Je n\'ai pas pu répondre. Vérifiez votre connexion internet et réessayez.';
      default:
        return 'I couldn\'t respond. Please check your internet connection and try again.';
    }
  }
}
