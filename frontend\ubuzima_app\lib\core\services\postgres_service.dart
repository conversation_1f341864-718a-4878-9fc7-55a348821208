import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

/// PostgreSQL Database Service for Ubuzima App
/// Handles real database connections via HTTP API
class PostgresService extends ChangeNotifier {
  static final PostgresService _instance = PostgresService._internal();
  factory PostgresService() => _instance;
  PostgresService._internal();

  bool _isConnected = false;
  String? _lastError;
  String? _apiUrl;

  // Database configuration
  late String _host;
  late int _port;
  late String _database;
  late String _username;
  late String _password;

  // Getters
  bool get isConnected => _isConnected;
  String? get lastError => _lastError;
  String? get apiUrl => _apiUrl;

  /// Initialize PostgreSQL service with configuration
  Future<bool> initialize({
    String? host,
    int? port,
    String? database,
    String? username,
    String? password,
  }) async {
    try {
      // Load configuration from parameters or SharedPreferences
      await _loadConfiguration(
        host: host,
        port: port,
        database: database,
        username: username,
        password: password,
      );

      // Attempt to connect
      await _connect();

      // Create tables if they don't exist
      if (_isConnected) {
        await _createTables();
      }

      debugPrint('✅ PostgreSQL Service initialized: $_isConnected');
      return _isConnected;
    } catch (e) {
      debugPrint('❌ PostgreSQL initialization failed: $e');
      _lastError = e.toString();
      return false;
    }
  }

  /// Load database configuration
  Future<void> _loadConfiguration({
    String? host,
    int? port,
    String? database,
    String? username,
    String? password,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    _host = host ?? prefs.getString('db_host') ?? 'localhost';
    _port = port ?? prefs.getInt('db_port') ?? 5432;
    _database = database ?? prefs.getString('db_database') ?? 'ubuzima_db';
    _username = username ?? prefs.getString('db_username') ?? 'postgres';
    _password = password ?? prefs.getString('db_password') ?? 'password';

    debugPrint('📊 Database config: $_host:$_port/$_database');
  }

  /// Save database configuration
  Future<void> saveConfiguration({
    required String host,
    required int port,
    required String database,
    required String username,
    required String password,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setString('db_host', host);
    await prefs.setInt('db_port', port);
    await prefs.setString('db_database', database);
    await prefs.setString('db_username', username);
    await prefs.setString('db_password', password);

    _host = host;
    _port = port;
    _database = database;
    _username = username;
    _password = password;

    debugPrint('💾 Database configuration saved');
  }

  /// Connect to PostgreSQL database via API
  Future<void> _connect() async {
    try {
      // For now, we'll simulate a connection
      // In a real implementation, you would connect to your backend API
      _apiUrl = 'http://$_host:3000/api'; // Assuming a REST API on port 3000

      _isConnected = true;
      _lastError = null;
      notifyListeners();

      debugPrint('✅ Connected to PostgreSQL API: $_apiUrl');
    } catch (e) {
      _isConnected = false;
      _lastError = e.toString();
      notifyListeners();

      debugPrint('❌ PostgreSQL API connection failed: $e');
      rethrow;
    }
  }

  /// Test database connection
  Future<bool> testConnection({
    required String host,
    required int port,
    required String database,
    required String username,
    required String password,
  }) async {
    try {
      // For demo purposes, we'll simulate a successful connection test
      // In a real implementation, you would test your backend API
      await Future.delayed(const Duration(seconds: 1));

      debugPrint('✅ Connection test successful (simulated)');
      return true;
    } catch (e) {
      debugPrint('❌ Connection test failed: $e');
      return false;
    }
  }

  /// Create database tables (simulated)
  Future<void> _createTables() async {
    if (!_isConnected) return;

    try {
      // Users table
      await _connection!.execute('''
        CREATE TABLE IF NOT EXISTS users (
          id SERIAL PRIMARY KEY,
          uuid VARCHAR(36) UNIQUE NOT NULL,
          name VARCHAR(255) NOT NULL,
          email VARCHAR(255) UNIQUE,
          phone VARCHAR(20),
          role VARCHAR(50) NOT NULL,
          date_of_birth DATE,
          gender VARCHAR(10),
          location VARCHAR(255),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      ''');

      // Health records table
      await _connection!.execute('''
        CREATE TABLE IF NOT EXISTS health_records (
          id SERIAL PRIMARY KEY,
          user_id INTEGER REFERENCES users(id),
          record_type VARCHAR(50) NOT NULL,
          date TIMESTAMP NOT NULL,
          weight DECIMAL(5,2),
          blood_pressure_systolic INTEGER,
          blood_pressure_diastolic INTEGER,
          temperature DECIMAL(4,2),
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      ''');

      // Appointments table
      await _connection!.execute('''
        CREATE TABLE IF NOT EXISTS appointments (
          id SERIAL PRIMARY KEY,
          client_id INTEGER REFERENCES users(id),
          health_worker_id INTEGER REFERENCES users(id),
          appointment_date TIMESTAMP NOT NULL,
          type VARCHAR(100) NOT NULL,
          status VARCHAR(50) DEFAULT 'scheduled',
          notes TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      ''');

      // Messages table
      await _connection!.execute('''
        CREATE TABLE IF NOT EXISTS messages (
          id SERIAL PRIMARY KEY,
          sender_id INTEGER REFERENCES users(id),
          receiver_id INTEGER REFERENCES users(id),
          message TEXT NOT NULL,
          message_type VARCHAR(50) DEFAULT 'text',
          is_read BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      ''');

      // Education progress table
      await _connection!.execute('''
        CREATE TABLE IF NOT EXISTS education_progress (
          id SERIAL PRIMARY KEY,
          user_id INTEGER REFERENCES users(id),
          topic_id VARCHAR(100) NOT NULL,
          completed BOOLEAN DEFAULT FALSE,
          progress_percentage INTEGER DEFAULT 0,
          completed_at TIMESTAMP,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      ''');

      debugPrint('✅ Database tables created/verified');
    } catch (e) {
      debugPrint('❌ Table creation failed: $e');
      rethrow;
    }
  }

  /// Execute a query and return results
  Future<List<Map<String, dynamic>>> query(
    String sql, {
    Map<String, dynamic>? parameters,
  }) async {
    if (!_isConnected || _connection == null) {
      throw Exception('Database not connected');
    }

    try {
      final result = await _connection!.execute(
        Sql.named(sql),
        parameters: parameters,
      );

      return result.map((row) => row.toColumnMap()).toList();
    } catch (e) {
      debugPrint('❌ Query failed: $e');
      _lastError = e.toString();
      rethrow;
    }
  }

  /// Execute an insert/update/delete query
  Future<int> execute(String sql, {Map<String, dynamic>? parameters}) async {
    if (!_isConnected || _connection == null) {
      throw Exception('Database not connected');
    }

    try {
      final result = await _connection!.execute(
        Sql.named(sql),
        parameters: parameters,
      );

      return result.affectedRows;
    } catch (e) {
      debugPrint('❌ Execute failed: $e');
      _lastError = e.toString();
      rethrow;
    }
  }

  /// Close database connection
  Future<void> close() async {
    if (_connection != null) {
      await _connection!.close();
      _connection = null;
      _isConnected = false;
      notifyListeners();
      debugPrint('🔌 PostgreSQL connection closed');
    }
  }

  /// Reconnect to database
  Future<bool> reconnect() async {
    await close();
    try {
      await _connect();
      return _isConnected;
    } catch (e) {
      return false;
    }
  }

  @override
  void dispose() {
    close();
    super.dispose();
  }
}
