import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';
import '../../core/models/health_record_model.dart';

class ChatScreen extends StatelessWidget {
  final HealthWorker contact;

  const ChatScreen({super.key, required this.contact});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(contact.name),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Text('Chat Screen - Coming Soon'),
      ),
    );
  }
}
