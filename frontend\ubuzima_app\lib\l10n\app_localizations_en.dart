import 'app_localizations.dart';

class AppLocalizationsEn extends AppLocalizations {
  // App Info
  @override
  String get appName => 'Ubuzima';
  @override
  String get appTagline => 'Your Health Companion';
  @override
  String get version => 'Version';

  // Navigation & Common
  @override
  String get home => 'Home';
  @override
  String get back => 'Back';
  @override
  String get next => 'Next';
  @override
  String get previous => 'Previous';
  @override
  String get save => 'Save';
  @override
  String get cancel => 'Cancel';
  @override
  String get delete => 'Delete';
  @override
  String get edit => 'Edit';
  @override
  String get add => 'Add';
  @override
  String get search => 'Search';
  @override
  String get filter => 'Filter';
  @override
  String get settings => 'Settings';
  @override
  String get help => 'Help';
  @override
  String get profile => 'Profile';
  @override
  String get logout => 'Logout';
  @override
  String get login => 'Login';
  @override
  String get register => 'Register';
  @override
  String get skip => 'Skip';
  @override
  String get continue_ => 'Continue';
  @override
  String get done => 'Done';
  @override
  String get loading => 'Loading...';
  @override
  String get error => 'Error';
  @override
  String get success => 'Success';
  @override
  String get warning => 'Warning';
  @override
  String get info => 'Information';

  // Authentication
  @override
  String get email => 'Email';
  @override
  String get password => 'Password';
  @override
  String get confirmPassword => 'Confirm Password';
  @override
  String get forgotPassword => 'Forgot Password?';
  @override
  String get createAccount => 'Create Account';
  @override
  String get alreadyHaveAccount => 'Already have an account?';
  @override
  String get dontHaveAccount => "Don't have an account?";
  @override
  String get signInWithGoogle => 'Sign in with Google';
  @override
  String get signInWithFacebook => 'Sign in with Facebook';
  @override
  String get or => 'OR';
  @override
  String get fullName => 'Full Name';
  @override
  String get phoneNumber => 'Phone Number';
  @override
  String get dateOfBirth => 'Date of Birth';
  @override
  String get gender => 'Gender';
  @override
  String get male => 'Male';
  @override
  String get female => 'Female';
  @override
  String get other => 'Other';
  @override
  String get location => 'Location';
  @override
  String get district => 'District';
  @override
  String get sector => 'Sector';
  @override
  String get cell => 'Cell';
  @override
  String get village => 'Village';

  // Dashboard
  @override
  String get dashboard => 'Dashboard';
  @override
  String get welcome => 'Welcome';
  @override
  String get welcomeBack => 'Welcome Back';
  @override
  String get quickActions => 'Quick Actions';
  @override
  String get recentActivity => 'Recent Activity';
  @override
  String get healthOverview => 'Health Overview';
  @override
  String get upcomingAppointments => 'Upcoming Appointments';
  @override
  String get messages => 'Messages';
  @override
  String get notifications => 'Notifications';

  // Health Tracking
  @override
  String get healthTracking => 'Health Tracking';
  @override
  String get menstrualCycle => 'Menstrual Cycle';
  @override
  String get cycleTracking => 'Cycle Tracking';
  @override
  String get periodTracker => 'Period Tracker';
  @override
  String get ovulation => 'Ovulation';
  @override
  String get fertility => 'Fertility';
  @override
  String get symptoms => 'Symptoms';
  @override
  String get mood => 'Mood';
  @override
  String get flow => 'Flow';
  @override
  String get pain => 'Pain';
  @override
  String get notes => 'Notes';
  @override
  String get addSymptom => 'Add Symptom';
  @override
  String get editSymptom => 'Edit Symptom';
  @override
  String get deleteSymptom => 'Delete Symptom';
  @override
  String get cycleLength => 'Cycle Length';
  @override
  String get periodLength => 'Period Length';
  @override
  String get lastPeriod => 'Last Period';
  @override
  String get nextPeriod => 'Next Period';
  @override
  String get daysUntilPeriod => 'Days Until Period';
  @override
  String get daysUntilOvulation => 'Days Until Ovulation';
  @override
  String get fertile => 'Fertile';
  @override
  String get notFertile => 'Not Fertile';
  @override
  String get highFertility => 'High Fertility';
  @override
  String get lowFertility => 'Low Fertility';

  // Contraception
  @override
  String get contraception => 'Contraception';
  @override
  String get birthControl => 'Birth Control';
  @override
  String get contraceptiveMethod => 'Contraceptive Method';
  @override
  String get pill => 'Pill';
  @override
  String get condom => 'Condom';
  @override
  String get iud => 'IUD';
  @override
  String get implant => 'Implant';
  @override
  String get injection => 'Injection';
  @override
  String get patch => 'Patch';
  @override
  String get ring => 'Ring';
  @override
  String get naturalMethods => 'Natural Methods';
  @override
  String get emergency => 'Emergency';
  @override
  String get effectiveness => 'Effectiveness';
  @override
  String get sideEffects => 'Side Effects';
  @override
  String get howToUse => 'How to Use';
  @override
  String get reminders => 'Reminders';
  @override
  String get setReminder => 'Set Reminder';
  @override
  String get dailyReminder => 'Daily Reminder';
  @override
  String get weeklyReminder => 'Weekly Reminder';
  @override
  String get monthlyReminder => 'Monthly Reminder';

  // Education
  @override
  String get education => 'Education';
  @override
  String get lessons => 'Lessons';
  @override
  String get courses => 'Courses';
  @override
  String get topics => 'Topics';
  @override
  String get familyPlanning => 'Family Planning';
  @override
  String get reproductiveHealth => 'Reproductive Health';
  @override
  String get sexualHealth => 'Sexual Health';
  @override
  String get pregnancy => 'Pregnancy';
  @override
  String get prenatalCare => 'Prenatal Care';
  @override
  String get postnatalCare => 'Postnatal Care';
  @override
  String get breastfeeding => 'Breastfeeding';
  @override
  String get childcare => 'Childcare';
  @override
  String get nutrition => 'Nutrition';
  @override
  String get exercise => 'Exercise';
  @override
  String get mentalHealth => 'Mental Health';
  @override
  String get relationships => 'Relationships';
  @override
  String get communication => 'Communication';
  @override
  String get consent => 'Consent';
  @override
  String get safety => 'Safety';

  // Communication
  @override
  String get messaging => 'Messaging';
  @override
  String get chat => 'Chat';
  @override
  String get call => 'Call';
  @override
  String get videoCall => 'Video Call';
  @override
  String get sendMessage => 'Send Message';
  @override
  String get typeMessage => 'Type a message...';
  @override
  String get voiceMessage => 'Voice Message';
  @override
  String get attachment => 'Attachment';
  @override
  String get photo => 'Photo';
  @override
  String get document => 'Document';
  @override
  String get healthWorker => 'Health Worker';
  @override
  String get doctor => 'Doctor';
  @override
  String get nurse => 'Nurse';
  @override
  String get midwife => 'Midwife';
  @override
  String get counselor => 'Counselor';
  @override
  String get online => 'Online';
  @override
  String get offline => 'Offline';
  @override
  String get lastSeen => 'Last seen';
  @override
  String get typing => 'Typing...';

  // Appointments
  @override
  String get appointments => 'Appointments';
  @override
  String get bookAppointment => 'Book Appointment';
  @override
  String get rescheduleAppointment => 'Reschedule Appointment';
  @override
  String get cancelAppointment => 'Cancel Appointment';
  @override
  String get upcomingAppointment => 'Upcoming Appointment';
  @override
  String get pastAppointments => 'Past Appointments';
  @override
  String get appointmentConfirmed => 'Appointment Confirmed';
  @override
  String get appointmentCancelled => 'Appointment Cancelled';
  @override
  String get appointmentRescheduled => 'Appointment Rescheduled';
  @override
  String get selectDate => 'Select Date';
  @override
  String get selectTime => 'Select Time';
  @override
  String get selectHealthWorker => 'Select Health Worker';
  @override
  String get selectService => 'Select Service';
  @override
  String get appointmentType => 'Appointment Type';
  @override
  String get consultation => 'Consultation';
  @override
  String get checkup => 'Check-up';
  @override
  String get followUp => 'Follow-up';
  @override
  String get routine => 'Routine';

  // Clinics & Locations
  @override
  String get clinics => 'Clinics';
  @override
  String get healthFacilities => 'Health Facilities';
  @override
  String get nearbyFacilities => 'Nearby Facilities';
  @override
  String get findClinic => 'Find Clinic';
  @override
  String get directions => 'Directions';
  @override
  String get distance => 'Distance';
  @override
  String get openingHours => 'Opening Hours';
  @override
  String get services => 'Services';
  @override
  String get contactInfo => 'Contact Information';
  @override
  String get address => 'Address';
  @override
  String get mapView => 'Map View';
  @override
  String get listView => 'List View';
  @override
  String get currentLocation => 'Current Location';
  @override
  String get searchLocation => 'Search Location';

  // Voice Commands
  @override
  String get useVoice => 'Use Voice';
  @override
  String get voiceCommand => 'Voice Command';
  @override
  String get listening => 'Listening...';
  @override
  String get speakNow => 'Speak Now';
  @override
  String get voiceNotRecognized => 'Voice not recognized';
  @override
  String get tryAgain => 'Try Again';
  @override
  String get voiceHelp => 'Voice Help';

  // Settings
  @override
  String get language => 'Language';
  @override
  String get changeLanguage => 'Change Language';
  @override
  String get privacy => 'Privacy';
  @override
  String get security => 'Security';
  @override
  String get account => 'Account';
  @override
  String get about => 'About';
  @override
  String get termsOfService => 'Terms of Service';
  @override
  String get privacyPolicy => 'Privacy Policy';
  @override
  String get contactSupport => 'Contact Support';
  @override
  String get reportBug => 'Report Bug';
  @override
  String get rateApp => 'Rate App';
  @override
  String get shareApp => 'Share App';

  // Errors & Messages
  @override
  String get errorOccurred => 'An error occurred';
  @override
  String get networkError => 'Network error';
  @override
  String get serverError => 'Server error';
  @override
  String get validationError => 'Validation error';
  @override
  String get fieldRequired => 'This field is required';
  @override
  String get invalidEmail => 'Invalid email address';
  @override
  String get invalidPhone => 'Invalid phone number';
  @override
  String get passwordTooShort => 'Password is too short';
  @override
  String get passwordsDoNotMatch => 'Passwords do not match';
  @override
  String get loginFailed => 'Login failed';
  @override
  String get registrationFailed => 'Registration failed';
  @override
  String get dataLoadFailed => 'Failed to load data';
  @override
  String get dataSaveFailed => 'Failed to save data';
  @override
  String get permissionDenied => 'Permission denied';
  @override
  String get locationPermissionDenied => 'Location permission denied';
  @override
  String get cameraPermissionDenied => 'Camera permission denied';
  @override
  String get microphonePermissionDenied => 'Microphone permission denied';

  // Success Messages
  @override
  String get loginSuccessful => 'Login successful';
  @override
  String get registrationSuccessful => 'Registration successful';
  @override
  String get dataSaved => 'Data saved successfully';
  @override
  String get appointmentBooked => 'Appointment booked successfully';
  @override
  String get messagesSent => 'Message sent successfully';
  @override
  String get profileUpdated => 'Profile updated successfully';
  @override
  String get settingsUpdated => 'Settings updated successfully';

  // Time & Dates
  @override
  String get today => 'Today';
  @override
  String get yesterday => 'Yesterday';
  @override
  String get tomorrow => 'Tomorrow';
  @override
  String get thisWeek => 'This Week';
  @override
  String get thisMonth => 'This Month';
  @override
  String get lastWeek => 'Last Week';
  @override
  String get lastMonth => 'Last Month';
  @override
  String get nextWeek => 'Next Week';
  @override
  String get nextMonth => 'Next Month';
  @override
  String get morning => 'Morning';
  @override
  String get afternoon => 'Afternoon';
  @override
  String get evening => 'Evening';
  @override
  String get night => 'Night';

  // Days of Week
  @override
  String get monday => 'Monday';
  @override
  String get tuesday => 'Tuesday';
  @override
  String get wednesday => 'Wednesday';
  @override
  String get thursday => 'Thursday';
  @override
  String get friday => 'Friday';
  @override
  String get saturday => 'Saturday';
  @override
  String get sunday => 'Sunday';

  // Months
  @override
  String get january => 'January';
  @override
  String get february => 'February';
  @override
  String get march => 'March';
  @override
  String get april => 'April';
  @override
  String get may => 'May';
  @override
  String get june => 'June';
  @override
  String get july => 'July';
  @override
  String get august => 'August';
  @override
  String get september => 'September';
  @override
  String get october => 'October';
  @override
  String get november => 'November';
  @override
  String get december => 'December';
}
