import 'app_localizations.dart';

class AppLocalizationsFr extends AppLocalizations {
  // App Info
  @override
  String get appName => 'Ubuzima';
  @override
  String get appTagline => 'Votre compagnon santé';
  @override
  String get version => 'Version';

  // Navigation & Common
  @override
  String get home => 'Accueil';
  @override
  String get back => 'Retour';
  @override
  String get next => 'Suivant';
  @override
  String get previous => 'Précédent';
  @override
  String get save => 'Enregistrer';
  @override
  String get cancel => 'Annuler';
  @override
  String get delete => 'Supprimer';
  @override
  String get edit => 'Modifier';
  @override
  String get add => 'Ajouter';
  @override
  String get search => 'Rechercher';
  @override
  String get filter => 'Filtrer';
  @override
  String get settings => 'Paramètres';
  @override
  String get help => 'Aide';
  @override
  String get profile => 'Profil';
  @override
  String get logout => 'Déconnexion';
  @override
  String get login => 'Connexion';
  @override
  String get register => 'S\'inscrire';
  @override
  String get skip => 'Ignorer';
  @override
  String get continue_ => 'Continuer';
  @override
  String get done => 'Terminé';
  @override
  String get loading => 'Chargement...';
  @override
  String get error => 'Erreur';
  @override
  String get success => 'Succès';
  @override
  String get warning => 'Avertissement';
  @override
  String get info => 'Information';

  // Authentication
  @override
  String get email => 'Email';
  @override
  String get password => 'Mot de passe';
  @override
  String get confirmPassword => 'Confirmer le mot de passe';
  @override
  String get forgotPassword => 'Mot de passe oublié?';
  @override
  String get createAccount => 'Créer un compte';
  @override
  String get alreadyHaveAccount => 'Vous avez déjà un compte?';
  @override
  String get dontHaveAccount => 'Vous n\'avez pas de compte?';
  @override
  String get signInWithGoogle => 'Se connecter avec Google';
  @override
  String get signInWithFacebook => 'Se connecter avec Facebook';
  @override
  String get or => 'OU';
  @override
  String get fullName => 'Nom complet';
  @override
  String get phoneNumber => 'Numéro de téléphone';
  @override
  String get dateOfBirth => 'Date de naissance';
  @override
  String get gender => 'Genre';
  @override
  String get male => 'Homme';
  @override
  String get female => 'Femme';
  @override
  String get other => 'Autre';
  @override
  String get location => 'Localisation';
  @override
  String get district => 'District';
  @override
  String get sector => 'Secteur';
  @override
  String get cell => 'Cellule';
  @override
  String get village => 'Village';

  // Dashboard
  @override
  String get dashboard => 'Tableau de bord';
  @override
  String get welcome => 'Bienvenue';
  @override
  String get welcomeBack => 'Bon retour';
  @override
  String get quickActions => 'Actions rapides';
  @override
  String get recentActivity => 'Activité récente';
  @override
  String get healthOverview => 'Aperçu santé';
  @override
  String get upcomingAppointments => 'Rendez-vous à venir';
  @override
  String get messages => 'Messages';
  @override
  String get notifications => 'Notifications';

  // Health Tracking
  @override
  String get healthTracking => 'Suivi de santé';
  @override
  String get menstrualCycle => 'Cycle menstruel';
  @override
  String get cycleTracking => 'Suivi du cycle';
  @override
  String get periodTracker => 'Suivi des règles';
  @override
  String get ovulation => 'Ovulation';
  @override
  String get fertility => 'Fertilité';
  @override
  String get symptoms => 'Symptômes';
  @override
  String get mood => 'Humeur';
  @override
  String get flow => 'Flux';
  @override
  String get pain => 'Douleur';
  @override
  String get notes => 'Notes';
  @override
  String get addSymptom => 'Ajouter un symptôme';
  @override
  String get editSymptom => 'Modifier le symptôme';
  @override
  String get deleteSymptom => 'Supprimer le symptôme';
  @override
  String get cycleLength => 'Durée du cycle';
  @override
  String get periodLength => 'Durée des règles';
  @override
  String get lastPeriod => 'Dernières règles';
  @override
  String get nextPeriod => 'Prochaines règles';
  @override
  String get daysUntilPeriod => 'Jours avant les règles';
  @override
  String get daysUntilOvulation => 'Jours avant l\'ovulation';
  @override
  String get fertile => 'Fertile';
  @override
  String get notFertile => 'Non fertile';
  @override
  String get highFertility => 'Haute fertilité';
  @override
  String get lowFertility => 'Faible fertilité';

  // Contraception
  @override
  String get contraception => 'Contraception';
  @override
  String get birthControl => 'Contrôle des naissances';
  @override
  String get contraceptiveMethod => 'Méthode contraceptive';
  @override
  String get pill => 'Pilule';
  @override
  String get condom => 'Préservatif';
  @override
  String get iud => 'DIU';
  @override
  String get implant => 'Implant';
  @override
  String get injection => 'Injection';
  @override
  String get patch => 'Patch';
  @override
  String get ring => 'Anneau';
  @override
  String get naturalMethods => 'Méthodes naturelles';
  @override
  String get emergency => 'Urgence';
  @override
  String get effectiveness => 'Efficacité';
  @override
  String get sideEffects => 'Effets secondaires';
  @override
  String get howToUse => 'Comment utiliser';
  @override
  String get reminders => 'Rappels';
  @override
  String get setReminder => 'Définir un rappel';
  @override
  String get dailyReminder => 'Rappel quotidien';
  @override
  String get weeklyReminder => 'Rappel hebdomadaire';
  @override
  String get monthlyReminder => 'Rappel mensuel';

  // Education
  @override
  String get education => 'Éducation';
  @override
  String get lessons => 'Leçons';
  @override
  String get courses => 'Cours';
  @override
  String get topics => 'Sujets';
  @override
  String get familyPlanning => 'Planification familiale';
  @override
  String get reproductiveHealth => 'Santé reproductive';
  @override
  String get sexualHealth => 'Santé sexuelle';
  @override
  String get pregnancy => 'Grossesse';
  @override
  String get prenatalCare => 'Soins prénataux';
  @override
  String get postnatalCare => 'Soins postnataux';
  @override
  String get breastfeeding => 'Allaitement';
  @override
  String get childcare => 'Soins aux enfants';
  @override
  String get nutrition => 'Nutrition';
  @override
  String get exercise => 'Exercice';
  @override
  String get mentalHealth => 'Santé mentale';
  @override
  String get relationships => 'Relations';
  @override
  String get communication => 'Communication';
  @override
  String get consent => 'Consentement';
  @override
  String get safety => 'Sécurité';

  // Communication
  @override
  String get messaging => 'Messagerie';
  @override
  String get chat => 'Chat';
  @override
  String get call => 'Appel';
  @override
  String get videoCall => 'Appel vidéo';
  @override
  String get sendMessage => 'Envoyer un message';
  @override
  String get typeMessage => 'Tapez un message...';
  @override
  String get voiceMessage => 'Message vocal';
  @override
  String get attachment => 'Pièce jointe';
  @override
  String get photo => 'Photo';
  @override
  String get document => 'Document';
  @override
  String get healthWorker => 'Agent de santé';
  @override
  String get doctor => 'Médecin';
  @override
  String get nurse => 'Infirmière';
  @override
  String get midwife => 'Sage-femme';
  @override
  String get counselor => 'Conseiller';
  @override
  String get online => 'En ligne';
  @override
  String get offline => 'Hors ligne';
  @override
  String get lastSeen => 'Vu pour la dernière fois';
  @override
  String get typing => 'En train de taper...';

  // Appointments
  @override
  String get appointments => 'Rendez-vous';
  @override
  String get bookAppointment => 'Prendre rendez-vous';
  @override
  String get rescheduleAppointment => 'Reporter le rendez-vous';
  @override
  String get cancelAppointment => 'Annuler le rendez-vous';
  @override
  String get upcomingAppointment => 'Prochain rendez-vous';
  @override
  String get pastAppointments => 'Rendez-vous passés';
  @override
  String get appointmentConfirmed => 'Rendez-vous confirmé';
  @override
  String get appointmentCancelled => 'Rendez-vous annulé';
  @override
  String get appointmentRescheduled => 'Rendez-vous reporté';
  @override
  String get selectDate => 'Sélectionner la date';
  @override
  String get selectTime => 'Sélectionner l\'heure';
  @override
  String get selectHealthWorker => 'Sélectionner un agent de santé';
  @override
  String get selectService => 'Sélectionner un service';
  @override
  String get appointmentType => 'Type de rendez-vous';
  @override
  String get consultation => 'Consultation';
  @override
  String get checkup => 'Examen';
  @override
  String get followUp => 'Suivi';
  @override
  String get routine => 'Routine';

  // Clinics & Locations
  @override
  String get clinics => 'Cliniques';
  @override
  String get healthFacilities => 'Établissements de santé';
  @override
  String get nearbyFacilities => 'Établissements à proximité';
  @override
  String get findClinic => 'Trouver une clinique';
  @override
  String get directions => 'Directions';
  @override
  String get distance => 'Distance';
  @override
  String get openingHours => 'Heures d\'ouverture';
  @override
  String get services => 'Services';
  @override
  String get contactInfo => 'Informations de contact';
  @override
  String get address => 'Adresse';
  @override
  String get mapView => 'Vue carte';
  @override
  String get listView => 'Vue liste';
  @override
  String get currentLocation => 'Position actuelle';
  @override
  String get searchLocation => 'Rechercher une localisation';

  // Voice Commands
  @override
  String get useVoice => 'Utiliser la voix';
  @override
  String get voiceCommand => 'Commande vocale';
  @override
  String get listening => 'Écoute...';
  @override
  String get speakNow => 'Parlez maintenant';
  @override
  String get voiceNotRecognized => 'Voix non reconnue';
  @override
  String get tryAgain => 'Réessayer';
  @override
  String get voiceHelp => 'Aide vocale';

  // Settings
  @override
  String get language => 'Langue';
  @override
  String get changeLanguage => 'Changer de langue';
  @override
  String get privacy => 'Confidentialité';
  @override
  String get security => 'Sécurité';
  @override
  String get account => 'Compte';
  @override
  String get about => 'À propos';
  @override
  String get termsOfService => 'Conditions d\'utilisation';
  @override
  String get privacyPolicy => 'Politique de confidentialité';
  @override
  String get contactSupport => 'Contacter le support';
  @override
  String get reportBug => 'Signaler un bug';
  @override
  String get rateApp => 'Noter l\'application';
  @override
  String get shareApp => 'Partager l\'application';

  // Errors & Messages
  @override
  String get errorOccurred => 'Une erreur s\'est produite';
  @override
  String get networkError => 'Erreur réseau';
  @override
  String get serverError => 'Erreur serveur';
  @override
  String get validationError => 'Erreur de validation';
  @override
  String get fieldRequired => 'Ce champ est requis';
  @override
  String get invalidEmail => 'Adresse email invalide';
  @override
  String get invalidPhone => 'Numéro de téléphone invalide';
  @override
  String get passwordTooShort => 'Mot de passe trop court';
  @override
  String get passwordsDoNotMatch => 'Les mots de passe ne correspondent pas';
  @override
  String get loginFailed => 'Échec de la connexion';
  @override
  String get registrationFailed => 'Échec de l\'inscription';
  @override
  String get dataLoadFailed => 'Échec du chargement des données';
  @override
  String get dataSaveFailed => 'Échec de l\'enregistrement des données';
  @override
  String get permissionDenied => 'Permission refusée';
  @override
  String get locationPermissionDenied => 'Permission de localisation refusée';
  @override
  String get cameraPermissionDenied => 'Permission de caméra refusée';
  @override
  String get microphonePermissionDenied => 'Permission de microphone refusée';

  // Success Messages
  @override
  String get loginSuccessful => 'Connexion réussie';
  @override
  String get registrationSuccessful => 'Inscription réussie';
  @override
  String get dataSaved => 'Données enregistrées avec succès';
  @override
  String get appointmentBooked => 'Rendez-vous pris avec succès';
  @override
  String get messagesSent => 'Message envoyé avec succès';
  @override
  String get profileUpdated => 'Profil mis à jour avec succès';
  @override
  String get settingsUpdated => 'Paramètres mis à jour avec succès';

  // Time & Dates
  @override
  String get today => 'Aujourd\'hui';
  @override
  String get yesterday => 'Hier';
  @override
  String get tomorrow => 'Demain';
  @override
  String get thisWeek => 'Cette semaine';
  @override
  String get thisMonth => 'Ce mois';
  @override
  String get lastWeek => 'La semaine dernière';
  @override
  String get lastMonth => 'Le mois dernier';
  @override
  String get nextWeek => 'La semaine prochaine';
  @override
  String get nextMonth => 'Le mois prochain';
  @override
  String get morning => 'Matin';
  @override
  String get afternoon => 'Après-midi';
  @override
  String get evening => 'Soir';
  @override
  String get night => 'Nuit';

  // Days of Week
  @override
  String get monday => 'Lundi';
  @override
  String get tuesday => 'Mardi';
  @override
  String get wednesday => 'Mercredi';
  @override
  String get thursday => 'Jeudi';
  @override
  String get friday => 'Vendredi';
  @override
  String get saturday => 'Samedi';
  @override
  String get sunday => 'Dimanche';

  // Months
  @override
  String get january => 'Janvier';
  @override
  String get february => 'Février';
  @override
  String get march => 'Mars';
  @override
  String get april => 'Avril';
  @override
  String get may => 'Mai';
  @override
  String get june => 'Juin';
  @override
  String get july => 'Juillet';
  @override
  String get august => 'Août';
  @override
  String get september => 'Septembre';
  @override
  String get october => 'Octobre';
  @override
  String get november => 'Novembre';
  @override
  String get december => 'Décembre';
}
