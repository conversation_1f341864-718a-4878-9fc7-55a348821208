import 'app_localizations.dart';

class AppLocalizationsRw extends AppLocalizations {
  // App Info
  @override
  String get appName => 'Ubuzima';
  @override
  String get appTagline => 'Umunyangire w\'ubuzima bwawe';
  @override
  String get version => 'Verisiyo';

  // Navigation & Common
  @override
  String get home => 'Ahabanza';
  @override
  String get back => 'Subira';
  @override
  String get next => 'Komeza';
  @override
  String get previous => 'Ibanjirije';
  @override
  String get save => 'Bika';
  @override
  String get cancel => 'Hagarika';
  @override
  String get delete => 'Siba';
  @override
  String get edit => 'Hindura';
  @override
  String get add => 'Ongeraho';
  @override
  String get search => 'Shakisha';
  @override
  String get filter => 'Shungura';
  @override
  String get settings => 'Igenamiterere';
  @override
  String get help => 'Ubufasha';
  @override
  String get profile => 'Umwirondoro';
  @override
  String get logout => 'Sohoka';
  @override
  String get login => 'Injira';
  @override
  String get register => 'Iyandikishe';
  @override
  String get skip => 'Simbuka';
  @override
  String get continue_ => 'Komeza';
  @override
  String get done => 'Byarangiye';
  @override
  String get loading => 'Birategurika...';
  @override
  String get error => 'Ikosa';
  @override
  String get success => 'Byagenze neza';
  @override
  String get warning => 'Iburira';
  @override
  String get info => 'Amakuru';

  // Authentication
  @override
  String get email => 'Imeyili';
  @override
  String get password => 'Ijambo ry\'ibanga';
  @override
  String get confirmPassword => 'Emeza ijambo ry\'ibanga';
  @override
  String get forgotPassword => 'Wibagiwe ijambo ry\'ibanga?';
  @override
  String get createAccount => 'Kora konti';
  @override
  String get alreadyHaveAccount => 'Usanzwe ufite konti?';
  @override
  String get dontHaveAccount => 'Ntufite konti?';
  @override
  String get signInWithGoogle => 'Injira ukoresheje Google';
  @override
  String get signInWithFacebook => 'Injira ukoresheje Facebook';
  @override
  String get or => 'CYANGWA';
  @override
  String get fullName => 'Amazina yose';
  @override
  String get phoneNumber => 'Nimero ya telefone';
  @override
  String get dateOfBirth => 'Itariki y\'amavuko';
  @override
  String get gender => 'Igitsina';
  @override
  String get male => 'Gabo';
  @override
  String get female => 'Gore';
  @override
  String get other => 'Ikindi';
  @override
  String get location => 'Aho ubarizwa';
  @override
  String get district => 'Akarere';
  @override
  String get sector => 'Umurenge';
  @override
  String get cell => 'Akagari';
  @override
  String get village => 'Umudugudu';

  // Dashboard
  @override
  String get dashboard => 'Ikibaho';
  @override
  String get welcome => 'Murakaza neza';
  @override
  String get welcomeBack => 'Murakaza neza nanone';
  @override
  String get quickActions => 'Ibikorwa byihuse';
  @override
  String get recentActivity => 'Ibikorwa bya vuba';
  @override
  String get healthOverview => 'Incamake y\'ubuzima';
  @override
  String get upcomingAppointments => 'Gahunda zitegereje';
  @override
  String get messages => 'Ubutumwa';
  @override
  String get notifications => 'Amakuru';

  // Health Tracking
  @override
  String get healthTracking => 'Gukurikirana ubuzima';
  @override
  String get menstrualCycle => 'Imihango';
  @override
  String get cycleTracking => 'Gukurikirana imihango';
  @override
  String get periodTracker => 'Gukurikirana imihango';
  @override
  String get ovulation => 'Gusohora amagi';
  @override
  String get fertility => 'Ubusore';
  @override
  String get symptoms => 'Ibimenyetso';
  @override
  String get mood => 'Imyumvire';
  @override
  String get flow => 'Ubwinshi';
  @override
  String get pain => 'Ububabare';
  @override
  String get notes => 'Inyandiko';
  @override
  String get addSymptom => 'Ongeraho ikimenyetso';
  @override
  String get editSymptom => 'Hindura ikimenyetso';
  @override
  String get deleteSymptom => 'Siba ikimenyetso';
  @override
  String get cycleLength => 'Uburebure bw\'imihango';
  @override
  String get periodLength => 'Uburebure bw\'imihango';
  @override
  String get lastPeriod => 'Imihango ishize';
  @override
  String get nextPeriod => 'Imihango itaha';
  @override
  String get daysUntilPeriod => 'Iminsi isigaye kugeza ku mihango';
  @override
  String get daysUntilOvulation => 'Iminsi isigaye kugeza ku gusohora amagi';
  @override
  String get fertile => 'Usore';
  @override
  String get notFertile => 'Ntusore';
  @override
  String get highFertility => 'Ubusore bwinshi';
  @override
  String get lowFertility => 'Ubusore buke';

  // Contraception
  @override
  String get contraception => 'Kurinda inda';
  @override
  String get birthControl => 'Kurinda inda';
  @override
  String get contraceptiveMethod => 'Uburyo bwo kurinda inda';
  @override
  String get pill => 'Imiti';
  @override
  String get condom => 'Agakingirizo';
  @override
  String get iud => 'IUD';
  @override
  String get implant => 'Implant';
  @override
  String get injection => 'Urushinge';
  @override
  String get patch => 'Patch';
  @override
  String get ring => 'Ring';
  @override
  String get naturalMethods => 'Uburyo bwa kamere';
  @override
  String get emergency => 'Ubwihuse';
  @override
  String get effectiveness => 'Ubushobozi';
  @override
  String get sideEffects => 'Ingaruka';
  @override
  String get howToUse => 'Uburyo bwo gukoresha';
  @override
  String get reminders => 'Kwibutsa';
  @override
  String get setReminder => 'Shyiraho kwibutsa';
  @override
  String get dailyReminder => 'Kwibutsa buri munsi';
  @override
  String get weeklyReminder => 'Kwibutsa buri cyumweru';
  @override
  String get monthlyReminder => 'Kwibutsa buri kwezi';

  // Education
  @override
  String get education => 'Amasomo';
  @override
  String get lessons => 'Amasomo';
  @override
  String get courses => 'Amasomo';
  @override
  String get topics => 'Ingingo';
  @override
  String get familyPlanning => 'Kubana no kurinda inda';
  @override
  String get reproductiveHealth => 'Ubuzima bw\'imyororokere';
  @override
  String get sexualHealth => 'Ubuzima bw\'imibonano';
  @override
  String get pregnancy => 'Inda';
  @override
  String get prenatalCare => 'Kwita ku inda';
  @override
  String get postnatalCare => 'Kwita nyuma y\'inda';
  @override
  String get breastfeeding => 'Konsa';
  @override
  String get childcare => 'Kwita ku bana';
  @override
  String get nutrition => 'Imirire myiza';
  @override
  String get exercise => 'Siporo';
  @override
  String get mentalHealth => 'Ubuzima bw\'ubwoba';
  @override
  String get relationships => 'Ubusabane';
  @override
  String get communication => 'Itumanaho';
  @override
  String get consent => 'Kwemera';
  @override
  String get safety => 'Umutekano';

  // Communication
  @override
  String get messaging => 'Ubutumwa';
  @override
  String get chat => 'Ikiganiro';
  @override
  String get call => 'Guhamagara';
  @override
  String get videoCall => 'Guhamagara kw\'amashusho';
  @override
  String get sendMessage => 'Ohereza ubutumwa';
  @override
  String get typeMessage => 'Andika ubutumwa...';
  @override
  String get voiceMessage => 'Ubutumwa bw\'ijwi';
  @override
  String get attachment => 'Inyongera';
  @override
  String get photo => 'Ifoto';
  @override
  String get document => 'Inyandiko';
  @override
  String get healthWorker => 'Umukozi w\'ubuzima';
  @override
  String get doctor => 'Muganga';
  @override
  String get nurse => 'Umuforomo';
  @override
  String get midwife => 'Umubyaza';
  @override
  String get counselor => 'Umujyanama';
  @override
  String get online => 'Ku murongo';
  @override
  String get offline => 'Ntari ku murongo';
  @override
  String get lastSeen => 'Yabonetse bwa nyuma';
  @override
  String get typing => 'Arandika...';

  // Appointments
  @override
  String get appointments => 'Gahunda';
  @override
  String get bookAppointment => 'Shyiraho gahunda';
  @override
  String get rescheduleAppointment => 'Hindura gahunda';
  @override
  String get cancelAppointment => 'Hagarika gahunda';
  @override
  String get upcomingAppointment => 'Gahunda itegereje';
  @override
  String get pastAppointments => 'Gahunda zashize';
  @override
  String get appointmentConfirmed => 'Gahunda yemejwe';
  @override
  String get appointmentCancelled => 'Gahunda yahagaritswe';
  @override
  String get appointmentRescheduled => 'Gahunda yahinduwe';
  @override
  String get selectDate => 'Hitamo itariki';
  @override
  String get selectTime => 'Hitamo igihe';
  @override
  String get selectHealthWorker => 'Hitamo umukozi w\'ubuzima';
  @override
  String get selectService => 'Hitamo serivisi';
  @override
  String get appointmentType => 'Ubwoko bwa gahunda';
  @override
  String get consultation => 'Inama';
  @override
  String get checkup => 'Isuzuma';
  @override
  String get followUp => 'Gukurikirana';
  @override
  String get routine => 'Bisanzwe';

  // Clinics & Locations
  @override
  String get clinics => 'Amavuriro';
  @override
  String get healthFacilities => 'Ibigo by\'ubuzima';
  @override
  String get nearbyFacilities => 'Ibigo biri hafi';
  @override
  String get findClinic => 'Shakisha ivuriro';
  @override
  String get directions => 'Inzira';
  @override
  String get distance => 'Intera';
  @override
  String get openingHours => 'Amasaha yo gufungura';
  @override
  String get services => 'Serivisi';
  @override
  String get contactInfo => 'Amakuru y\'itumanaho';
  @override
  String get address => 'Aderesi';
  @override
  String get mapView => 'Ikarita';
  @override
  String get listView => 'Urutonde';
  @override
  String get currentLocation => 'Aho uri ubu';
  @override
  String get searchLocation => 'Shakisha aho uri';

  // Voice Commands
  @override
  String get useVoice => 'Koresha ijwi';
  @override
  String get voiceCommand => 'Itegeko ry\'ijwi';
  @override
  String get listening => 'Ndumva...';
  @override
  String get speakNow => 'Vuga ubu';
  @override
  String get voiceNotRecognized => 'Ijwi ntiriramenyekana';
  @override
  String get tryAgain => 'Ongera ugerageze';
  @override
  String get voiceHelp => 'Ubufasha bw\'ijwi';

  // Settings
  @override
  String get language => 'Ururimi';
  @override
  String get changeLanguage => 'Hindura ururimi';
  @override
  String get privacy => 'Ibanga';
  @override
  String get security => 'Umutekano';
  @override
  String get account => 'Konti';
  @override
  String get about => 'Ibyerekeye';
  @override
  String get termsOfService => 'Amabwiriza';
  @override
  String get privacyPolicy => 'Politiki y\'ibanga';
  @override
  String get contactSupport => 'Hamagara ubufasha';
  @override
  String get reportBug => 'Menyesha ikosa';
  @override
  String get rateApp => 'Tanga amanota';
  @override
  String get shareApp => 'Sangira porogaramu';

  // Errors & Messages
  @override
  String get errorOccurred => 'Habaye ikosa';
  @override
  String get networkError => 'Ikosa rya murandasi';
  @override
  String get serverError => 'Ikosa rya seriveri';
  @override
  String get validationError => 'Ikosa ryo kwemeza';
  @override
  String get fieldRequired => 'Iki gice gikenewe';
  @override
  String get invalidEmail => 'Imeyili idafite ubunyangamugayo';
  @override
  String get invalidPhone => 'Nimero ya telefone idafite ubunyangamugayo';
  @override
  String get passwordTooShort => 'Ijambo ry\'ibanga ni rito';
  @override
  String get passwordsDoNotMatch => 'Amagambo y\'ibanga ntabwo ahura';
  @override
  String get loginFailed => 'Kwinjira byanze';
  @override
  String get registrationFailed => 'Kwiyandikisha byanze';
  @override
  String get dataLoadFailed => 'Gupakurura amakuru byanze';
  @override
  String get dataSaveFailed => 'Kubika amakuru byanze';
  @override
  String get permissionDenied => 'Uruhushya rwahakanywe';
  @override
  String get locationPermissionDenied =>
      'Uruhushya rwo kumenya aho uri rwahakanywe';
  @override
  String get cameraPermissionDenied => 'Uruhushya rwa kamera rwahakanywe';
  @override
  String get microphonePermissionDenied => 'Uruhushya rwa mikoro rwahakanywe';

  // Success Messages
  @override
  String get loginSuccessful => 'Kwinjira byagenze neza';
  @override
  String get registrationSuccessful => 'Kwiyandikisha byagenze neza';
  @override
  String get dataSaved => 'Amakuru yabitswe neza';
  @override
  String get appointmentBooked => 'Gahunda yashyizweho neza';
  @override
  String get messagesSent => 'Ubutumwa bwoherejwe neza';
  @override
  String get profileUpdated => 'Umwirondoro wavuguruwe neza';
  @override
  String get settingsUpdated => 'Igenamiterere ryavuguruwe neza';

  // Time & Dates
  @override
  String get today => 'Uyu munsi';
  @override
  String get yesterday => 'Ejo hashize';
  @override
  String get tomorrow => 'Ejo hazaza';
  @override
  String get thisWeek => 'Iki cyumweru';
  @override
  String get thisMonth => 'Uku kwezi';
  @override
  String get lastWeek => 'Icyumweru gishize';
  @override
  String get lastMonth => 'Ukwezi gushize';
  @override
  String get nextWeek => 'Icyumweru gitaha';
  @override
  String get nextMonth => 'Ukwezi gutaha';
  @override
  String get morning => 'Igitondo';
  @override
  String get afternoon => 'Nyuma ya saa sita';
  @override
  String get evening => 'Nimugoroba';
  @override
  String get night => 'Nijoro';

  // Days of Week
  @override
  String get monday => 'Ku wa mbere';
  @override
  String get tuesday => 'Ku wa kabiri';
  @override
  String get wednesday => 'Ku wa gatatu';
  @override
  String get thursday => 'Ku wa kane';
  @override
  String get friday => 'Ku wa gatanu';
  @override
  String get saturday => 'Ku wa gatandatu';
  @override
  String get sunday => 'Ku cyumweru';

  // Months
  @override
  String get january => 'Mutarama';
  @override
  String get february => 'Gashyantare';
  @override
  String get march => 'Werurwe';
  @override
  String get april => 'Mata';
  @override
  String get may => 'Gicuransi';
  @override
  String get june => 'Kamena';
  @override
  String get july => 'Nyakanga';
  @override
  String get august => 'Kanama';
  @override
  String get september => 'Nzeli';
  @override
  String get october => 'Ukwakira';
  @override
  String get november => 'Ugushyingo';
  @override
  String get december => 'Ukuboza';
}
