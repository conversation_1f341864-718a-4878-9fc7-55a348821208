import 'package:flutter/material.dart';
import '../core/theme/app_theme.dart';

class VoiceButton extends StatefulWidget {
  final String prompt;
  final Function(String) onResult;
  final String tooltip;

  const VoiceButton({
    super.key,
    required this.prompt,
    required this.onResult,
    this.tooltip = 'Koresha ijwi',
  });

  @override
  State<VoiceButton> createState() => _VoiceButtonState();
}

class _VoiceButtonState extends State<VoiceButton>
    with TickerProviderStateMixin {
  bool _isListening = false;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  void _startListening() {
    setState(() {
      _isListening = true;
    });
    _pulseController.repeat(reverse: true);

    // Simulate voice recognition
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _stopListening();
        widget.onResult('komeza'); // Simulate voice command
      }
    });
  }

  void _stopListening() {
    setState(() {
      _isListening = false;
    });
    _pulseController.stop();
    _pulseController.reset();
  }

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: widget.tooltip,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _isListening ? _pulseAnimation.value : 1.0,
            child: Container(
              decoration: BoxDecoration(
                gradient:
                    _isListening
                        ? LinearGradient(
                          colors: [
                            AppTheme.errorColor,
                            AppTheme.errorColor.withValues(alpha: 0.8),
                          ],
                        )
                        : AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(28),
                boxShadow: [
                  BoxShadow(
                    color: (_isListening
                            ? AppTheme.errorColor
                            : AppTheme.primaryColor)
                        .withValues(alpha: 0.4),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: FloatingActionButton(
                onPressed: _isListening ? _stopListening : _startListening,
                backgroundColor: Colors.transparent,
                elevation: 0,
                child: Icon(
                  _isListening ? Icons.stop_rounded : Icons.mic_rounded,
                  color: Colors.white,
                  size: 28,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
