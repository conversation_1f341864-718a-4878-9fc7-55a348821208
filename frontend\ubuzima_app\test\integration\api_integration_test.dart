import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

void main() {
  group('API Integration Tests', () {
    const baseUrl = 'http://localhost:8080/api/v1';
    late String authToken;

    setUpAll(() async {
      // Setup test environment
      print('Setting up API integration tests...');
    });

    tearDownAll(() async {
      // Cleanup test environment
      print('Cleaning up API integration tests...');
    });

    group('Authentication API', () {
      test('should register new user', () async {
        // Arrange
        final userData = {
          'name': 'Test User',
          'email': 'test${DateTime.now().millisecondsSinceEpoch}@example.com',
          'password': 'password123',
          'role': 'CLIENT',
          'phone': '+250788123456',
        };

        // Act
        final response = await http.post(
          Uri.parse('$baseUrl/auth/register'),
          headers: {'Content-Type': 'application/json'},
          body: json.encode(userData),
        );

        // Assert
        expect(response.statusCode, equals(201));
        final responseData = json.decode(response.body);
        expect(responseData['success'], isTrue);
        expect(responseData['data']['user']['email'], equals(userData['email']));
      });

      test('should login with valid credentials', () async {
        // Arrange
        final loginData = {
          'email': '<EMAIL>',
          'password': 'password123',
        };

        // Act
        final response = await http.post(
          Uri.parse('$baseUrl/auth/login'),
          headers: {'Content-Type': 'application/json'},
          body: json.encode(loginData),
        );

        // Assert
        expect(response.statusCode, equals(200));
        final responseData = json.decode(response.body);
        expect(responseData['success'], isTrue);
        expect(responseData['data']['token'], isNotNull);
        
        // Store token for subsequent tests
        authToken = responseData['data']['token'];
      });

      test('should fail login with invalid credentials', () async {
        // Arrange
        final loginData = {
          'email': '<EMAIL>',
          'password': 'wrongpassword',
        };

        // Act
        final response = await http.post(
          Uri.parse('$baseUrl/auth/login'),
          headers: {'Content-Type': 'application/json'},
          body: json.encode(loginData),
        );

        // Assert
        expect(response.statusCode, equals(401));
        final responseData = json.decode(response.body);
        expect(responseData['success'], isFalse);
      });
    });

    group('User API', () {
      test('should get current user profile', () async {
        // Act
        final response = await http.get(
          Uri.parse('$baseUrl/users/profile'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $authToken',
          },
        );

        // Assert
        expect(response.statusCode, equals(200));
        final responseData = json.decode(response.body);
        expect(responseData['success'], isTrue);
        expect(responseData['data']['user'], isNotNull);
      });

      test('should update user profile', () async {
        // Arrange
        final updateData = {
          'name': 'Updated Test User',
          'phone': '+250788654321',
        };

        // Act
        final response = await http.put(
          Uri.parse('$baseUrl/users/profile'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $authToken',
          },
          body: json.encode(updateData),
        );

        // Assert
        expect(response.statusCode, equals(200));
        final responseData = json.decode(response.body);
        expect(responseData['success'], isTrue);
        expect(responseData['data']['user']['name'], equals(updateData['name']));
      });
    });

    group('Health Records API', () {
      test('should create health record', () async {
        // Arrange
        final healthData = {
          'type': 'WEIGHT',
          'value': 70.5,
          'unit': 'kg',
          'notes': 'Regular checkup',
          'recordedAt': DateTime.now().toIso8601String(),
        };

        // Act
        final response = await http.post(
          Uri.parse('$baseUrl/health-records'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $authToken',
          },
          body: json.encode(healthData),
        );

        // Assert
        expect(response.statusCode, equals(201));
        final responseData = json.decode(response.body);
        expect(responseData['success'], isTrue);
        expect(responseData['data']['healthRecord']['type'], equals('WEIGHT'));
      });

      test('should get user health records', () async {
        // Act
        final response = await http.get(
          Uri.parse('$baseUrl/health-records'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $authToken',
          },
        );

        // Assert
        expect(response.statusCode, equals(200));
        final responseData = json.decode(response.body);
        expect(responseData['success'], isTrue);
        expect(responseData['data']['healthRecords'], isList);
      });

      test('should filter health records by type', () async {
        // Act
        final response = await http.get(
          Uri.parse('$baseUrl/health-records?type=WEIGHT'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $authToken',
          },
        );

        // Assert
        expect(response.statusCode, equals(200));
        final responseData = json.decode(response.body);
        expect(responseData['success'], isTrue);
        
        final records = responseData['data']['healthRecords'] as List;
        for (final record in records) {
          expect(record['type'], equals('WEIGHT'));
        }
      });
    });

    group('Appointments API', () {
      test('should create appointment', () async {
        // Arrange
        final appointmentData = {
          'type': 'CONSULTATION',
          'scheduledAt': DateTime.now().add(Duration(days: 1)).toIso8601String(),
          'notes': 'Regular checkup appointment',
          'facilityId': 'facility-1',
        };

        // Act
        final response = await http.post(
          Uri.parse('$baseUrl/appointments'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $authToken',
          },
          body: json.encode(appointmentData),
        );

        // Assert
        expect(response.statusCode, equals(201));
        final responseData = json.decode(response.body);
        expect(responseData['success'], isTrue);
        expect(responseData['data']['appointment']['type'], equals('CONSULTATION'));
      });

      test('should get user appointments', () async {
        // Act
        final response = await http.get(
          Uri.parse('$baseUrl/appointments'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $authToken',
          },
        );

        // Assert
        expect(response.statusCode, equals(200));
        final responseData = json.decode(response.body);
        expect(responseData['success'], isTrue);
        expect(responseData['data']['appointments'], isList);
      });
    });

    group('Error Handling', () {
      test('should return 401 for unauthorized requests', () async {
        // Act
        final response = await http.get(
          Uri.parse('$baseUrl/users/profile'),
          headers: {'Content-Type': 'application/json'},
        );

        // Assert
        expect(response.statusCode, equals(401));
      });

      test('should return 404 for non-existent endpoints', () async {
        // Act
        final response = await http.get(
          Uri.parse('$baseUrl/non-existent-endpoint'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $authToken',
          },
        );

        // Assert
        expect(response.statusCode, equals(404));
      });

      test('should handle malformed JSON', () async {
        // Act
        final response = await http.post(
          Uri.parse('$baseUrl/health-records'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $authToken',
          },
          body: 'invalid json',
        );

        // Assert
        expect(response.statusCode, equals(400));
      });
    });
  });
}
