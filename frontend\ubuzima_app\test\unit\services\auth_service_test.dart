import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ubuzima_app/core/services/auth_service.dart';
import 'package:ubuzima_app/core/models/user_model.dart';

import 'auth_service_test.mocks.dart';

@GenerateMocks([SharedPreferences])
void main() {
  group('AuthService Tests', () {
    late AuthService authService;
    late MockSharedPreferences mockPrefs;

    setUp(() {
      mockPrefs = MockSharedPreferences();
      authService = AuthService();
    });

    group('Authentication', () {
      test('should login successfully with valid credentials', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        
        when(mockPrefs.setString(any, any)).thenAnswer((_) async => true);
        when(mockPrefs.setBool(any, any)).thenAnswer((_) async => true);

        // Act
        final result = await authService.login(email, password);

        // Assert
        expect(result, isTrue);
      });

      test('should fail login with invalid credentials', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'wrongpassword';

        // Act
        final result = await authService.login(email, password);

        // Assert
        expect(result, isFalse);
      });

      test('should register new user successfully', () async {
        // Arrange
        final user = User(
          id: 'test-id',
          name: 'Test User',
          email: '<EMAIL>',
          role: UserRole.client,
          createdAt: DateTime.now(),
          isActive: true,
        );

        when(mockPrefs.setString(any, any)).thenAnswer((_) async => true);
        when(mockPrefs.setBool(any, any)).thenAnswer((_) async => true);

        // Act
        final result = await authService.register(user, 'password123');

        // Assert
        expect(result, isTrue);
      });

      test('should logout successfully', () async {
        // Arrange
        when(mockPrefs.remove(any)).thenAnswer((_) async => true);

        // Act
        await authService.logout();

        // Assert
        verify(mockPrefs.remove('auth_token')).called(1);
        verify(mockPrefs.remove('user_data')).called(1);
      });
    });

    group('User Session', () {
      test('should check if user is logged in', () async {
        // Arrange
        when(mockPrefs.getString('auth_token')).thenReturn('valid_token');

        // Act
        final isLoggedIn = await authService.isLoggedIn();

        // Assert
        expect(isLoggedIn, isTrue);
      });

      test('should return false when no token exists', () async {
        // Arrange
        when(mockPrefs.getString('auth_token')).thenReturn(null);

        // Act
        final isLoggedIn = await authService.isLoggedIn();

        // Assert
        expect(isLoggedIn, isFalse);
      });

      test('should get current user when logged in', () async {
        // Arrange
        const userData = '''
        {
          "id": "test-id",
          "name": "Test User",
          "email": "<EMAIL>",
          "role": "client",
          "createdAt": "2024-01-01T00:00:00.000Z",
          "isActive": true
        }
        ''';
        
        when(mockPrefs.getString('user_data')).thenReturn(userData);

        // Act
        final user = await authService.getCurrentUser();

        // Assert
        expect(user, isNotNull);
        expect(user?.name, equals('Test User'));
        expect(user?.email, equals('<EMAIL>'));
      });
    });

    group('Token Management', () {
      test('should validate token format', () {
        // Arrange
        const validToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.signature';
        const invalidToken = 'invalid_token';

        // Act & Assert
        expect(authService.isValidTokenFormat(validToken), isTrue);
        expect(authService.isValidTokenFormat(invalidToken), isFalse);
      });

      test('should refresh token when expired', () async {
        // Arrange
        const oldToken = 'expired_token';
        const newToken = 'new_valid_token';
        
        when(mockPrefs.getString('auth_token')).thenReturn(oldToken);
        when(mockPrefs.setString('auth_token', newToken)).thenAnswer((_) async => true);

        // Act
        final result = await authService.refreshToken();

        // Assert
        expect(result, isTrue);
      });
    });

    group('Error Handling', () {
      test('should handle network errors gracefully', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';

        // Simulate network error
        when(mockPrefs.setString(any, any)).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(() => authService.login(email, password), throwsException);
      });

      test('should handle storage errors gracefully', () async {
        // Arrange
        when(mockPrefs.getString(any)).thenThrow(Exception('Storage error'));

        // Act
        final isLoggedIn = await authService.isLoggedIn();

        // Assert
        expect(isLoggedIn, isFalse);
      });
    });
  });
}
