import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:ubuzima_app/core/services/database_service.dart';
import 'package:ubuzima_app/core/models/health_record_model.dart';

import 'health_service_test.mocks.dart';

@GenerateMocks([DatabaseService])
void main() {
  group('Health Service Tests', () {
    late MockDatabaseService mockDatabaseService;

    setUp(() {
      mockDatabaseService = MockDatabaseService();
    });

    group('Health Records', () {
      test('should save health record successfully', () async {
        // Arrange
        final healthRecord = HealthRecord(
          id: 'test-id',
          userId: 'user-id',
          type: HealthRecordType.weight,
          value: 70.5,
          unit: 'kg',
          recordedAt: DateTime.now(),
          notes: 'Regular checkup',
        );

        when(mockDatabaseService.insert('health_records', any))
            .thenAnswer((_) async => 1);

        // Act
        final result = await mockDatabaseService.insert('health_records', healthRecord.toMap());

        // Assert
        expect(result, equals(1));
        verify(mockDatabaseService.insert('health_records', any)).called(1);
      });

      test('should retrieve health records by user', () async {
        // Arrange
        const userId = 'user-id';
        final mockRecords = [
          {
            'id': 'record-1',
            'user_id': userId,
            'type': 'weight',
            'value': 70.5,
            'unit': 'kg',
            'recorded_at': DateTime.now().toIso8601String(),
            'notes': 'Regular checkup',
          },
          {
            'id': 'record-2',
            'user_id': userId,
            'type': 'blood_pressure',
            'value': 120.0,
            'unit': 'mmHg',
            'recorded_at': DateTime.now().toIso8601String(),
            'notes': 'Normal reading',
          },
        ];

        when(mockDatabaseService.query(
          'health_records',
          where: 'user_id = ?',
          whereArgs: [userId],
          orderBy: 'recorded_at DESC',
        )).thenAnswer((_) async => mockRecords);

        // Act
        final records = await mockDatabaseService.query(
          'health_records',
          where: 'user_id = ?',
          whereArgs: [userId],
          orderBy: 'recorded_at DESC',
        );

        // Assert
        expect(records, hasLength(2));
        expect(records[0]['type'], equals('weight'));
        expect(records[1]['type'], equals('blood_pressure'));
      });

      test('should update health record', () async {
        // Arrange
        const recordId = 'record-1';
        final updatedData = {
          'value': 72.0,
          'notes': 'Updated weight',
          'updated_at': DateTime.now().toIso8601String(),
        };

        when(mockDatabaseService.update(
          'health_records',
          updatedData,
          'id = ?',
          [recordId],
        )).thenAnswer((_) async => 1);

        // Act
        final result = await mockDatabaseService.update(
          'health_records',
          updatedData,
          'id = ?',
          [recordId],
        );

        // Assert
        expect(result, equals(1));
      });

      test('should delete health record', () async {
        // Arrange
        const recordId = 'record-1';

        when(mockDatabaseService.delete(
          'health_records',
          'id = ?',
          [recordId],
        )).thenAnswer((_) async => 1);

        // Act
        final result = await mockDatabaseService.delete(
          'health_records',
          'id = ?',
          [recordId],
        );

        // Assert
        expect(result, equals(1));
      });
    });

    group('Health Statistics', () {
      test('should calculate BMI correctly', () {
        // Arrange
        const weight = 70.0; // kg
        const height = 1.75; // meters

        // Act
        final bmi = weight / (height * height);

        // Assert
        expect(bmi, closeTo(22.86, 0.01));
      });

      test('should categorize BMI correctly', () {
        // Test cases for BMI categories
        expect(_getBMICategory(16.0), equals('Underweight'));
        expect(_getBMICategory(22.0), equals('Normal'));
        expect(_getBMICategory(27.0), equals('Overweight'));
        expect(_getBMICategory(32.0), equals('Obese'));
      });

      test('should calculate average values', () async {
        // Arrange
        final mockRecords = [
          {'value': 68.0},
          {'value': 70.0},
          {'value': 72.0},
        ];

        when(mockDatabaseService.query(
          'health_records',
          where: 'user_id = ? AND type = ?',
          whereArgs: ['user-id', 'weight'],
        )).thenAnswer((_) async => mockRecords);

        // Act
        final records = await mockDatabaseService.query(
          'health_records',
          where: 'user_id = ? AND type = ?',
          whereArgs: ['user-id', 'weight'],
        );

        final average = records
            .map((r) => r['value'] as double)
            .reduce((a, b) => a + b) / records.length;

        // Assert
        expect(average, equals(70.0));
      });
    });

    group('Data Validation', () {
      test('should validate weight values', () {
        expect(_isValidWeight(70.0), isTrue);
        expect(_isValidWeight(-5.0), isFalse);
        expect(_isValidWeight(500.0), isFalse);
      });

      test('should validate blood pressure values', () {
        expect(_isValidBloodPressure(120, 80), isTrue);
        expect(_isValidBloodPressure(50, 30), isFalse);
        expect(_isValidBloodPressure(300, 200), isFalse);
      });

      test('should validate temperature values', () {
        expect(_isValidTemperature(36.5), isTrue);
        expect(_isValidTemperature(25.0), isFalse);
        expect(_isValidTemperature(45.0), isFalse);
      });
    });
  });
}

// Helper functions for testing
String _getBMICategory(double bmi) {
  if (bmi < 18.5) return 'Underweight';
  if (bmi < 25.0) return 'Normal';
  if (bmi < 30.0) return 'Overweight';
  return 'Obese';
}

bool _isValidWeight(double weight) {
  return weight > 0 && weight < 300;
}

bool _isValidBloodPressure(int systolic, int diastolic) {
  return systolic > 60 && systolic < 250 && 
         diastolic > 40 && diastolic < 150;
}

bool _isValidTemperature(double temperature) {
  return temperature > 30.0 && temperature < 43.0;
}
