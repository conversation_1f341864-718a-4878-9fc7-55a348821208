@echo off
REM Ubuzima App Deployment Script for Windows
REM Usage: scripts\deploy.bat [environment]
REM Example: scripts\deploy.bat production

setlocal enabledelayedexpansion

set ENVIRONMENT=%1
if "%ENVIRONMENT%"=="" set ENVIRONMENT=development

echo 🚀 Starting Ubuzima deployment for environment: %ENVIRONMENT%

REM Get project root directory
for %%i in ("%~dp0..") do set "PROJECT_ROOT=%%~fi"

REM Check if environment file exists
set ENV_FILE=%PROJECT_ROOT%\.env.%ENVIRONMENT%
if not exist "%ENV_FILE%" (
    echo [ERROR] Environment file %ENV_FILE% not found!
    exit /b 1
)

echo [INFO] Using environment file: %ENV_FILE%

REM Step 1: Validate prerequisites
echo [INFO] Validating prerequisites...

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not installed. Please install Docker first.
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose is not installed. Please install Docker Compose first.
    exit /b 1
)

echo [SUCCESS] Prerequisites validated

REM Step 2: Build frontend
echo [INFO] Building Flutter frontend...
cd /d "%PROJECT_ROOT%\frontend\ubuzima_app"

REM Clean previous builds
call flutter clean
call flutter pub get

REM Build for web
if "%ENVIRONMENT%"=="production" (
    call flutter build web --release --web-renderer html
) else (
    call flutter build web --web-renderer html
)

if errorlevel 1 (
    echo [ERROR] Frontend build failed
    exit /b 1
)

echo [SUCCESS] Frontend built successfully

REM Step 3: Build backend
echo [INFO] Building Spring Boot backend...
cd /d "%PROJECT_ROOT%\backend"

REM Build with Maven
if "%ENVIRONMENT%"=="production" (
    call mvnw.cmd clean package -DskipTests -Pprod
) else (
    call mvnw.cmd clean package -DskipTests
)

if errorlevel 1 (
    echo [ERROR] Backend build failed
    exit /b 1
)

echo [SUCCESS] Backend built successfully

REM Step 4: Build Docker images
echo [INFO] Building Docker images...
cd /d "%PROJECT_ROOT%"

REM Build frontend image
docker build -t ubuzima-frontend:%ENVIRONMENT% .\frontend\ubuzima_app
if errorlevel 1 (
    echo [ERROR] Frontend Docker image build failed
    exit /b 1
)

REM Build backend image
docker build -t ubuzima-backend:%ENVIRONMENT% .\backend
if errorlevel 1 (
    echo [ERROR] Backend Docker image build failed
    exit /b 1
)

echo [SUCCESS] Docker images built successfully

REM Step 5: Deploy with Docker Compose
echo [INFO] Deploying with Docker Compose...

REM Set compose file based on environment
if "%ENVIRONMENT%"=="production" (
    set COMPOSE_FILE=docker-compose.prod.yml
) else (
    set COMPOSE_FILE=docker-compose.yml
)

REM Stop existing containers
docker-compose -f %COMPOSE_FILE% down

REM Start new containers
docker-compose -f %COMPOSE_FILE% up -d

if errorlevel 1 (
    echo [ERROR] Docker Compose deployment failed
    exit /b 1
)

echo [SUCCESS] Deployment completed successfully

REM Step 6: Health checks
echo [INFO] Running health checks...

REM Wait for services to start
timeout /t 30 /nobreak >nul

REM Check backend health
curl -f http://localhost:8080/api/v1/actuator/health >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Backend health check failed - service may still be starting
) else (
    echo [SUCCESS] Backend health check passed
)

REM Check frontend
curl -f http://localhost >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Frontend health check failed - service may still be starting
) else (
    echo [SUCCESS] Frontend health check passed
)

REM Step 7: Display deployment information
echo [INFO] Deployment Information:
echo Environment: %ENVIRONMENT%
echo Frontend URL: http://localhost
echo Backend API: http://localhost:8080/api/v1
echo Database: PostgreSQL on port 5432

if "%ENVIRONMENT%"=="production" (
    echo Monitoring: http://localhost:3000 (Grafana^)
    echo Logs: docker-compose logs -f
)

echo [SUCCESS] 🎉 Ubuzima deployment completed successfully!
echo [INFO] You can now access the application at http://localhost

REM Step 8: Optional - Run tests
if not "%ENVIRONMENT%"=="production" (
    set /p "REPLY=Do you want to run integration tests? (y/n): "
    if /i "!REPLY!"=="y" (
        echo [INFO] Running integration tests...
        cd /d "%PROJECT_ROOT%\frontend\ubuzima_app"
        call flutter test integration_test\
        echo [SUCCESS] Integration tests completed
    )
)

echo [INFO] Deployment script finished
pause
