#!/bin/bash

# Ubuzima App Deployment Script
# Usage: ./scripts/deploy.sh [environment]
# Example: ./scripts/deploy.sh production

set -e

ENVIRONMENT=${1:-development}
PROJECT_ROOT=$(dirname $(dirname $(realpath $0)))

echo "🚀 Starting Ubuzima deployment for environment: $ENVIRONMENT"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if environment file exists
ENV_FILE="$PROJECT_ROOT/.env.$ENVIRONMENT"
if [ ! -f "$ENV_FILE" ]; then
    print_error "Environment file $ENV_FILE not found!"
    exit 1
fi

print_status "Using environment file: $ENV_FILE"

# Load environment variables
export $(cat $ENV_FILE | grep -v '^#' | xargs)

# Step 1: Validate prerequisites
print_status "Validating prerequisites..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_success "Prerequisites validated"

# Step 2: Build frontend
print_status "Building Flutter frontend..."
cd "$PROJECT_ROOT/frontend/ubuzima_app"

# Clean previous builds
flutter clean
flutter pub get

# Build for web
if [ "$ENVIRONMENT" = "production" ]; then
    flutter build web --release --web-renderer html
else
    flutter build web --web-renderer html
fi

print_success "Frontend built successfully"

# Step 3: Build backend
print_status "Building Spring Boot backend..."
cd "$PROJECT_ROOT/backend"

# Build with Maven
if [ "$ENVIRONMENT" = "production" ]; then
    ./mvnw clean package -DskipTests -Pprod
else
    ./mvnw clean package -DskipTests
fi

print_success "Backend built successfully"

# Step 4: Build Docker images
print_status "Building Docker images..."
cd "$PROJECT_ROOT"

# Build frontend image
docker build -t ubuzima-frontend:$ENVIRONMENT ./frontend/ubuzima_app

# Build backend image
docker build -t ubuzima-backend:$ENVIRONMENT ./backend

print_success "Docker images built successfully"

# Step 5: Deploy with Docker Compose
print_status "Deploying with Docker Compose..."

# Copy environment-specific docker-compose file
if [ "$ENVIRONMENT" = "production" ]; then
    COMPOSE_FILE="docker-compose.prod.yml"
else
    COMPOSE_FILE="docker-compose.yml"
fi

# Stop existing containers
docker-compose -f $COMPOSE_FILE down

# Start new containers
docker-compose -f $COMPOSE_FILE up -d

print_success "Deployment completed successfully"

# Step 6: Health checks
print_status "Running health checks..."

# Wait for services to start
sleep 30

# Check backend health
BACKEND_URL="http://localhost:8080/api/v1/actuator/health"
if curl -f $BACKEND_URL > /dev/null 2>&1; then
    print_success "Backend health check passed"
else
    print_warning "Backend health check failed - service may still be starting"
fi

# Check frontend
FRONTEND_URL="http://localhost"
if curl -f $FRONTEND_URL > /dev/null 2>&1; then
    print_success "Frontend health check passed"
else
    print_warning "Frontend health check failed - service may still be starting"
fi

# Step 7: Display deployment information
print_status "Deployment Information:"
echo "Environment: $ENVIRONMENT"
echo "Frontend URL: http://localhost"
echo "Backend API: http://localhost:8080/api/v1"
echo "Database: PostgreSQL on port 5432"

if [ "$ENVIRONMENT" = "production" ]; then
    echo "Monitoring: http://localhost:3000 (Grafana)"
    echo "Logs: docker-compose logs -f"
fi

print_success "🎉 Ubuzima deployment completed successfully!"
print_status "You can now access the application at http://localhost"

# Step 8: Optional - Run tests
if [ "$ENVIRONMENT" != "production" ]; then
    read -p "Do you want to run integration tests? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Running integration tests..."
        cd "$PROJECT_ROOT/frontend/ubuzima_app"
        flutter test integration_test/
        print_success "Integration tests completed"
    fi
fi

print_status "Deployment script finished"
