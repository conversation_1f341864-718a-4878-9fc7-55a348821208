  Manifest android  
permission android.Manifest  ACCESS_BACKGROUND_LOCATION android.Manifest.permission  ACCESS_FINE_LOCATION android.Manifest.permission  Activity android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  Service android.app  getLET android.app.Activity  getLet android.app.Activity  let android.app.Activity  VISIBILITY_PRIVATE android.app.Notification  Notification android.app.NotificationChannel  apply android.app.NotificationChannel  getAPPLY android.app.NotificationChannel  getApply android.app.NotificationChannel  getLOCKSCREENVisibility android.app.NotificationChannel  getLockscreenVisibility android.app.NotificationChannel  lockscreenVisibility android.app.NotificationChannel  setLockscreenVisibility android.app.NotificationChannel  IMPORTANCE_NONE android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  getActivity android.app.PendingIntent  Activity android.app.Service  ActivityCompat android.app.Service  ActivityNotFoundException android.app.Service  Any android.app.Service  Array android.app.Service  BackgroundNotification android.app.Service  Binder android.app.Service  Boolean android.app.Service  Build android.app.Service  
CHANNEL_ID android.app.Service  FlutterLocation android.app.Service  FlutterLocationService android.app.Service  IBinder android.app.Service  Int android.app.Service  IntArray android.app.Service  Intent android.app.Service  Log android.app.Service  Manifest android.app.Service  Map android.app.Service  
MethodChannel android.app.Service  NotificationOptions android.app.Service  ONGOING_NOTIFICATION_ID android.app.Service  PackageManager android.app.Service  PluginRegistry android.app.Service   REQUEST_PERMISSIONS_REQUEST_CODE android.app.Service  STOP_FOREGROUND_REMOVE android.app.Service  String android.app.Service  Suppress android.app.Service  TAG android.app.Service  arrayOf android.app.Service  enableBackgroundMode android.app.Service  let android.app.Service  mapOf android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  onUnbind android.app.Service  .shouldShowRequestBackgroundPermissionRationale android.app.Service  startForeground android.app.Service  stopForeground android.app.Service  to android.app.Service  ActivityNotFoundException android.content  Context android.content  Intent android.content  Activity android.content.Context  ActivityCompat android.content.Context  ActivityNotFoundException android.content.Context  Any android.content.Context  Array android.content.Context  BackgroundNotification android.content.Context  Binder android.content.Context  Boolean android.content.Context  Build android.content.Context  
CHANNEL_ID android.content.Context  FlutterLocation android.content.Context  FlutterLocationService android.content.Context  IBinder android.content.Context  Int android.content.Context  IntArray android.content.Context  Intent android.content.Context  Log android.content.Context  Manifest android.content.Context  Map android.content.Context  
MethodChannel android.content.Context  NotificationOptions android.content.Context  ONGOING_NOTIFICATION_ID android.content.Context  PackageManager android.content.Context  PluginRegistry android.content.Context   REQUEST_PERMISSIONS_REQUEST_CODE android.content.Context  STOP_FOREGROUND_REMOVE android.content.Context  String android.content.Context  Suppress android.content.Context  TAG android.content.Context  arrayOf android.content.Context  enableBackgroundMode android.content.Context  getPACKAGEManager android.content.Context  getPACKAGEName android.content.Context  getPackageManager android.content.Context  getPackageName android.content.Context  getRESOURCES android.content.Context  getResources android.content.Context  let android.content.Context  mapOf android.content.Context  onCreate android.content.Context  	onDestroy android.content.Context  onUnbind android.content.Context  packageManager android.content.Context  packageName android.content.Context  	resources android.content.Context  setPackageManager android.content.Context  setPackageName android.content.Context  setResources android.content.Context  .shouldShowRequestBackgroundPermissionRationale android.content.Context  startForeground android.content.Context  stopForeground android.content.Context  to android.content.Context  Activity android.content.ContextWrapper  ActivityCompat android.content.ContextWrapper  ActivityNotFoundException android.content.ContextWrapper  Any android.content.ContextWrapper  Array android.content.ContextWrapper  BackgroundNotification android.content.ContextWrapper  Binder android.content.ContextWrapper  Boolean android.content.ContextWrapper  Build android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  FlutterLocation android.content.ContextWrapper  FlutterLocationService android.content.ContextWrapper  IBinder android.content.ContextWrapper  Int android.content.ContextWrapper  IntArray android.content.ContextWrapper  Intent android.content.ContextWrapper  Log android.content.ContextWrapper  Manifest android.content.ContextWrapper  Map android.content.ContextWrapper  
MethodChannel android.content.ContextWrapper  NotificationOptions android.content.ContextWrapper  ONGOING_NOTIFICATION_ID android.content.ContextWrapper  PackageManager android.content.ContextWrapper  PluginRegistry android.content.ContextWrapper   REQUEST_PERMISSIONS_REQUEST_CODE android.content.ContextWrapper  STOP_FOREGROUND_REMOVE android.content.ContextWrapper  String android.content.ContextWrapper  Suppress android.content.ContextWrapper  TAG android.content.ContextWrapper  arrayOf android.content.ContextWrapper  enableBackgroundMode android.content.ContextWrapper  let android.content.ContextWrapper  mapOf android.content.ContextWrapper  onCreate android.content.ContextWrapper  	onDestroy android.content.ContextWrapper  onUnbind android.content.ContextWrapper  .shouldShowRequestBackgroundPermissionRationale android.content.ContextWrapper  startForeground android.content.ContextWrapper  stopForeground android.content.ContextWrapper  to android.content.ContextWrapper  FLAG_ACTIVITY_NEW_TASK android.content.Intent  "FLAG_ACTIVITY_RESET_TASK_IF_NEEDED android.content.Intent  equals android.content.Intent  setFlags android.content.Intent  
setPackage android.content.Intent  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  getLaunchIntentForPackage !android.content.pm.PackageManager  
getIdentifier android.content.res.Resources  Binder 
android.os  Build 
android.os  IBinder 
android.os  FlutterLocationService android.os.Binder  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  N android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  Log android.util  d android.util.Log  let  android.view.ContextThemeWrapper  ActivityCompat androidx.core.app  NotificationCompat androidx.core.app  NotificationManagerCompat androidx.core.app  checkSelfPermission  androidx.core.app.ActivityCompat  requestPermissions  androidx.core.app.ActivityCompat  $shouldShowRequestPermissionRationale  androidx.core.app.ActivityCompat  Builder $androidx.core.app.NotificationCompat  
PRIORITY_HIGH $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  setColor ,androidx.core.app.NotificationCompat.Builder  setColorized ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  
setSubText ,androidx.core.app.NotificationCompat.Builder  createNotificationChannel +androidx.core.app.NotificationManagerCompat  from +androidx.core.app.NotificationManagerCompat  notify +androidx.core.app.NotificationManagerCompat  ActivityCompat com.lyokone.location  ActivityNotFoundException com.lyokone.location  Any com.lyokone.location  Array com.lyokone.location  BackgroundNotification com.lyokone.location  Boolean com.lyokone.location  Build com.lyokone.location  
CHANNEL_ID com.lyokone.location  FlutterLocation com.lyokone.location  FlutterLocationService com.lyokone.location  Int com.lyokone.location  IntArray com.lyokone.location  Intent com.lyokone.location  Log com.lyokone.location  Manifest com.lyokone.location  Map com.lyokone.location  Notification com.lyokone.location  NotificationChannel com.lyokone.location  NotificationCompat com.lyokone.location  NotificationManager com.lyokone.location  NotificationManagerCompat com.lyokone.location  NotificationOptions com.lyokone.location  ONGOING_NOTIFICATION_ID com.lyokone.location  PackageManager com.lyokone.location  
PendingIntent com.lyokone.location   REQUEST_PERMISSIONS_REQUEST_CODE com.lyokone.location  STOP_FOREGROUND_REMOVE com.lyokone.location  String com.lyokone.location  Suppress com.lyokone.location  TAG com.lyokone.location  apply com.lyokone.location  arrayOf com.lyokone.location  kDefaultChannelName com.lyokone.location  kDefaultNotificationIconName com.lyokone.location  kDefaultNotificationTitle com.lyokone.location  let com.lyokone.location  mapOf com.lyokone.location  to com.lyokone.location  Boolean +com.lyokone.location.BackgroundNotification  Build +com.lyokone.location.BackgroundNotification  Context +com.lyokone.location.BackgroundNotification  Int +com.lyokone.location.BackgroundNotification  Intent +com.lyokone.location.BackgroundNotification  Notification +com.lyokone.location.BackgroundNotification  NotificationChannel +com.lyokone.location.BackgroundNotification  NotificationCompat +com.lyokone.location.BackgroundNotification  NotificationManager +com.lyokone.location.BackgroundNotification  NotificationManagerCompat +com.lyokone.location.BackgroundNotification  NotificationOptions +com.lyokone.location.BackgroundNotification  
PendingIntent +com.lyokone.location.BackgroundNotification  String +com.lyokone.location.BackgroundNotification  apply +com.lyokone.location.BackgroundNotification  build +com.lyokone.location.BackgroundNotification  buildBringToFrontIntent +com.lyokone.location.BackgroundNotification  builder +com.lyokone.location.BackgroundNotification  	channelId +com.lyokone.location.BackgroundNotification  context +com.lyokone.location.BackgroundNotification  getAPPLY +com.lyokone.location.BackgroundNotification  getApply +com.lyokone.location.BackgroundNotification  
getDrawableId +com.lyokone.location.BackgroundNotification  getKDefaultNotificationIconName +com.lyokone.location.BackgroundNotification  getLET +com.lyokone.location.BackgroundNotification  getLet +com.lyokone.location.BackgroundNotification  kDefaultNotificationIconName +com.lyokone.location.BackgroundNotification  let +com.lyokone.location.BackgroundNotification  notificationId +com.lyokone.location.BackgroundNotification  options +com.lyokone.location.BackgroundNotification  
updateChannel +com.lyokone.location.BackgroundNotification  updateNotification +com.lyokone.location.BackgroundNotification  
updateOptions +com.lyokone.location.BackgroundNotification  checkPermissions $com.lyokone.location.FlutterLocation  requestPermissions $com.lyokone.location.FlutterLocation  result $com.lyokone.location.FlutterLocation  setActivity $com.lyokone.location.FlutterLocation  Activity +com.lyokone.location.FlutterLocationService  ActivityCompat +com.lyokone.location.FlutterLocationService  ActivityNotFoundException +com.lyokone.location.FlutterLocationService  Any +com.lyokone.location.FlutterLocationService  Array +com.lyokone.location.FlutterLocationService  BackgroundNotification +com.lyokone.location.FlutterLocationService  Binder +com.lyokone.location.FlutterLocationService  Boolean +com.lyokone.location.FlutterLocationService  Build +com.lyokone.location.FlutterLocationService  
CHANNEL_ID +com.lyokone.location.FlutterLocationService  FlutterLocation +com.lyokone.location.FlutterLocationService  FlutterLocationService +com.lyokone.location.FlutterLocationService  IBinder +com.lyokone.location.FlutterLocationService  Int +com.lyokone.location.FlutterLocationService  IntArray +com.lyokone.location.FlutterLocationService  Intent +com.lyokone.location.FlutterLocationService  LocalBinder +com.lyokone.location.FlutterLocationService  Log +com.lyokone.location.FlutterLocationService  Manifest +com.lyokone.location.FlutterLocationService  Map +com.lyokone.location.FlutterLocationService  
MethodChannel +com.lyokone.location.FlutterLocationService  NotificationOptions +com.lyokone.location.FlutterLocationService  ONGOING_NOTIFICATION_ID +com.lyokone.location.FlutterLocationService  PackageManager +com.lyokone.location.FlutterLocationService  PluginRegistry +com.lyokone.location.FlutterLocationService   REQUEST_PERMISSIONS_REQUEST_CODE +com.lyokone.location.FlutterLocationService  STOP_FOREGROUND_REMOVE +com.lyokone.location.FlutterLocationService  String +com.lyokone.location.FlutterLocationService  Suppress +com.lyokone.location.FlutterLocationService  TAG +com.lyokone.location.FlutterLocationService  activity +com.lyokone.location.FlutterLocationService  applicationContext +com.lyokone.location.FlutterLocationService  arrayOf +com.lyokone.location.FlutterLocationService  backgroundNotification +com.lyokone.location.FlutterLocationService  binder +com.lyokone.location.FlutterLocationService  enableBackgroundMode +com.lyokone.location.FlutterLocationService  getAPPLICATIONContext +com.lyokone.location.FlutterLocationService  
getARRAYOf +com.lyokone.location.FlutterLocationService  getApplicationContext +com.lyokone.location.FlutterLocationService  
getArrayOf +com.lyokone.location.FlutterLocationService  getLET +com.lyokone.location.FlutterLocationService  getLet +com.lyokone.location.FlutterLocationService  getMAPOf +com.lyokone.location.FlutterLocationService  getMapOf +com.lyokone.location.FlutterLocationService  getTO +com.lyokone.location.FlutterLocationService  getTo +com.lyokone.location.FlutterLocationService  isForeground +com.lyokone.location.FlutterLocationService  let +com.lyokone.location.FlutterLocationService  location +com.lyokone.location.FlutterLocationService  mapOf +com.lyokone.location.FlutterLocationService  result +com.lyokone.location.FlutterLocationService  setApplicationContext +com.lyokone.location.FlutterLocationService  .shouldShowRequestBackgroundPermissionRationale +com.lyokone.location.FlutterLocationService  startForeground +com.lyokone.location.FlutterLocationService  stopForeground +com.lyokone.location.FlutterLocationService  to +com.lyokone.location.FlutterLocationService  Activity 5com.lyokone.location.FlutterLocationService.Companion  ActivityCompat 5com.lyokone.location.FlutterLocationService.Companion  ActivityNotFoundException 5com.lyokone.location.FlutterLocationService.Companion  Any 5com.lyokone.location.FlutterLocationService.Companion  Array 5com.lyokone.location.FlutterLocationService.Companion  BackgroundNotification 5com.lyokone.location.FlutterLocationService.Companion  Binder 5com.lyokone.location.FlutterLocationService.Companion  Boolean 5com.lyokone.location.FlutterLocationService.Companion  Build 5com.lyokone.location.FlutterLocationService.Companion  
CHANNEL_ID 5com.lyokone.location.FlutterLocationService.Companion  FlutterLocation 5com.lyokone.location.FlutterLocationService.Companion  FlutterLocationService 5com.lyokone.location.FlutterLocationService.Companion  IBinder 5com.lyokone.location.FlutterLocationService.Companion  Int 5com.lyokone.location.FlutterLocationService.Companion  IntArray 5com.lyokone.location.FlutterLocationService.Companion  Intent 5com.lyokone.location.FlutterLocationService.Companion  Log 5com.lyokone.location.FlutterLocationService.Companion  Manifest 5com.lyokone.location.FlutterLocationService.Companion  Map 5com.lyokone.location.FlutterLocationService.Companion  
MethodChannel 5com.lyokone.location.FlutterLocationService.Companion  NotificationOptions 5com.lyokone.location.FlutterLocationService.Companion  ONGOING_NOTIFICATION_ID 5com.lyokone.location.FlutterLocationService.Companion  PackageManager 5com.lyokone.location.FlutterLocationService.Companion  PluginRegistry 5com.lyokone.location.FlutterLocationService.Companion   REQUEST_PERMISSIONS_REQUEST_CODE 5com.lyokone.location.FlutterLocationService.Companion  STOP_FOREGROUND_REMOVE 5com.lyokone.location.FlutterLocationService.Companion  String 5com.lyokone.location.FlutterLocationService.Companion  Suppress 5com.lyokone.location.FlutterLocationService.Companion  TAG 5com.lyokone.location.FlutterLocationService.Companion  arrayOf 5com.lyokone.location.FlutterLocationService.Companion  
getARRAYOf 5com.lyokone.location.FlutterLocationService.Companion  
getArrayOf 5com.lyokone.location.FlutterLocationService.Companion  getLET 5com.lyokone.location.FlutterLocationService.Companion  getLet 5com.lyokone.location.FlutterLocationService.Companion  getMAPOf 5com.lyokone.location.FlutterLocationService.Companion  getMapOf 5com.lyokone.location.FlutterLocationService.Companion  getTO 5com.lyokone.location.FlutterLocationService.Companion  getTo 5com.lyokone.location.FlutterLocationService.Companion  let 5com.lyokone.location.FlutterLocationService.Companion  mapOf 5com.lyokone.location.FlutterLocationService.Companion  to 5com.lyokone.location.FlutterLocationService.Companion  FlutterLocationService 7com.lyokone.location.FlutterLocationService.LocalBinder  Boolean (com.lyokone.location.NotificationOptions  Int (com.lyokone.location.NotificationOptions  String (com.lyokone.location.NotificationOptions  channelName (com.lyokone.location.NotificationOptions  color (com.lyokone.location.NotificationOptions  description (com.lyokone.location.NotificationOptions  iconName (com.lyokone.location.NotificationOptions  kDefaultChannelName (com.lyokone.location.NotificationOptions  kDefaultNotificationIconName (com.lyokone.location.NotificationOptions  kDefaultNotificationTitle (com.lyokone.location.NotificationOptions  onTapBringToFront (com.lyokone.location.NotificationOptions  subtitle (com.lyokone.location.NotificationOptions  title (com.lyokone.location.NotificationOptions  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  Result &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  ActivityResultListener 'io.flutter.plugin.common.PluginRegistry   RequestPermissionsResultListener 'io.flutter.plugin.common.PluginRegistry  ActivityCompat 	java.lang  ActivityNotFoundException 	java.lang  BackgroundNotification 	java.lang  Build 	java.lang  
CHANNEL_ID 	java.lang  FlutterLocation 	java.lang  Intent 	java.lang  Log 	java.lang  Manifest 	java.lang  Notification 	java.lang  NotificationChannel 	java.lang  NotificationCompat 	java.lang  NotificationManager 	java.lang  NotificationManagerCompat 	java.lang  NotificationOptions 	java.lang  ONGOING_NOTIFICATION_ID 	java.lang  PackageManager 	java.lang  
PendingIntent 	java.lang   REQUEST_PERMISSIONS_REQUEST_CODE 	java.lang  STOP_FOREGROUND_REMOVE 	java.lang  TAG 	java.lang  apply 	java.lang  arrayOf 	java.lang  kDefaultNotificationIconName 	java.lang  let 	java.lang  mapOf 	java.lang  to 	java.lang  ActivityCompat kotlin  ActivityNotFoundException kotlin  Any kotlin  Array kotlin  BackgroundNotification kotlin  Boolean kotlin  Build kotlin  
CHANNEL_ID kotlin  FlutterLocation kotlin  	Function1 kotlin  Int kotlin  IntArray kotlin  Intent kotlin  Log kotlin  Manifest kotlin  Nothing kotlin  Notification kotlin  NotificationChannel kotlin  NotificationCompat kotlin  NotificationManager kotlin  NotificationManagerCompat kotlin  NotificationOptions kotlin  ONGOING_NOTIFICATION_ID kotlin  PackageManager kotlin  Pair kotlin  
PendingIntent kotlin   REQUEST_PERMISSIONS_REQUEST_CODE kotlin  STOP_FOREGROUND_REMOVE kotlin  String kotlin  Suppress kotlin  TAG kotlin  Unit kotlin  apply kotlin  arrayOf kotlin  kDefaultNotificationIconName kotlin  let kotlin  mapOf kotlin  to kotlin  getLET 
kotlin.Int  getLet 
kotlin.Int  getTO 
kotlin.String  getTo 
kotlin.String  ActivityCompat kotlin.annotation  ActivityNotFoundException kotlin.annotation  BackgroundNotification kotlin.annotation  Build kotlin.annotation  
CHANNEL_ID kotlin.annotation  FlutterLocation kotlin.annotation  Intent kotlin.annotation  Log kotlin.annotation  Manifest kotlin.annotation  Notification kotlin.annotation  NotificationChannel kotlin.annotation  NotificationCompat kotlin.annotation  NotificationManager kotlin.annotation  NotificationManagerCompat kotlin.annotation  NotificationOptions kotlin.annotation  ONGOING_NOTIFICATION_ID kotlin.annotation  PackageManager kotlin.annotation  
PendingIntent kotlin.annotation   REQUEST_PERMISSIONS_REQUEST_CODE kotlin.annotation  STOP_FOREGROUND_REMOVE kotlin.annotation  TAG kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  kDefaultNotificationIconName kotlin.annotation  let kotlin.annotation  mapOf kotlin.annotation  to kotlin.annotation  ActivityCompat kotlin.collections  ActivityNotFoundException kotlin.collections  BackgroundNotification kotlin.collections  Build kotlin.collections  
CHANNEL_ID kotlin.collections  FlutterLocation kotlin.collections  Intent kotlin.collections  Log kotlin.collections  Manifest kotlin.collections  Map kotlin.collections  Notification kotlin.collections  NotificationChannel kotlin.collections  NotificationCompat kotlin.collections  NotificationManager kotlin.collections  NotificationManagerCompat kotlin.collections  NotificationOptions kotlin.collections  ONGOING_NOTIFICATION_ID kotlin.collections  PackageManager kotlin.collections  
PendingIntent kotlin.collections   REQUEST_PERMISSIONS_REQUEST_CODE kotlin.collections  STOP_FOREGROUND_REMOVE kotlin.collections  TAG kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  kDefaultNotificationIconName kotlin.collections  let kotlin.collections  mapOf kotlin.collections  to kotlin.collections  ActivityCompat kotlin.comparisons  ActivityNotFoundException kotlin.comparisons  BackgroundNotification kotlin.comparisons  Build kotlin.comparisons  
CHANNEL_ID kotlin.comparisons  FlutterLocation kotlin.comparisons  Intent kotlin.comparisons  Log kotlin.comparisons  Manifest kotlin.comparisons  Notification kotlin.comparisons  NotificationChannel kotlin.comparisons  NotificationCompat kotlin.comparisons  NotificationManager kotlin.comparisons  NotificationManagerCompat kotlin.comparisons  NotificationOptions kotlin.comparisons  ONGOING_NOTIFICATION_ID kotlin.comparisons  PackageManager kotlin.comparisons  
PendingIntent kotlin.comparisons   REQUEST_PERMISSIONS_REQUEST_CODE kotlin.comparisons  STOP_FOREGROUND_REMOVE kotlin.comparisons  TAG kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  kDefaultNotificationIconName kotlin.comparisons  let kotlin.comparisons  mapOf kotlin.comparisons  to kotlin.comparisons  ActivityCompat 	kotlin.io  ActivityNotFoundException 	kotlin.io  BackgroundNotification 	kotlin.io  Build 	kotlin.io  
CHANNEL_ID 	kotlin.io  FlutterLocation 	kotlin.io  Intent 	kotlin.io  Log 	kotlin.io  Manifest 	kotlin.io  Notification 	kotlin.io  NotificationChannel 	kotlin.io  NotificationCompat 	kotlin.io  NotificationManager 	kotlin.io  NotificationManagerCompat 	kotlin.io  NotificationOptions 	kotlin.io  ONGOING_NOTIFICATION_ID 	kotlin.io  PackageManager 	kotlin.io  
PendingIntent 	kotlin.io   REQUEST_PERMISSIONS_REQUEST_CODE 	kotlin.io  STOP_FOREGROUND_REMOVE 	kotlin.io  TAG 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  kDefaultNotificationIconName 	kotlin.io  let 	kotlin.io  mapOf 	kotlin.io  to 	kotlin.io  ActivityCompat 
kotlin.jvm  ActivityNotFoundException 
kotlin.jvm  BackgroundNotification 
kotlin.jvm  Build 
kotlin.jvm  
CHANNEL_ID 
kotlin.jvm  FlutterLocation 
kotlin.jvm  Intent 
kotlin.jvm  Log 
kotlin.jvm  Manifest 
kotlin.jvm  Notification 
kotlin.jvm  NotificationChannel 
kotlin.jvm  NotificationCompat 
kotlin.jvm  NotificationManager 
kotlin.jvm  NotificationManagerCompat 
kotlin.jvm  NotificationOptions 
kotlin.jvm  ONGOING_NOTIFICATION_ID 
kotlin.jvm  PackageManager 
kotlin.jvm  
PendingIntent 
kotlin.jvm   REQUEST_PERMISSIONS_REQUEST_CODE 
kotlin.jvm  STOP_FOREGROUND_REMOVE 
kotlin.jvm  TAG 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  kDefaultNotificationIconName 
kotlin.jvm  let 
kotlin.jvm  mapOf 
kotlin.jvm  to 
kotlin.jvm  ActivityCompat 
kotlin.ranges  ActivityNotFoundException 
kotlin.ranges  BackgroundNotification 
kotlin.ranges  Build 
kotlin.ranges  
CHANNEL_ID 
kotlin.ranges  FlutterLocation 
kotlin.ranges  Intent 
kotlin.ranges  Log 
kotlin.ranges  Manifest 
kotlin.ranges  Notification 
kotlin.ranges  NotificationChannel 
kotlin.ranges  NotificationCompat 
kotlin.ranges  NotificationManager 
kotlin.ranges  NotificationManagerCompat 
kotlin.ranges  NotificationOptions 
kotlin.ranges  ONGOING_NOTIFICATION_ID 
kotlin.ranges  PackageManager 
kotlin.ranges  
PendingIntent 
kotlin.ranges   REQUEST_PERMISSIONS_REQUEST_CODE 
kotlin.ranges  STOP_FOREGROUND_REMOVE 
kotlin.ranges  TAG 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  kDefaultNotificationIconName 
kotlin.ranges  let 
kotlin.ranges  mapOf 
kotlin.ranges  to 
kotlin.ranges  ActivityCompat kotlin.sequences  ActivityNotFoundException kotlin.sequences  BackgroundNotification kotlin.sequences  Build kotlin.sequences  
CHANNEL_ID kotlin.sequences  FlutterLocation kotlin.sequences  Intent kotlin.sequences  Log kotlin.sequences  Manifest kotlin.sequences  Notification kotlin.sequences  NotificationChannel kotlin.sequences  NotificationCompat kotlin.sequences  NotificationManager kotlin.sequences  NotificationManagerCompat kotlin.sequences  NotificationOptions kotlin.sequences  ONGOING_NOTIFICATION_ID kotlin.sequences  PackageManager kotlin.sequences  
PendingIntent kotlin.sequences   REQUEST_PERMISSIONS_REQUEST_CODE kotlin.sequences  STOP_FOREGROUND_REMOVE kotlin.sequences  TAG kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  kDefaultNotificationIconName kotlin.sequences  let kotlin.sequences  mapOf kotlin.sequences  to kotlin.sequences  ActivityCompat kotlin.text  ActivityNotFoundException kotlin.text  BackgroundNotification kotlin.text  Build kotlin.text  
CHANNEL_ID kotlin.text  FlutterLocation kotlin.text  Intent kotlin.text  Log kotlin.text  Manifest kotlin.text  Notification kotlin.text  NotificationChannel kotlin.text  NotificationCompat kotlin.text  NotificationManager kotlin.text  NotificationManagerCompat kotlin.text  NotificationOptions kotlin.text  ONGOING_NOTIFICATION_ID kotlin.text  PackageManager kotlin.text  
PendingIntent kotlin.text   REQUEST_PERMISSIONS_REQUEST_CODE kotlin.text  STOP_FOREGROUND_REMOVE kotlin.text  TAG kotlin.text  apply kotlin.text  arrayOf kotlin.text  kDefaultNotificationIconName kotlin.text  let kotlin.text  mapOf kotlin.text  to kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            